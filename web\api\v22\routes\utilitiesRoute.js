const { Router } = require('express');
const { validate } = require('express-validation');
const { equipmentValidation } = require('../middlewares/validations');
const UtilitiesController = require('../controllers/UtilitiesController');
const passportConfig = require('../config/passport');
const checkAdmin = require('../middlewares/checkAdmin');

const UtilitiesRoute = {
    get router() {
        const router = Router();
        router.post(
            '/add_Utilities',
            // validate(equipmentValidation.addEquipment, { keyByField: true }, { abortEarly: false }),
            passportConfig.isAuthenticated,
            // checkAdmin.isProjectAdmin,
            UtilitiesController.addUtilities,
        );
        router.get(
            '/Utilities_list/:pageSize/:pageNo',
            // validate(equipmentValidation.equipmentDetail, { keyByField: true }, { abortEarly: false }),
            passportConfig.isAuthenticated,
            UtilitiesController.listUtilities,
        );
        return router;
    },
};
module.exports = UtilitiesRoute;