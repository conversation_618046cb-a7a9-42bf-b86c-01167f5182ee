module.exports = (sequelize, DataTypes) => {
    const EquipmentLog = sequelize.define(
        'EquipmentLog',
        {
            EquipmentName: DataTypes.STRING,
            EquipmentManufacturer: DataTypes.STRING,
            EquipmentModel: DataTypes.STRING,
            fuelType: DataTypes.STRING,
            mobilizedDate: DataTypes.DATE,
            deMobilizedDate: DataTypes.DATE,
            date: DataTypes.DATE,
            value: DataTypes.STRING,
            unit: DataTypes.STRING,
            projectId: DataTypes.INTEGER,
            unitType: DataTypes.STRING,
            unitValue: DataTypes.STRING,
            isDeleted: DataTypes.BOOLEAN,
        },
        {},
    );
    EquipmentLog.createEquipmentLog = async (equipmentData) => {
        const newData = await EquipmentLog.create(equipmentData);
        return newData;
    };
    EquipmentLog.getAll = async (attr, limit, offset, searchCondition, sort, sortColumn) => {
        let equipmentLogs;
        const sortByFieldName = sortColumn || 'id';
        const sortByColumnType = sort || 'DESC';

        equipmentLogs = await EquipmentLog.findAndCountAll({
            where: { ...attr, ...searchCondition },
            limit,
            offset,
            order: [[`${sortByFieldName}`, `${sortByColumnType}`]]
        });

        return equipmentLogs;
    };
    return EquipmentLog;
};