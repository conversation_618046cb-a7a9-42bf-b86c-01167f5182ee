const { Router } = require('express');
const { validate } = require('express-validation');
const { CraneRequestController } = require('../controllers');
const passportConfig = require('../config/passport');
const { craneRequestValidation } = require('../middlewares/validations');
const cacheMiddleware = require('../middlewares/cacheMiddleware');

const accountRoute = {
  get router() {
    const router = Router();
    router.post(
      '/create_crane_request',
      validate(craneRequestValidation.craneRequest, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      cacheMiddleware.invalidateAfterAllCalendarMutation(),
      cacheMiddleware.invalidateAfterCraneNDRListMutation(),
      cacheMiddleware.invalidateAfterCraneSingleNDRMutation(),
      cacheMiddleware.invalidateAfterHistoryMutation(),
      CraneRequestController.createCraneRequest,
    );
    router.post(
      '/edit_crane_request',
      validate(
        craneRequestValidation.editCraneRequest,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      cacheMiddleware.invalidateAfterAllCalendarMutation(),
      cacheMiddleware.invalidateAfterCraneNDRListMutation(),
      cacheMiddleware.invalidateAfterCraneSingleNDRMutation(),
      cacheMiddleware.invalidateAfterHistoryMutation(),
      CraneRequestController.editCraneRequest,
    );
    router.post(
      '/get_crane_request_list/:ProjectId/:pageSize/:pageNo/:void',
      passportConfig.isAuthenticated,
      cacheMiddleware.cacheCraneNDRList(),
      CraneRequestController.getCraneRequestList,
    );
    router.get(
      '/get_last_crane_request_id/:ProjectId/?:ParentCompanyId',
      passportConfig.isAuthenticated,
      CraneRequestController.getLastCraneRequestId,
    );
    router.get(
      '/get_single_crane_request/:CraneRequestId/:ProjectId/?:ParentCompanyId',
      validate(
        craneRequestValidation.getSingleCraneRequest,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      cacheMiddleware.cacheCraneSingleNDR(),
      CraneRequestController.getSingleCraneRequest,
    );
    router.post(
      '/update_crane_request_status',
      validate(craneRequestValidation.updateNDRStatus, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      cacheMiddleware.invalidateAfterAllCalendarMutation(),
      cacheMiddleware.invalidateAfterCraneNDRListMutation(),
      cacheMiddleware.invalidateAfterCraneSingleNDRMutation(),
      cacheMiddleware.invalidateAfterHistoryMutation(),
      CraneRequestController.updateCraneRequestStatus,
    );
    router.get(
      '/upcoming_crane_request',
      validate(
        craneRequestValidation.upcomingRequestList,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      CraneRequestController.upcomingRequestListForMobile,
    );
    router.get(
      '/upcoming_request_list',
      passportConfig.isAuthenticated,
      CraneRequestController.upcomingRequestList,
    );
    router.post(
      '/edit_multiple_request',
      passportConfig.isAuthenticated,
      cacheMiddleware.invalidateAfterAllCalendarMutation(),
      cacheMiddleware.invalidateAfterCraneNDRListMutation(),
      cacheMiddleware.invalidateAfterCraneSingleNDRMutation(),
      cacheMiddleware.invalidateAfterHistoryMutation(),
      CraneRequestController.editMultipleDeliveryRequest,
    );
    return router;
  },
};
module.exports = accountRoute;
