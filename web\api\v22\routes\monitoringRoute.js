const { Router } = require('express');
const passportConfig = require('../config/passport');

const monitoringRoute = {
    get router() {
        const router = Router();

        // Memory monitoring endpoint
        router.get(
            '/memory',
            // passportConfig.isAuthenticated,
            (req, res) => {
                const memUsage = process.memoryUsage();
                const heapUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);
                const heapTotalMB = Math.round(memUsage.heapTotal / 1024 / 1024);
                const rssMB = Math.round(memUsage.rss / 1024 / 1024);
                const externalMB = Math.round(memUsage.external / 1024 / 1024);

                // Calculate percentage
                const heapUsagePercent = Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100);

                // Determine status
                let status = 'healthy';
                if (heapUsagePercent > 80) {
                    status = 'critical';
                } else if (heapUsagePercent > 60) {
                    status = 'warning';
                }

                const memoryInfo = {
                    status,
                    timestamp: new Date().toISOString(),
                    memory: {
                        heapUsed: `${heapUsedMB} MB`,
                        heapTotal: `${heapTotalMB} MB`,
                        heapUsagePercent: `${heapUsagePercent}%`,
                        rss: `${rssMB} MB`,
                        external: `${externalMB} MB`
                    },
                    limits: {
                        nodeMaxOldSpace: '4096 MB',
                        warning: '60%',
                        critical: '80%'
                    },
                    recommendations: heapUsagePercent > 60 ? [
                        'Consider restarting the server',
                        'Check for memory leaks in recent queries',
                        'Monitor slow query logs'
                    ] : []
                };

                res.json({
                    message: 'Memory usage information',
                    data: memoryInfo
                });
            }
        );

        // Database connection status
        router.get(
            '/database',
            // passportConfig.isAuthenticated,
            async (req, res) => {
                try {
                    const { sequelize } = require('../models');
                    await sequelize.authenticate();

                    res.json({
                        message: 'Database connection status',
                        data: {
                            status: 'connected',
                            timestamp: new Date().toISOString(),
                            dialect: sequelize.getDialect(),
                            pool: {
                                max: sequelize.connectionManager.pool.options.max,
                                min: sequelize.connectionManager.pool.options.min,
                                idle: sequelize.connectionManager.pool.options.idle,
                                acquire: sequelize.connectionManager.pool.options.acquire
                            }
                        }
                    });
                } catch (error) {
                    res.status(500).json({
                        message: 'Database connection failed',
                        error: error.message
                    });
                }
            }
        );

        return router;
    },
};

module.exports = monitoringRoute; 