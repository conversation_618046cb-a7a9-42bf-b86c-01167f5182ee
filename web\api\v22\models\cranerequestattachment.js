module.exports = (sequelize, DataTypes) => {
  const CraneRequestAttachement = sequelize.define(
    'CraneRequestAttachment',
    {
      ProjectId: DataTypes.INTEGER,
      MemberId: DataTypes.INTEGER,
      CraneRequestId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      attachement: DataTypes.STRING,
      filename: DataTypes.STRING,
      extension: DataTypes.STRING,
    },
    {},
  );

  CraneRequestAttachement.associate = (models) => {
    CraneRequestAttachement.belongsTo(models.CraneRequest);
  };

  CraneRequestAttachement.getAll = async (attr) => {
    const newCraneRequestAttachement = await CraneRequestAttachement.findAll({
      where: { ...attr },
      order: [['id', 'DESC']],
    });
    return newCraneRequestAttachement;
  };
  CraneRequestAttachement.createMultipleInstance = async (paramData) => {
    const newCraneRequestAttachement = await CraneRequestAttachement.bulkCreate(paramData);
    return newCraneRequestAttachement;
  };
  CraneRequestAttachement.createInstance = async (paramData) => {
    const newCraneRequestAttachement = await CraneRequestAttachement.create(paramData);
    return newCraneRequestAttachement;
  };
  return CraneRequestAttachement;
};
