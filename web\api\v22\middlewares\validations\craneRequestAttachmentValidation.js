const Joi = require('joi');

const craneRequestAttachmentValidation = {
  createCraneRequestAttachement: {
    params: Joi.object({
      CraneRequestId: Joi.number().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
    }),
  },
  getCraneRequestAttachement: {
    params: Joi.object({
      CraneRequestId: Joi.number().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
    }),
  },
  deleteCraneRequestAttachement: {
    params: Joi.object({
      id: Joi.number().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
    }),
  },
};
module.exports = craneRequestAttachmentValidation;
