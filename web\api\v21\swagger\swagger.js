module.exports = {
  openapi: '3.0.1',
  info: {
    title: 'NodeJS FolloIt',
    description: 'NodeJS FolloIt code for all modules.',
    version: '1.0.0',
  },
  servers: [
    {
      url: `${process.env.API_URL}v19`,
      description: `${process.env.NODE_ENV} server (v19)`,
    },
    {
      url: `${process.env.API_URL}v20`,
      description: `${process.env.NODE_ENV} server (v20)`,
    },
    {
      // url: `${process.env.API_URL}v21`,
      url: 'http://localhost:3000/api/v21',
      description: `${process.env.NODE_ENV} server (v21)`,
    },
  ],
  tags: [
    {
      name: 'Auth',
    },
    {
      name: 'Address',
    },
    {
      name: 'Admin',
    },
    {
      name: 'User',
    },
    {
      name: 'Plans',
    },
    {
      name: 'Gate',
    },
    {
      name: 'Equipment',
    },
    {
      name: 'Project',
    },
    {
      name: 'Company',
    },
    {
      name: 'Member',
    },
    {
      name: 'Definable',
    },
    {
      name: 'NDR',
    },
    {
      name: 'Void',
    },
    {
      name: 'Comment',
    },
    {
      name: 'Attachement',
    },
    {
      name: 'History',
    },
    {
      name: 'Subscription',
    },
    {
      name: 'Calendar',
    },
    {
      name: 'Notification',
    },
    {
      name: 'Dashboard',
    },
    {
      name: 'Device Token',
    },
    {
      name: 'Get Accounts',
    },
    {
      name: 'Plans And Projects',
    },
    {
      name: 'Overview',
    },
    {
      name: 'Super Admin Api Lists',
    },
    {
      name: 'Members',
    },
    {
      name: 'Crane Module',
    },
    {
      name: 'Crane Module - Attachments',
    },
    {
      name: 'Crane Module - Histories',
    },
    {
      name: 'Crane Module - Comments',
    },
    {
      name: 'Calendar Settings',
    },
    {
      name: 'Time Zone',
    },
    {
      name: 'Invite Link',
    },
    {
      name: 'Notification Preference',
    },
    {
      name: 'Concrete Module',
    },
    {
      name: 'Concrete Module - Attachments',
    },
    {
      name: 'Concrete Module - Histories',
    },
    {
      name: 'Concrete Module - Comments',
    },
    {
      name: 'Reports',
    },
    {
      name: 'Project Settings',
    },
    {
      name: 'Location',
    },
    {
      name: 'Project Sharing Settings',
    },
    {
      name: 'Booking Templates',
    },
  ],
  paths: {
    '/auth/register': {
      post: {
        tags: ['Auth'],
        summary: 'Create New Project Admin',
        description: 'Register as new Project Admin.',
        operationId: 'createUser',
        requestBody: {
          description: 'Create Project Admin',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/CreateUser',
              },
              examples: {
                SampleInput: {
                  $ref: '#/components/examples/registerUser',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
      },
    },
    '/auth/forgot_password': {
      post: {
        tags: ['Auth'],
        summary: 'Forgot Password',
        description: 'Forgot Password.',
        operationId: 'forgotPassword',
        requestBody: {
          description: 'Forgot Password',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/forgotPassword',
              },
            },
          },
          required: true,
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
      },
    },
    '/auth/reset_password_email/{reset_password_token}': {
      parameters: [
        {
          name: 'reset_password_token',
          in: 'path',
          description: 'reset token of the user',
          required: true,
          schema: {
            type: 'string',
          },
        },
      ],
      post: {
        tags: ['Auth'],
        summary: 'Reset Password',
        operationId: 'adminViewUser',
        requestBody: {
          description: 'Reset Password',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/resetPassword',
              },
            },
          },
          required: true,
        },
        responses: {
          200: {
            $ref: '#/components/responses/UserResponse',
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          404: {
            $ref: '#/components/responses/NotFound',
          },
        },
      },
    },
    '/auth/check_reset_token/{resetToken}': {
      parameters: [
        {
          name: 'resetToken',
          in: 'path',
          description: 'Check whether the reset token is correct or not',
          required: true,
          schema: {
            type: 'string',
          },
        },
      ],
      get: {
        tags: ['Auth'],
        summary: 'Check whether the reset token is correct or not',
        operationId: 'checkCorrectReset',
        responses: {
          200: {
            $ref: '#/components/responses/UserResponse',
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          404: {
            $ref: '#/components/responses/NotFound',
          },
        },
      },
    },
    '/auth/login': {
      post: {
        tags: ['Auth'],
        summary: 'Login',
        description: 'Login.',
        operationId: 'loginProperty',
        requestBody: {
          description: 'Login',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/loginProperty',
              },
              examples: {
                User: {
                  $ref: '#/components/examples/User',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          security: [
            {
              Authorization: [],
            },
          ],
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
      },
    },
    '/auth/exist_user': {
      post: {
        tags: ['Auth'],
        summary: 'Exist Email checking',
        description: 'Exist email.',
        operationId: 'existEmail',
        requestBody: {
          description: 'Exit email or Phone number',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/existEmail',
              },
              examples: {
                'Sample Input': {
                  $ref: '#/components/examples/existEmail',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
      },
    },
    '/address/get_country': {
      get: {
        tags: ['Address'],
        summary: 'Country List',
        description: 'Country List',
        operationId: 'countryList',
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
      },
    },
    '/member/get_roles': {
      get: {
        tags: ['Member'],
        summary: 'Get Role list for Member Creation',
        description: 'Get Role list for Member Creation',
        operationId: 'roleList',
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/admin/admin_login': {
      post: {
        tags: ['Admin'],
        summary: 'Login',
        description: 'Login.',
        operationId: 'loginProperty',
        requestBody: {
          description: 'Login',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/loginProperty',
              },
              examples: {
                Admin: {
                  $ref: '#/components/examples/Admin',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          security: [
            {
              Authorization: [],
            },
          ],
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
      },
    },
    '/admin/create_account_admin': {
      post: {
        tags: ['Admin'],
        summary: 'Create New Account Admin',
        description: 'Create New Account Admin',
        operationId: 'createAccount',
        requestBody: {
          description: 'Create Account Admin',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/CreateAccount',
              },
              examples: {
                SampleInput: {
                  $ref: '#/components/examples/CreateAccount',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/payment/create_plan': {
      post: {
        tags: ['Admin'],
        summary: 'Admin Create the initial plans in stripe.',
        description: "Create Plans (Login as a 'Super Admin' or 'Folloit Admin')",
        operationId: 'Create Plans',
        requestBody: {
          description: 'Plans',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/createPlan',
              },
              examples: {
                'Trial plan': {
                  $ref: '#/components/examples/trialPlan',
                },
                'Project Plan Monthly': {
                  $ref: '#/components/examples/projectPlanMonthly',
                },
                'Project Plan Yearly': {
                  $ref: '#/components/examples/projectPlanYearly',
                },
                'Enterprise Plan': {
                  $ref: '#/components/examples/enterprisePlan',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/payment/list_all_plans/{interval}': {
      parameters: [
        {
          name: 'interval',
          in: 'path',
          description: 'Plan List',
          required: true,
          schema: {
            type: 'string',
          },
        },
      ],
      get: {
        tags: ['Plans'],
        summary: 'Plan List',
        operationId: 'Plan List',
        requestBody: {
          description: 'All Plans basd on the interval',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/listPlan',
              },
            },
          },
          required: false,
        },
        responses: {
          200: {
            $ref: '#/components/responses/UserResponse',
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          404: {
            $ref: '#/components/responses/NotFound',
          },
        },
      },
    },
    '/address/get_state/{CountryId}': {
      parameters: [
        {
          name: 'CountryId',
          in: 'path',
          description: 'Country Id',
          required: true,
          schema: {
            type: 'string',
          },
        },
      ],
      get: {
        tags: ['Address'],
        summary: 'State List',
        operationId: 'State List',
        responses: {
          200: {
            $ref: '#/components/responses/UserResponse',
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          404: {
            $ref: '#/components/responses/NotFound',
          },
        },
      },
    },
    '/address/get_city/{StateId}': {
      parameters: [
        {
          name: 'StateId',
          in: 'path',
          description: 'State Id',
          required: true,
          schema: {
            type: 'integer',
            format: 'int64',
          },
        },
      ],
      get: {
        tags: ['Address'],
        summary: 'City List',
        operationId: 'City List',
        responses: {
          200: {
            $ref: '#/components/responses/UserResponse',
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          404: {
            $ref: '#/components/responses/NotFound',
          },
        },
      },
    },
    '/user/authenticated_user': {
      get: {
        tags: ['User'],
        summary: 'Login User Details',
        description: 'Login User Details.',
        operationId: 'authenticatedUser',
        requestBody: {
          description: 'Authenticated User',
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/user/change_password': {
      post: {
        tags: ['User'],
        summary: 'Change Password',
        description: 'Change Password',
        operationId: 'changePassword',
        requestBody: {
          description: 'Change Password for particular login User',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/changePassword',
              },
            },
          },
          required: true,
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/project/create_project': {
      post: {
        tags: ['Project'],
        summary: 'Create New Project',
        description: 'Create New Project',
        operationId: 'createProject',
        requestBody: {
          description: 'Create New Project.',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/createProject',
              },
              examples: {
                'New Project': {
                  $ref: '#/components/examples/createProject',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/project/exist_project': {
      post: {
        tags: ['Project'],
        summary: 'Check whether the project name exist or not',
        description: 'check Exist Project',
        operationId: 'existProject',
        requestBody: {
          description: 'Check Exist Project.',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/checkExist',
              },
              examples: {
                'New Project': {
                  $ref: '#/components/examples/checkExist',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/project/upgrade_plan': {
      post: {
        tags: ['Project'],
        summary: 'Upgrade Plan',
        description: 'Upgrade One plan to another',
        operationId: 'upgradePlan',
        requestBody: {
          description: 'Upgrade Plan.',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/upgradePlan',
              },
              examples: {
                'Upgrade Plan': {
                  $ref: '#/components/examples/upgradePlan',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/project/get_project': {
      get: {
        tags: ['Project'],
        summary: 'Get Project',
        description: 'Get Project',
        operationId: 'getProject',
        requestBody: {
          description: 'Get Project.',
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/gates/add_gates': {
      post: {
        tags: ['Gate'],
        summary: 'Add Gate',
        description: 'Add Gate with that name',
        operationId: 'Gate',
        requestBody: {
          description: 'Add Gate',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/addGate',
              },
            },
          },
          required: true,
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/gates/update_gates': {
      post: {
        tags: ['Gate'],
        summary: 'Update Gate',
        description: 'Update Gate',
        operationId: 'Update Gate',
        requestBody: {
          description: 'updateGate',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/updateGate',
              },
              examples: {
                'Update Gate': {
                  $ref: '#/components/examples/updateGate',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/gates/delete_gates': {
      post: {
        tags: ['Gate'],
        summary: 'Delete Gate',
        operationId: 'Delete Gate',
        requestBody: {
          description: 'DeleteGate.',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/deleteEquipment',
              },
              examples: {
                'Sample Input': {
                  $ref: '#/components/examples/deleteEquipment',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          200: {
            $ref: '#/components/responses/UserResponse',
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          404: {
            $ref: '#/components/responses/NotFound',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/concrete_request/delete_concrete_request': {
      post: {
        tags: ['Concrete Module'],
        summary: 'Delete concrete Booking',
        operationId: 'Delete concrete Booking',
        requestBody: {
          description: 'Delete concrete Booking',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/deleteConcreteRequest',
              },
              examples: {
                'Sample Input': {
                  $ref: '#/components/examples/deleteConcreteRequest',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          200: {
            $ref: '#/components/responses/UserResponse',
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          404: {
            $ref: '#/components/responses/NotFound',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/gates/gate_list/{ProjectId}/{pageSize}/{pageNo}/{ParentCompanyId}': {
      parameters: [
        {
          name: 'ProjectId',
          in: 'path',
          description: 'ProjectId of the Project',
          required: true,
          schema: {
            type: 'integer',
            format: 'int64',
          },
        },
        {
          name: 'pageSize',
          in: 'path',
          description: 'Page size for pagination',
          required: true,
          schema: {
            type: 'integer',
            format: 'int64',
          },
        },
        {
          name: 'pageNo',
          in: 'path',
          description: 'Page number for pagination',
          required: true,
          schema: {
            type: 'integer',
            format: 'int64',
          },
        },
        {
          name: 'ParentCompanyId',
          in: 'path',
          description: 'ParentCompanyId for company',
          required: true,
          schema: {
            type: 'integer',
            format: 'int64',
          },
        },
      ],
      post: {
        tags: ['Gate'],
        summary: 'List Gate',
        operationId: 'List Gate',
        requestBody: {
          description: 'Gate filter and search.',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/listGate',
              },
            },
          },
          required: false,
        },
        responses: {
          200: {
            $ref: '#/components/responses/UserResponse',
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          404: {
            $ref: '#/components/responses/NotFound',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/equipment/add_equipment': {
      post: {
        tags: ['Equipment'],
        summary: 'Add Equipment',
        description: 'Add Equipment',
        operationId: 'Equipment',
        requestBody: {
          description: 'Add Equipment',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/addEquipment',
              },
              examples: {
                'Sample Input': {
                  $ref: '#/components/examples/updateEquipment',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/equipment/update_equipment': {
      post: {
        tags: ['Equipment'],
        summary: 'Update Equipment',
        description: 'Update Equipment',
        operationId: 'Update Equipment',
        requestBody: {
          description: 'updateEquipment',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/updateEquipment',
              },
              examples: {
                'Sample Input': {
                  $ref: '#/components/examples/updateEquipment',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/equipment/delete_equipment': {
      post: {
        tags: ['Equipment'],
        summary: 'Delete Equipment',
        operationId: 'Delete Equipment',
        requestBody: {
          description: 'DeleteEquipment.',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/deleteEquipment',
              },
              examples: {
                'Sample Input': {
                  $ref: '#/components/examples/deleteEquipment',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          200: {
            $ref: '#/components/responses/UserResponse',
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          404: {
            $ref: '#/components/responses/NotFound',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/voids/remove_void': {
      post: {
        tags: ['Void'],
        summary: 'Remove Void',
        operationId: 'Remove Void',
        requestBody: {
          description: 'Remove Void.',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/removeVoid',
              },
              examples: {
                'Sample Input': {
                  $ref: '#/components/examples/removeVoid',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          200: {
            $ref: '#/components/responses/UserResponse',
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          404: {
            $ref: '#/components/responses/NotFound',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/equipment/delete_member': {
      post: {
        tags: ['Member'],
        summary: 'Delete Member',
        operationId: 'Delete Member',
        requestBody: {
          description: 'DeleteMember.',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/deleteEquipment',
              },
              examples: {
                'Sample Input': {
                  $ref: '#/components/examples/deleteEquipment',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          200: {
            $ref: '#/components/responses/UserResponse',
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          404: {
            $ref: '#/components/responses/NotFound',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/company/delete_company': {
      post: {
        tags: ['Company'],
        summary: 'Delete Company',
        operationId: 'Delete Company',
        requestBody: {
          description: 'DeleteCompany.',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/deleteEquipment',
              },
              examples: {
                'Sample Input': {
                  $ref: '#/components/examples/deleteEquipment',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          200: {
            $ref: '#/components/responses/UserResponse',
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          404: {
            $ref: '#/components/responses/NotFound',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/equipment/equipment_list/{ProjectId}/{pageSize}/{pageNo}/{ParentCompanyId}': {
      parameters: [
        {
          name: 'ProjectId',
          in: 'path',
          description: 'ProjectId of the Project',
          required: true,
          schema: {
            type: 'integer',
            format: 'int64',
          },
        },
        {
          name: 'pageSize',
          in: 'path',
          description: 'Page size for pagination',
          required: true,
          schema: {
            type: 'integer',
            format: 'int64',
          },
        },
        {
          name: 'pageNo',
          in: 'path',
          description: 'Page number for pagination',
          required: true,
          schema: {
            type: 'integer',
            format: 'int64',
          },
        },
        {
          name: 'ParentCompanyId',
          in: 'path',
          description: 'ParentCompanyId for company',
          required: true,
          schema: {
            type: 'integer',
            format: 'int64',
          },
        },
      ],
      post: {
        tags: ['Equipment'],
        summary: 'List Equipment',
        operationId: 'List Equipment',
        requestBody: {
          description: 'Equipment filter and search.',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/listEquipment',
              },
            },
          },
          required: false,
        },
        responses: {
          200: {
            $ref: '#/components/responses/UserResponse',
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          404: {
            $ref: '#/components/responses/NotFound',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/equipment/equipment_type_list/{ProjectId}': {
      parameters: [
        {
          name: 'ProjectId',
          in: 'path',
          description: 'ProjectId of the Project',
          required: true,
          schema: {
            type: 'integer',
            format: 'int64',
          },
        },
      ],
      get: {
        tags: ['Equipment'],
        summary: 'List Equipment type',
        operationId: 'List All Equipment type',
        requestBody: {
          description: 'List All Equipment type',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/listEquipmentType',
              },
            },
          },
          required: false,
        },
        responses: {
          200: {
            $ref: '#/components/responses/UserResponse',
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          404: {
            $ref: '#/components/responses/NotFound',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/calendar/event_NDR/{ProjectId}/{eventId}': {
      parameters: [
        {
          name: 'ProjectId',
          in: 'path',
          description: 'ProjectId of the Project',
          required: true,
          schema: {
            type: 'integer',
            format: 'int64',
          },
        },
        {
          name: 'eventId',
          in: 'path',
          description: 'ID of the event',
          required: true,
          schema: {
            type: 'number',
          },
        },
      ],
      post: {
        tags: ['Calendar'],
        summary: 'Calendar event',
        operationId: 'Calendar event',
        requestBody: {
          description: 'Calendar event.',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/calendarEvent',
              },
            },
          },
          required: false,
        },
        responses: {
          200: {
            $ref: '#/components/responses/UserResponse',
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          404: {
            $ref: '#/components/responses/NotFound',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/calendar/event_NDR/{ProjectId}/{eventId}/inspection': {
      parameters: [
        {
          name: 'ProjectId',
          in: 'path',
          description: 'ProjectId of the Project',
          required: true,
          schema: {
            type: 'integer',
            format: 'int64',
          },
        },
        {
          name: 'eventId',
          in: 'path',
          description: 'ID of the event',
          required: true,
          schema: {
            type: 'number',
          },
        },
      ],
      post: {
        tags: ['Inspection Calendar'],
        summary: 'Calendar Inspection event',
        operationId: 'Calendar Inspection event',
        requestBody: {
          description: 'Calendar event.',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/calendarEvent',
              },
            },
          },
          required: false,
        },
        responses: {
          200: {
            $ref: '#/components/responses/UserResponse',
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          404: {
            $ref: '#/components/responses/NotFound',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/company/add_company': {
      post: {
        tags: ['Company'],
        summary: 'Add Company',
        description: 'Add Company',
        operationId: 'addCompany',
        requestBody: {
          description: 'Add Company.',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/addCompany',
              },
              examples: {
                'Sample Input': {
                  $ref: '#/components/examples/addCompany',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/company/upload_logo/{ProjectId}/{ParentCompanyId}': {
      parameters: [
        {
          name: 'ProjectId',
          in: 'path',
          description: 'ProjectId for the company',
          required: true,
          schema: {
            type: 'integer',
            format: 'int64',
          },
        },
        {
          name: 'ParentCompanyId',
          in: 'path',
          description: 'ParentCompanyId for the company',
          required: true,
          schema: {
            type: 'integer',
            format: 'int64',
          },
        },
      ],
      post: {
        tags: ['Company'],
        summary: 'Company Logo Upload',
        description: 'Company Logo',
        operationId: 'companyLogo',
        requestBody: {
          required: true,
          description: 'Company Logo',
          content: {
            'multipart/form-data': {
              schema: {
                type: 'object',
                properties: {
                  logo: {
                    description: 'Company Logo',
                    name: 'logo',
                    type: 'file',
                    required: true,
                    allowEmptyValue: false,
                    format: 'binary',
                  },
                },
              },
            },
          },
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/company/edit_company': {
      post: {
        tags: ['Company'],
        summary: 'Edit Company',
        description: 'Edit Company',
        operationId: 'editCompany',
        requestBody: {
          description: 'Edit Company.',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/editCompany',
              },
              examples: {
                'Sample Input': {
                  $ref: '#/components/examples/editCompany',
                },
              },
            },
          },
          required: true,
        },
        responses: {
          default: {
            description: 'unexpected error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          200: {
            description: 'successful operation',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        'x-codegen-request-body-name': 'body',
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/crane_request_attachment/remove_crane_request_attachment/{id}/{ParentCompanyId}/{ProjectId}': {
      delete: {
        tags: ['Crane Module - Attachments'],
        summary: 'Remove Crane Request Attachment',
        operationId: 'removeCraneRequestAttachment',
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            schema: { type: 'string' },
          },
          {
            name: 'ParentCompanyId',
            in: 'path',
            required: true,
            schema: { type: 'string' },
          },
          {
            name: 'ProjectId',
            in: 'path',
            required: true,
            schema: { type: 'string' },
          },
        ],
        responses: {
          200: {
            description: 'Attachment removed successfully',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Success',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          404: {
            $ref: '#/components/responses/NotFound',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
    '/crane_request_attachment/list_crane_request_attachments/{CraneRequestId}/{ParentCompanyId}/{ProjectId}': {
      get: {
        tags: ['Crane Module - Attachments'],
        summary: 'List Crane Request Attachments',
        operationId: 'listCraneRequestAttachments',
        parameters: [
          {
            name: 'CraneRequestId',
            in: 'path',
            required: true,
            schema: { type: 'string' },
          },
          {
            name: 'ParentCompanyId',
            in: 'path',
            required: true,
            schema: { type: 'string' },
          },
          {
            name: 'ProjectId',
            in: 'path',
            required: true,
            schema: { type: 'string' },
          },
        ],
        responses: {
          200: {
            description: 'Attachment list retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Success',
                },
              },
            },
          },
          400: {
            $ref: '#/components/responses/BadRequest',
          },
          401: {
            $ref: '#/components/responses/Unauthorized',
          },
          404: {
            $ref: '#/components/responses/NotFound',
          },
          422: {
            $ref: '#/components/responses/UnprocessableEntity',
          },
        },
        security: [
          {
            Authorization: [],
          },
        ],
      },
    },
  },
};