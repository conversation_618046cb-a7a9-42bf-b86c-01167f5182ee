const status = require('http-status');
const equipmentLogService = require('../services/equipmentLogService')

const EquipmentLogController = {
    async addEquipmentLog(req, res, next) {
        equipmentLogService.addEquipmentLog(req, async (equipmentLog, error) => {
            if (error) {
                console.log(error, "error======")
                next(error);
            } else {
                res.status(status.CREATED).json({
                    message: 'EquipmentLog added successfully.',
                    data: equipmentLog,
                });
            }
        });
    },
    async listEquipmentLog(req, res, next) {
        equipmentLogService.listEquipmentLog(req, async (equipmentLogDetail, error) => {
            if (error) {
                next(error);
            } else {
                res.status(status.OK).json({
                    message: 'Equipment Log Listed successfully.',
                    data: equipmentLogDetail,
                });
            }
        });
    }
};
module.exports = EquipmentLogController;
