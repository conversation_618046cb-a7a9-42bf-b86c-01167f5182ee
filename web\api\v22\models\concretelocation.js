module.exports = (sequelize, DataTypes) => {
  const ConcreteLocation = sequelize.define(
    'ConcreteLocation',
    {
      location: DataTypes.STRING,
      locationAutoId: DataTypes.INTEGER,
      createdBy: {
        type: DataTypes.INTEGER,
        references: {
          model: 'Users', // name of Target model
          key: 'id', // key in Target model that we're referencing
        },
      },
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ProjectId: {
        type: DataTypes.INTEGER,
      },
      isDeleted: DataTypes.BOOLEAN,
    },
    {},
  );
  ConcreteLocation.associate = (models) => {
    ConcreteLocation.belongsTo(models.User, {
      as: 'userDetails',
      foreignKey: 'createdBy',
    });

    ConcreteLocation.belongsTo(models.Project);

    return ConcreteLocation;
  };
  ConcreteLocation.getConcreteLocations = async (attr) => {
    const locations = await ConcreteLocation.findOne({
      include: [
        { association: 'userDetails', attributes: { exclude: ['password', 'resetPasswordToken'] } },
        'Project',
      ],
      where: { ...attr },
      order: [['id', 'DESC']],
    });
    return locations;
  };
  ConcreteLocation.getAll = async (attr, limit, offset, searchCondition, sort, sortColumn) => {
    let location;
    const sortByFieldName = sortColumn || 'id';
    const sortByColumnType = sort || 'DESC';
    if (limit === 0) {
      location = await ConcreteLocation.findAll({
        where: { ...attr },
        order: [[`${sortByFieldName}`, `${sortByColumnType}`]],
      });
    } else {
      location = await ConcreteLocation.findAndCountAll({
        where: { ...attr, ...searchCondition },
        attributes: ['location', 'id', 'locationAutoId'],
        limit,
        offset,
        order: [[`${sortByFieldName}`, `${sortByColumnType}`]],
      });
    }
    return location;
  };
  ConcreteLocation.updateInstance = async (id, args) => {
    const location = await ConcreteLocation.update(args, { where: { id } });
    return location;
  };

  ConcreteLocation.createConcreteLocation = async (locationData) => {
    const newConcreteLocationData = await ConcreteLocation.create(locationData);
    return newConcreteLocationData;
  };
  return ConcreteLocation;
};
