const moment = require('moment');
const Cryptr = require('cryptr');
const { Sequelize, NotificationPreference, DigestNotification } = require('../models');

const { Op } = Sequelize;

const notificationHelper = {
  async createDeliveryPersonNotification(
    adminData,
    personData,
    projectDetails,
    newNotification,
    DeliveryPersonNotification,
    memberData,
    NotificationPreferenceItemId,
  ) {
    try {
      const data = {
        ProjectId: projectDetails.id,
        NotificationId: newNotification.id,
      };
      adminData.forEach(async (element) => {
        const newAdminData = data;
        newAdminData.MemberId = element.id;
        if (memberData.id !== element.id) {
          const getMemberNotificationPreference = await this.checkMemberNotificationPreference(
            data,
            element.id,
            NotificationPreferenceItemId,
          );
          if (getMemberNotificationPreference) {
            if (getMemberNotificationPreference.NotificationPreferenceItem.inappNotification) {
              if (getMemberNotificationPreference.instant) {
                const data1 = {
                  ProjectId: projectDetails.id,
                  NotificationId: newNotification.id,
                  MemberId: element.id,
                };
                await DeliveryPersonNotification.create(data1);
              }
              // else if (getMemberNotificationPreference.dailyDigest) {
              //   await this.createDailyDigestData(
              //     newAdminData,
              //     element.id,
              //     memberData,
              //     loginUser,
              //     dailyDigestMessage,
              //     requestType,
              //     messages,
              //     requestId,
              //   );
              // }
            }
          }
        }
      });
      personData.forEach(async (element1) => {
        const newPersonData = data;

        newPersonData.MemberId = element1.Member.id;
        if (memberData.id !== element1.Member.id) {
          const getNotificationPreference = await this.checkMemberNotificationPreference(
            data,
            element1.Member.id,
            NotificationPreferenceItemId,
          );
          if (getNotificationPreference) {
            if (getNotificationPreference.NotificationPreferenceItem.inappNotification) {
              if (getNotificationPreference.instant) {
                const data2 = {
                  ProjectId: projectDetails.id,
                  NotificationId: newNotification.id,
                  MemberId: element1.Member.id,
                };
                await DeliveryPersonNotification.create(data2);
              }
              //  else if (getNotificationPreference.dailyDigest) {
              //   await this.createDailyDigestData(
              //     newPersonData,
              //     element1.Member.id,
              //     memberData,
              //     loginUser,
              //     dailyDigestMessage,
              //     requestType,
              //     messages,
              //     requestId,
              //   );
              // }
            }
          }
        }
      });
    } catch (error) {
      console.log('error', error);
      return error;
    }
  },
  async memberNotificationCreation(memberData, DeliveryPersonNotification, Notification) {
    const notList = await Notification.findAll({
      where: { ProjectId: memberData.ProjectId },
    });
    const notData = {
      ProjectId: memberData.ProjectId,
    };
    notList.forEach(async (element) => {
      notData.NotificationId = element.id;
      notData.MemberId = memberData.id;
      await DeliveryPersonNotification.create({ notData });
    });
  },
  async checkMemberNotificationPreference(data, MemberId, NotificationPreferenceItemId) {
    const checkMemberNotification = await NotificationPreference.findOne({
      where: {
        MemberId,
        isDeleted: false,
        ProjectId: data.ProjectId,
      },
      attributes: [
        'id',
        'MemberId',
        'ProjectId',
        'ParentCompanyId',
        'NotificationPreferenceItemId',
        'instant',
        'dailyDigest',
      ],
      include: [
        {
          association: 'NotificationPreferenceItem',
          where: {
            id: +NotificationPreferenceItemId,
            isDeleted: false,
          },
          attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
        },
      ],
    });
    return checkMemberNotification;
  },
  async createDailyDigestData(
    data,
    MemberId,
    memberDetails,
    loginUser,
    dailyDigestMessage,
    requestType,
    messages,
    requestId,
  ) {
    const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
    const encryptedRequestId = cryptr.encrypt(requestId);
    const encryptedMemberId = cryptr.encrypt(MemberId);
    let imageUrl;
    let link;
    let height;
    if (requestType === 'Delivery Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/87758081-8ccd-4e4d-a4eb-6257466876da.png';
      link = 'delivery-request';
      height = 'height:18px;';
    }
    if (requestType === 'Crane Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/ab400721-520c-4f6f-941c-ac07acc28809.png';
      link = 'crane-request';
      height = 'height:32px;';
    }
    if (requestType === 'Concrete Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/c43e4c58-4c4d-44ff-b78c-fe9948ea53eb.png';
      link = 'concrete-request';
      height = 'height:18px;';
    }
    const object = {
      description: `<div>
	<ul style="list-style-type:none;padding:0px;border-bottom:1px dashed #E3E3E3;">
		<li style="display:flex;">
			<img src="${imageUrl}" alt="message-icon" style="${height}">
				<p style="margin:0px;font-size:12px;padding-left:10px;">
					<a href="#" target="" style="text-decoration: none;color:#4470FF;">
						${loginUser.firstName}  ${loginUser.lastName}
					</a>
					${dailyDigestMessage}
					<a href="${process.env.BASE_URL
        }/${link}?requestId=${encryptedRequestId}&memberId=${encryptedMemberId}" style="text-decoration: none;color:#4470FF;" >
      ${messages} 
					</a>
  <span style="color:#707070;">on ${moment().utc().format('MMMM DD')} at ${moment()
          .utc()
          .format('hh:mm A zz')}</span>
				</p>
		</li>
	</ul>
</div> `,
      MemberId,
      ProjectId: data.ProjectId,
      isSent: false,
      isDeleted: false,
      ParentCompanyId: memberDetails.ParentCompanyId,
    };
    await DigestNotification.create(object);
  },
  async createMemberDeliveryLocationInAppNotification(
    DeliveryPersonNotification,
    ProjectId,
    NotificationId,
    memberLocationPreference,
    NotificationPreferenceItemId,
  ) {
    for (let index = 0; index < memberLocationPreference.length; index += 1) {
      const element = memberLocationPreference[index];
      if (element.follow) {
        const data = {
          ProjectId,
        };
        const getMemberNotificationPreference = await this.checkMemberNotificationPreference(
          data,
          element.Member.id,
          NotificationPreferenceItemId,
        );
        if (getMemberNotificationPreference) {
          if (getMemberNotificationPreference.NotificationPreferenceItem.inappNotification) {
            if (getMemberNotificationPreference.instant) {
              const data1 = {
                ProjectId,
                NotificationId,
                MemberId: element.Member.id,
                isLocationFollowNotification: true,
              };
              await DeliveryPersonNotification.create(data1);
            }
          }
        }
      }
    }
  },

  async createMemberInspectionLocationInAppNotification(
    InspectionPersonNotification,
    ProjectId,
    NotificationId,
    memberLocationPreference,
    NotificationPreferenceItemId,
  ) {
    for (let index = 0; index < memberLocationPreference.length; index += 1) {
      const element = memberLocationPreference[index];
      if (element.follow) {
        const data = {
          ProjectId,
        };
        const getMemberNotificationPreference = await this.checkMemberNotificationPreference(
          data,
          element.Member.id,
          NotificationPreferenceItemId,
        );
        if (getMemberNotificationPreference) {
          if (getMemberNotificationPreference.NotificationPreferenceItem.inappNotification) {
            if (getMemberNotificationPreference.instant) {
              const data1 = {
                ProjectId,
                NotificationId,
                MemberId: element.Member.id,
                isLocationFollowNotification: true,
              };
              // await InspectionPersonNotification.create(data1);
            }
          }
        }
      }
    }
  },
};
module.exports = notificationHelper;
