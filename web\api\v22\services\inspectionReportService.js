/* eslint-disable prettier/prettier */
const moment = require('moment');
const _ = require('lodash');
const httpStatus = require('http-status');
const cron = require('node-cron');
const pdfHeatMapService = require('./pdfHeatMapService');
const { Sequelize, sequelize, Enterprise } = require('../models');
const {
    InspectionPerson,
    VoidList,
    SchedulerReport,
    CraneRequest,
    ConcreteRequest,
} = require('../models');
let { InspectionRequest, Member, InspectionGate, InspectionCompany, User } = require('../models');
const helper = require('../helpers/domainHelper');
const {
    queryBuilderExternal,
    replacementsBuilderExternal,
    defaultTimeSlots,
} = require('../helpers/queryBuilderExternal');
const exportService = require('./exportService');
const pdfInspectionReportService = require('./pdfInspectionReportService');
const csvInspectionReportService = require('./csvInspectionReportService');
const calendarSettingsService = require('./calendarSettingsService');
const excelInspectionReportService = require('./excelInspectionReportService');
const excelWeeklyCalendarService = require('./excelWeeklyCalendarService');
const ApiError = require('../helpers/apiError');
const awsConfig = require('../middlewares/awsConfig');
const puppeteerService = require('./puppeteerService');

let publicUser;
let publicMember;
const { Op } = Sequelize;

const inspectionReportService = {
    async getDynamicModel(inputData) {
        await this.returnProjectModel();
        let { domainName } = inputData.user;
        const incomeData = inputData;
        let enterpriseValue;
        let ProjectId;
        const ParentCompanyId = inputData.body.ParentCompanyId
            ? inputData.body.ParentCompanyId
            : inputData.params.ParentCompanyId;
        let domainEnterpriseValue;
        if (domainName) {
            domainEnterpriseValue = await Enterprise.findOne({
                where: { name: domainName.toLowerCase() },
            });
            if (!domainEnterpriseValue) {
                domainName = '';
            }
        }
        if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
            const { email } = inputData.user;
            let userData;
            if (email) {
                userData = await publicUser.findOne({ where: { email } });
            }
            if (userData) {
                const memberData = await publicMember.findOne({
                    where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
                });
                if (memberData) {
                    if (memberData.isAccount) {
                        enterpriseValue = await Enterprise.findOne({
                            where: { id: memberData.EnterpriseId, status: 'completed' },
                        });
                        if (enterpriseValue) {
                            domainName = enterpriseValue.name.toLowerCase();
                        }
                    } else {
                        enterpriseValue = await Enterprise.findOne({
                            where: { ParentCompanyId, status: 'completed' },
                        });
                        if (enterpriseValue) {
                            domainName = enterpriseValue.name.toLowerCase();
                        }
                    }
                } else {
                    enterpriseValue = await Enterprise.findOne({
                        where: { ParentCompanyId, status: 'completed' },
                    });
                    if (enterpriseValue) {
                        domainName = enterpriseValue.name.toLowerCase();
                    }
                }
            }
        }
        const modelObj = await helper.getDynamicModel(domainName);
        InspectionRequest = modelObj.InspectionRequest;
        Member = modelObj.Member;
        InspectionGate = modelObj.InspectionGate;
        InspectionCompany = modelObj.InspectionCompany;
        User = modelObj.User;
        if (enterpriseValue) {
            const newUser = await User.findOne({ where: { email: inputData.user.email } });
            incomeData.user = newUser;
        }
        return ProjectId;
    },
    async returnProjectModel() {
        const modelData = await helper.returnProjectModel();
        publicMember = modelData.Member;
        publicUser = modelData.User;
    },
    async listInspectionRequest(inputData, done) {
        try {
            await this.getDynamicModel(inputData);
            const { params } = inputData;
            const loginUser = inputData.user;
            const incomeData = inputData.body;
            let { sort } = inputData.body;
            let { sortByField } = inputData.body;
            let order;
            let searchCondition = {};
            if (params.void >= 1 && params.void <= 0) {
                done(null, { message: 'Please enter void as 1 or 0' });
            } else {
                const memberDetails = await Member.findOne({
                    where: Sequelize.and({
                        UserId: loginUser.id,
                        ProjectId: params.ProjectId,
                        isDeleted: false,
                        isActive: true,
                    }),
                });
                if (memberDetails) {
                    const voidInspection = [];
                    const voidList = await VoidList.findAll({
                        where: {
                            ProjectId: params.ProjectId,
                            isDeliveryRequest: true,
                            InspectionRequestId: { [Op.ne]: null },
                        },
                    });
                    voidList.forEach(async (element) => {
                        voidInspection.push(element.InspectionRequestId);
                    });
                    const offset = (+params.pageNo - 1) * +params.pageSize;
                    const condition = {
                        ProjectId: +params.ProjectId,
                        isQueued: false,
                    };
                    if (params.void === '0' || params.void === 0) {
                        condition['$InspectionRequest.id$'] = {
                            [Op.and]: [{ [Op.notIn]: voidInspection }],
                        };
                    } else {
                        condition['$InspectionRequest.id$'] = {
                            [Op.and]: [{ [Op.in]: voidInspection }],
                        };
                    }
                    if (incomeData.descriptionFilter) {
                        condition.description = {
                            [Sequelize.Op.iLike]: `%${incomeData.descriptionFilter}%`,
                        };
                    }
                    if (incomeData.pickFrom) {
                        condition.cranePickUpLocation = {
                            [Sequelize.Op.iLike]: `%${incomeData.pickFrom}%`,
                        };
                    }
                    if (incomeData.pickTo) {
                        condition.craneDropOffLocation = {
                            [Sequelize.Op.iLike]: `%${incomeData.pickTo}%`,
                        };
                    }
                    if (incomeData.idFilter) {
                        condition.InspectionId = incomeData.idFilter;
                    }

                    if (incomeData.startdate) {
                        const startDateTime = moment(incomeData.startdate, 'YYYY-MM-DD')
                            .startOf('day')
                            .utcOffset(Number(inputData.headers.timezoneoffset), true);
                        const endDateTime = moment(incomeData.enddate, 'YYYY-MM-DD')
                            .endOf('day')
                            .utcOffset(Number(inputData.headers.timezoneoffset), true);
                        condition.inspectionStart = {
                            [Op.between]: [moment(startDateTime), moment(endDateTime)],
                        };
                    } else if (incomeData.startdate === "") {
                        const startDateTime = moment(new Date(), 'YYYY-MM-DD')
                            .startOf('day')
                            .utcOffset(Number(inputData.headers.timezoneoffset), true);
                        condition.inspectionStart = {
                            [Op.gte]: moment(startDateTime),
                        };
                    }
                    if (incomeData.enddateFilter) {
                        const startDateTime = moment(incomeData.enddateFilter, 'YYYY-MM-DD')
                            .startOf('day')
                            .utcOffset(Number(inputData.headers.timezoneoffset), true);
                        const endDateTime = moment(incomeData.enddateFilter, 'YYYY-MM-DD')
                            .endOf('day')
                            .utcOffset(Number(inputData.headers.timezoneoffset), true);
                        condition.inspectionStart = {
                            [Op.between]: [moment(startDateTime), moment(endDateTime)],
                        };
                    }
                    if (incomeData.upcoming) {
                        condition.inspectionStart = {
                            [Op.gt]: new Date(),
                        };
                        order = 'ASC';
                        sort = 'ASC';
                        sortByField = 'inspectionStart';
                    }
                    if (incomeData.search) {
                        const searchDefault = [
                            {
                                '$approverDetails.User.firstName$': {
                                    [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                                },
                            },
                            {
                                '$equipmentDetails.Equipment.equipmentName$': {
                                    [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                                },
                            },
                            {
                                description: {
                                    [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                                },
                            },
                            {
                                cranePickUpLocation: {
                                    [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                                },
                            },
                            {
                                craneDropOffLocation: {
                                    [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                                },
                            },
                        ];
                        if (!Number.isNaN(+incomeData.search)) {
                            searchCondition = {
                                [Op.and]: [
                                    {
                                        [Op.or]: [
                                            searchDefault,
                                            {
                                                [Op.and]: [
                                                    {
                                                        InspectionId: +incomeData.search,
                                                        isDeleted: false,
                                                        ProjectId: +params.ProjectId,
                                                    },
                                                ],
                                            },
                                        ],
                                    },
                                ],
                            };
                        } else {
                            searchCondition = {
                                [Op.and]: [
                                    {
                                        [Op.or]: searchDefault,
                                    },
                                ],
                            };
                        }
                    }
                    if (incomeData.equipmentFilter.length) {
                        condition['$equipmentDetails.Equipment.id$'] = {
                            [Op.in]: incomeData.equipmentFilter
                        };
                    }
                    if (incomeData.gateFilter.length) {
                        condition['$gateDetails.Gate.id$'] = {
                            [Op.in]: incomeData.gateFilter
                        }
                    }
                    if (incomeData.defineFilter.length) {
                        condition['$defineWorkDetails.DeliverDefineWork.id$'] = {
                            [Op.in]: incomeData.defineFilter,
                        };
                    }
                    if (incomeData.companyFilter.length) {
                        condition['$companyDetails.Company.id$'] = {
                            [Op.in]: incomeData.companyFilter,
                        };
                    }

                    if (incomeData.statusFilter.length) {
                        condition.status = {
                            [Op.in]: incomeData.statusFilter,
                        };
                    }
                    if (incomeData.inspectionStatusFilter.length) {
                        condition.inspectionStatus = {
                            [Op.in]: incomeData.inspectionStatusFilter,
                        };
                    }
                    if (incomeData.inspectionTypeFilter.length) {
                        condition.inspectionType = {
                            [Op.in]: incomeData.inspectionTypeFilter,
                        };
                    }

                    if (incomeData.locationFilter.length) {
                        condition['$location.id$'] = {
                            [Op.in]: incomeData.locationFilter,
                        };
                    }

                    const roleId = memberDetails.RoleId;
                    const memberId = memberDetails.id;
                    if (
                        incomeData.companyFilter ||
                        incomeData.gateFilter ||
                        incomeData.memberFilter ||
                        incomeData.assignedFilter ||
                        (memberDetails.RoleId === 4 &&
                            (params.void === '0' || params.void === 0) &&
                            !incomeData.upcoming) ||
                        (memberDetails.RoleId === 3 &&
                            (params.void === '0' || params.void === 0) &&
                            !incomeData.upcoming)
                    ) {
                        const result = { count: 0, rows: [] };
                        const inspectionList = await InspectionRequest.getCalendarData(
                            condition,
                            roleId,
                            memberId,
                            searchCondition,
                            order,
                            sort,
                            sortByField,
                        );
                        this.getSearchData(
                            incomeData,
                            inspectionList.rows,
                            [],
                            +params.pageSize,
                            0,
                            0,
                            memberDetails,
                            async (checkResponse, checkError) => {
                                if (!checkError) {
                                    this.getLimitData(
                                        checkResponse,
                                        0,
                                        +params.pageSize,
                                        [],
                                        incomeData,
                                        inputData.headers.timezoneoffset,
                                        async (newResponse, newError) => {
                                            if (!newError) {
                                                if (sort === 'ASC') {
                                                    newResponse.sort(function (a, b) {
                                                        return a[sortByField] > b[sortByField]
                                                            ? 1
                                                            : b[sortByField] > a[sortByField]
                                                                ? -1
                                                                : 0;
                                                    });
                                                } else {
                                                    newResponse.sort(function (a, b) {
                                                        return b[sortByField] > a[sortByField]
                                                            ? 1
                                                            : a[sortByField] > b[sortByField]
                                                                ? -1
                                                                : 0;
                                                    });
                                                }
                                                if (inputData.body.exportType) {
                                                    result.rows = newResponse;
                                                } else {
                                                    result.rows = newResponse.slice(offset, offset + +params.pageSize);
                                                }
                                                result.count = checkResponse.length;
                                                done(result, false);
                                            } else {
                                                done(null, { message: 'Something went wrong' });
                                            }
                                        },
                                    );
                                } else {
                                    done(null, { message: 'Something went wrong' });
                                }
                            },
                        );
                    } else {
                        const newResult = { count: 0, rows: [] };
                        const inspectionList = await InspectionRequest.getAll(
                            condition,
                            roleId,
                            memberId,
                            +params.pageSize,
                            offset,
                            searchCondition,
                            order,
                            sort,
                            sortByField,
                        );
                        this.getLimitData(
                            inspectionList,
                            0,
                            +params.pageSize,
                            [],
                            incomeData,
                            inputData.headers.timezoneoffset,
                            async (newResponse, newError) => {
                                if (!newError) {
                                    if (sort === 'ASC') {
                                        newResponse.sort(function (a, b) {
                                            return a[sortByField] > b[sortByField]
                                                ? 1
                                                : b[sortByField] > a[sortByField]
                                                    ? -1
                                                    : 0;
                                        });
                                    } else {
                                        newResponse.sort(function (a, b) {
                                            return b[sortByField] > a[sortByField]
                                                ? 1
                                                : a[sortByField] > b[sortByField]
                                                    ? -1
                                                    : 0;
                                        });
                                    }
                                    if (inputData.body.exportType) {
                                        newResult.rows = newResponse;
                                    } else {
                                        newResult.rows = newResponse.slice(offset, offset + +params.pageSize);
                                    }
                                    newResult.count = inspectionList.length;
                                    done(newResult, false);
                                } else {
                                    done(null, { message: 'Something went wrong' });
                                }
                            },
                        );
                    }
                } else {
                    done(null, { message: 'Project Id/Member does not exist' });
                }
            }
        } catch (e) {
            done(null, e);
        }
    },
    async getSearchData(
        incomeData,
        inspectionList,
        result,
        limit,
        index,
        count,
        memberDetails,
        done,
    ) {
        const elementValue = inspectionList[index];
        if (elementValue) {
            const element = JSON.parse(JSON.stringify(elementValue));
            const status = { companyCondition: true, gateCondition: true, memberCondition: true };
            if (incomeData.companyFilter.length) {
                const data = await InspectionCompany.findOne({
                    where: {
                        InspectionId: element.id,
                        CompanyId: incomeData.companyFilter,
                        isDeleted: false,
                    },
                });
                if (!data) {
                    status.companyCondition = false;
                }
            }
            if (incomeData.gateFilter.length) {
                const data = await InspectionGate.findOne({
                    where: {
                        InspectionId: element.id,
                        GateId: incomeData.gateFilter,
                        isDeleted: false,
                    },
                });
                if (!data) {
                    status.gateCondition = false;
                }
            }
            if (incomeData.memberFilter.length) {
                const data = await InspectionPerson.findOne({
                    where: {
                        InspectionId: element.id,
                        MemberId: incomeData.memberFilter,
                        isDeleted: false,
                    },
                });
                if (!data) {
                    status.memberCondition = false;
                }
            }

            if (status.companyCondition && status.gateCondition && status.memberCondition) {
                result.push(element);
            }
            if (index < inspectionList.length - 1) {
                this.getSearchData(
                    incomeData,
                    inspectionList,
                    result,
                    limit,
                    index + 1,
                    count + 1,
                    memberDetails,
                    (response, err) => {
                        if (!err) {
                            done(response, false);
                        } else {
                            done(null, err);
                        }
                    },
                );
            } else {
                done(result, false);
            }
        } else {
            done(result, false);
        }
    },
    async getLimitData(result, index, limit, finalResult, incomeData, timezoneoffset, done) {
        if (index < limit) {
            finalResult.push(result);
            this.getLimitData(
                result,
                index + 1,
                limit,
                finalResult,
                incomeData,
                timezoneoffset,
                (response, err) => {
                    if (!err) {
                        done(response, false);
                    } else {
                        done(null, err);
                    }
                },
            );
        } else {
            done(result, false);
        }
    },

    async exportReport(req, done) {
        await this.listInspectionRequest(req, async (response, error) => {
            if (!error) {
                if (req.body.exportType === 'PDF') {
                    const loginUser = req.user;
                    await pdfInspectionReportService.pdfFormatOfInspectionRequest(
                        req.params,
                        loginUser,
                        response.rows,
                        req,
                        async (pdfFile, err) => {
                            if (!err) {
                                if (req.body.saved) {
                                    req.body.reportType = 'Inspection';
                                    const savedData = await this.createSavedReports(req, pdfFile);
                                    if (savedData) {
                                        return done(pdfFile, false);
                                    }
                                    done(null, { message: 'cannot create reports' });
                                } else {
                                    return done(pdfFile, false);
                                }
                            }
                        },
                    );
                }
                if (req.body.exportType === 'EXCEL') {
                    const workbook = await exportService.createWorkbook();
                    let reportWorkbook = await excelInspectionReportService.inspectionReport(
                        workbook,
                        response.rows,
                        req.body.selectedHeaders,
                        req.headers.timezoneoffset,
                    );
                    if (reportWorkbook) {
                        if (req.body.saved) {
                            reportWorkbook = await reportWorkbook.xlsx.writeBuffer();
                            const excelFile = await this.saveExcelReport(
                                reportWorkbook,
                                req.body.reportName,
                                req.body.exportType,
                            );
                            if (excelFile) {
                                req.body.reportType = 'Inspection';
                                const savedData = await this.createSavedReports(req, excelFile);
                                return done(excelFile, false);
                            }
                            done(null, { message: 'cannot create reports' });
                        } else {
                            return done(reportWorkbook, false);
                        }
                    }
                    done(null, { message: 'cannot export document' });
                }
                if (req.body.exportType === 'CSV') {
                    await csvInspectionReportService.exportInspectionReportInCsvFormat(
                        response.rows,
                        req.body.selectedHeaders,
                        req.headers.timezoneoffset,
                        req.body.reportName,
                        req.body.exportType,
                        async (csvFile, err) => {
                            if (!err) {
                                if (req.body.saved) {
                                    req.body.reportType = 'Inspection';
                                    const savedData = await this.createSavedReports(req, csvFile);
                                    if (savedData) {
                                        return done(csvFile, false);
                                    }
                                    done(null, { message: 'cannot create reports' });
                                } else {
                                    return done(csvFile, false);
                                }
                            }
                            return done(null, { message: 'cannot export document' });
                        },
                    );
                }
            }
        });
    },

    async createSavedReports(inputData, pdfFile) {
        try {
            const { params } = inputData;
            const loginUser = inputData.user;
            const incomeData = inputData.body;
            incomeData.startTime = incomeData.startTime ? incomeData.startTime : '00:00';
            incomeData.endTime = incomeData.endTime ? incomeData.endTime : '00:00';
            incomeData.startDate = incomeData.startDate
                ? incomeData.startDate
                : moment().format('YYYY-MM-DD');
            incomeData.endDate = incomeData.endDate ? incomeData.endDate : moment().format('YYYY-MM-DD');

            const createDataFormation = {
                isSaved: true,
                reportName: incomeData.reportName,
                reportType: incomeData.reportType,
                ProjectId: parseInt(params.ProjectId),
                outputFormat: incomeData.exportType.trim(),
                recurrence: 'Does Not Repeat',
                repeatEvery: {},
                sendTo: [],
                subject: 'Report',
                message: 'Report',
                status: incomeData.status || null,
                isDeleted: false,
                createdBy: loginUser.id,
                lastRun: moment(),
                timezone: incomeData.timezone,
                startDate: `${incomeData.startDate}/${incomeData.startTime}`,
                endDate: `${incomeData.endDate}/${incomeData.endTime}`,
                memberFilterId: incomeData.memberFilter,
                parentFilterCompanyId: incomeData.ParentCompanyId,
                gateId: incomeData.gateFilter,
                defineId: incomeData.defineFilter,
                companyId: typeof incomeData.companyFilter === 'number' ? incomeData.companyFilter : 0,
                equipmentId:
                    typeof incomeData.equipmentFilter === 'number' ? incomeData.equipmentFilter : 0,
                companyFilter: typeof incomeData.companyFilter === 'number' ? '' : incomeData.companyFilter,
                equipmentFilter:
                    typeof incomeData.equipmentFilter === 'number' ? '' : incomeData.equipmentFilter,
                templateFilterType: incomeData.templateType ? incomeData.templateType : '[]',
                selectedHeaders: incomeData.selectedHeaders ? incomeData.selectedHeaders : '[]',
                s3_url: pdfFile,
                isEndDateMeet: false,
                idFilter: incomeData.idFilter ? incomeData.idFilter : 0,
                sort: incomeData.sort ? incomeData.sort : 'DESC',
                sortByField: incomeData.sortByField ? incomeData.sortByField : 'id',
                queuedNdr: incomeData.queuedNdr ? incomeData.queuedNdr : false,
                pickFrom: incomeData.pickFrom ? incomeData.pickFrom : '',
                pickTo: incomeData.pickTo ? incomeData.pickTo : '',
                locationFilter: incomeData.locationFilter ? incomeData.locationFilter : '',
                descriptionFilter: incomeData.descriptionFilter ? incomeData.descriptionFilter : '',
                orderNumberFilter: incomeData.orderNumberFilter ? incomeData.orderNumberFilter : '',
                mixDesignFilter: incomeData.mixDesignFilter ? incomeData.mixDesignFilter : '',
                truckspacingFilter: incomeData.truckspacingFilter ? incomeData.truckspacingFilter : '',
                slumpFilter: incomeData.slumpFilter ? incomeData.slumpFilter : '',
                primerFilter: incomeData.primerFilter ? incomeData.primerFilter : '',
                quantityFilter: incomeData.quantityFilter ? incomeData.quantityFilter : '',
                inspectionStatusFilter: incomeData.inspectionStatusFilter,
                inspectionTypeFilter: incomeData.inspectionTypeFilter,
            };
            createDataFormation.cronExpression = '1 1 1 1 1';
            const newScheduler = await SchedulerReport.createInstance(createDataFormation);
            return newScheduler;
        } catch (e) {
            return null;
        }
    },
    async saveExcelReport(reportWorkbook, reportName, exportType) {
        return new Promise(async (res, rej) => {
            if (Buffer.isBuffer(reportWorkbook)) {
                await awsConfig.reportUpload(
                    reportWorkbook,
                    reportName,
                    exportType === 'EXCEL' ? 'xlsx' : exportType,
                    async (result, error1) => {
                        if (!error1) {
                            res(result);
                        }
                        rej(null);
                    },
                );
            } else {
                rej(null);
            }
        });
    },
    async exportReportForScheduler(req) {
        return new Promise(async (res, rej) => {
            try {
                await this.listInspectionRequest(req, async (response, error) => {
                    if (!error) {
                        if (response.count == 0) {
                            return res('No Data Found');
                        }
                        if (req.body.exportType === 'PDF') {
                            const loginUser = req.user;
                            await pdfInspectionReportService.pdfFormatOfInspectionRequest(
                                req.params,
                                loginUser,
                                response.rows,
                                req,
                                async (pdfFile, err) => {
                                    if (!err) {
                                        res(pdfFile);
                                    } else {
                                        rej(err);
                                    }
                                },
                            );
                        }
                        if (req.body.exportType === 'EXCEL') {
                            const workbook = await exportService.createWorkbook();
                            let reportWorkbook = await excelInspectionReportService.inspectionReport(
                                workbook,
                                response.rows,
                                req.body.selectedHeaders,
                                req.headers.timezoneoffset,
                            );
                            if (reportWorkbook) {
                                reportWorkbook = await reportWorkbook.xlsx.writeBuffer();
                                // console.log("******Buffer.isBuffer(reportWorkbook)******", Buffer.isBuffer(reportWorkbook));
                                if (Buffer.isBuffer(reportWorkbook)) {
                                    await awsConfig.reportUpload(
                                        reportWorkbook,
                                        req.body.reportName,
                                        req.body.exportType === 'EXCEL' ? 'xlsx' : req.body.exportType,
                                        async (result, error1) => {
                                            if (!error1) {
                                                res(result);
                                            } else {
                                                rej(error1);
                                            }
                                        },
                                    );
                                } else {
                                    res('No data found');
                                }
                            } else {
                                res('No data found');
                            }
                        }
                        if (req.body.exportType === 'CSV') {
                            await csvInspectionReportService.exportInspectionReportInCsvFormat(
                                response.rows,
                                req.body.selectedHeaders,
                                req.headers.timezoneoffset,
                                req.body.reportName,
                                req.body.exportType,
                                async (csvFile, err) => {
                                    if (!err) {
                                        res(csvFile);
                                    } else {
                                        rej(err);
                                    }
                                },
                            );
                        }
                    } else {
                        rej(error);
                    }
                });
            } catch (e) {
                rej(e);
            }
        });
    },
};

module.exports = inspectionReportService;
