const NodeCache = require('node-cache');
const crypto = require('crypto');
const { CACHE_TTL } = require('../config/cache');

class CacheService {
  constructor() {
    this.cache = new NodeCache({
      stdTTL: CACHE_TTL.DEFAULT, // Default TTL: 2 hours
      checkperiod: 600, // Check for expired keys every 10 minutes
      useClones: false, // Don't clone objects for better performance
      deleteOnExpire: true, // Automatically delete expired keys
    });

    this.cache.on('expired', (key, value) => {
      console.log(`Cache key expired: ${key}`);
    });

    this.cache.on('flush', () => {
      console.log('Cache flushed');
    });

    // Default TTL in seconds (2 hours)
    this.defaultTTL = CACHE_TTL.DEFAULT;
  }

  /**
   * Generate a hash for cache key generation
   * @param {Object} data - Data to hash
   * @returns {string} Hash string
   */
  generateHash(data) {
    const jsonString = JSON.stringify(data);
    return crypto.createHash('md5').update(jsonString).digest('hex');
  }

  /**
   * Generate a cache key with parameters
   * @param {string} prefix - Key prefix
   * @param {Object} params - Parameters to include in key
   * @returns {string} Generated cache key
   */
  generateCacheKey(prefix, params = {}) {
    const filteredParams = Object.keys(params).reduce((acc, key) => {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        acc[key] = params[key];
      }
      return acc;
    }, {});

    const hash = this.generateHash(filteredParams);
    return `${prefix}:${hash}`;
  }

  /**
   * Get data from cache
   * @param {string} key - Cache key
   * @returns {any} Cached data or null
   */
  get(key) {
    try {
      const data = this.cache.get(key);
      return data !== undefined ? data : null;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  /**
   * Set data in cache
   * @param {string} key - Cache key
   * @param {any} data - Data to cache
   * @param {number} ttl - Time to live in seconds
   * @returns {boolean} Success status
   */
  set(key, data, ttl = this.defaultTTL) {
    try {
      return this.cache.set(key, data, ttl);
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  /**
   * Delete data from cache
   * @param {string} key - Cache key
   * @returns {boolean} Success status
   */
  del(key) {
    try {
      const deletedCount = this.cache.del(key);
      return deletedCount > 0;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }

  /**
   * Delete multiple keys matching a pattern
   * @param {string} pattern - Pattern to match keys
   * @returns {boolean} Success status
   */
  delPattern(pattern) {
    try {
      const keys = this.cache.keys();

      const matchingKeys = keys.filter(key => {
        // Convert Redis pattern to regex pattern
        const regexPattern = pattern
          .replace(/\*/g, '.*')
          .replace(/\?/g, '.');
        const regex = new RegExp(regexPattern);
        return regex.test(key);
      });

      if (matchingKeys.length > 0) {
        this.cache.del(matchingKeys);
        console.log(`Deleted ${matchingKeys.length} keys matching pattern: ${pattern}`);
      }
      return true;
    } catch (error) {
      console.error('Cache delete pattern error:', error);
      return false;
    }
  }

  /**
   * Check if key exists in cache
   * @param {string} key - Cache key
   * @returns {boolean} Existence status
   */
  exists(key) {
    try {
      return this.cache.has(key);
    } catch (error) {
      console.error('Cache exists error:', error);
      return false;
    }
  }

  /**
   * Get multiple keys from cache
   * @param {Array<string>} keys - Array of cache keys
   * @returns {Object} Object with key-value pairs
   */
  mget(keys) {
    try {
      return this.cache.mget(keys);
    } catch (error) {
      console.error('Cache mget error:', error);
      return {};
    }
  }

  /**
   * Set multiple key-value pairs in cache
   * @param {Object} data - Object with key-value pairs
   * @param {number} ttl - Time to live in seconds
   * @returns {boolean} Success status
   */
  mset(data, ttl = this.defaultTTL) {
    try {
      return this.cache.mset(data);
    } catch (error) {
      console.error('Cache mset error:', error);
      return false;
    }
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getStats() {
    try {
      return this.cache.getStats();
    } catch (error) {
      console.error('Cache stats error:', error);
      return {};
    }
  }

  /**
   * Flush all cache data
   * @returns {boolean} Success status
   */
  flush() {
    try {
      this.cache.flushAll();
      return true;
    } catch (error) {
      console.error('Cache flush error:', error);
      return false;
    }
  }

  /**
   * Get all keys in cache
   * @returns {Array<string>} Array of cache keys
   */
  keys() {
    try {
      return this.cache.keys();
    } catch (error) {
      console.error('Cache keys error:', error);
      return [];
    }
  }

  /**
   * Get TTL for a key
   * @param {string} key - Cache key
   * @returns {number} TTL in seconds or -1 if key doesn't exist
   */
  ttl(key) {
    try {
      return this.cache.getTtl(key);
    } catch (error) {
      console.error('Cache TTL error:', error);
      return -1;
    }
  }

  /**
   * Invalidate project cache
   * @param {string} ProjectId - Project ID
   * @param {string} ParentCompanyId - Parent Company ID
   */
  async invalidateProjectCache(ProjectId, ParentCompanyId) {
    if (ProjectId) {
      await this.delPattern(`projects_company:*ProjectId*${ProjectId}*`);
      await this.delPattern(`account_projects:*ProjectId*${ProjectId}*`);
    }
    if (ParentCompanyId) {
      await this.delPattern(`projects_company:*ParentCompanyId*${ParentCompanyId}*`);
      await this.delPattern(`account_projects:*ParentCompanyId*${ParentCompanyId}*`);
    }
  }

  /**
   * Invalidate event cache
   * @param {string} id - Event ID
   * @param {string} ProjectId - Project ID
   * @param {string} ParentCompanyId - Parent Company ID
   */
  async invalidateEventCache(id, ProjectId, ParentCompanyId) {
    if (id) {
      await this.delPattern(`calendar_event:*id*${id}*`);
    }
    if (ProjectId) {
      await this.delPattern(`calendar_events:*ProjectId*${ProjectId}*`);
      await this.delPattern(`all_calendar:*ProjectId*${ProjectId}*`);
    }
    if (ParentCompanyId) {
      await this.delPattern(`calendar_events:*ParentCompanyId*${ParentCompanyId}*`);
      await this.delPattern(`all_calendar:*ParentCompanyId*${ParentCompanyId}*`);
    }
  }

  /**
   * Close cache connection (no-op for NodeCache)
   */
  async close() {
    // NodeCache doesn't need explicit closing
    console.log('NodeCache service closed');
  }
}

module.exports = new CacheService();
