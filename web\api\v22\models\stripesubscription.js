module.exports = (sequelize, DataTypes) => {
  const StripeSubscription = sequelize.define('StripeSubscription', {
    subscriptionId: DataTypes.STRING,
    UserId: {
      type: DataTypes.INTEGER,
    },
    status: DataTypes.STRING,
  });
  StripeSubscription.associate = (models) => {
    StripeSubscription.belongsTo(models.User);
    return StripeSubscription;
  };

  StripeSubscription.createInstance = async (user, subscription) => {
    const newSubscription = await StripeSubscription.create({
      UserId: user.id,
      subscriptionId: subscription.id,
      status: subscription.status,
    });

    return newSubscription;
  };

  StripeSubscription.getBy = async (attr) => {
    const stripeSubs = await StripeSubscription.findOne({
      where: { ...attr },
    });
    return stripeSubs;
  };

  StripeSubscription.prototype.updateInstance = async (id, attr) => {
    const stripeSubs = await StripeSubscription.update(attr, { where: { id } });

    return stripeSubs;
  };

  StripeSubscription.editNotify = async (attr) => {
    const stripeSubs = await StripeSubscription.findAll({
      where: { ...attr },
    });

    return stripeSubs;
  };

  return StripeSubscription;
};
