const status = require('http-status');
const {
    Sequelize,
    Utilities
} = require('../models');

const UtilitiesService = {
    async addUtilities(equData, done) {
        try {
            // await this.getDynamicModel(equData);
            const inputData = equData.body;

            const newEquipment = await Utilities.createUtilities(inputData).then(async (response) => {
                const payload = []
                if (inputData.item_details) {
                    inputData.item_details.forEach(element => {
                        let data = {}
                        data['utilityType'] = element.item_name ? element.item_name : element.Material
                        data['quantity'] = element.quantity ? element.quantity : element.Quantity
                        data['unit'] = element.unit
                        data['parentId'] = response.id
                        data['projectId'] = inputData.projectId
                        data['cost'] = element.cost ? element.cost : element.Cost
                        data['co2_emissions'] = element['CO2 Emissions'] ? element['CO2 Emissions'] : ''
                        payload.push(data)
                    })
                }
                await Utilities.bulkCreate(payload);
            });
            done(newEquipment, false);
        } catch (e) {
            done(null, e);
        }
    },
    async listUtilities(inputData, done) {
        try {
            // await this.getDynamicModel(inputData);
            const { params } = inputData;
            const incomeData = inputData.body;
            let searchCondition = {};
            const pageNumber = +params.pageNo;
            const pageSize = +params.pageSize;
            const { sort } = inputData.body;
            const { sortByField } = inputData.body;
            const offset = (pageNumber - 1) * pageSize;
            const condition = {
                isDeleted: false,
                projectId: inputData.query.projectId,
                parentId: null
            };
            const utilitiesData = await Utilities.getAll(
                condition,
                pageSize,
                offset,
                searchCondition,
                sort,
                sortByField,
            );
            if (incomeData && incomeData.isFilter) {
                if (utilitiesData.rows) {
                    if (utilitiesData.rows.length > 0) {
                        utilitiesData.rows.sort((a, b) =>
                            a.equipmentName.toLowerCase() > b.equipmentName.toLowerCase() ? 1 : -1,
                        );
                    }
                } else if (utilitiesData.length > 0) {
                    utilitiesData.sort((a, b) =>
                        a.equipmentName.toLowerCase() > b.equipmentName.toLowerCase() ? 1 : -1,
                    );
                }
            }

            done(utilitiesData, false);
        } catch (e) {
            done(null, e);
        }
    },
};
module.exports = UtilitiesService;
