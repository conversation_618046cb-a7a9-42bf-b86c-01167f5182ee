const moment = require('moment');
const { Sequelize } = require('sequelize');

const { Op } = Sequelize;
module.exports = (sequelize, DataTypes) => {
  const ConcreteRequest = sequelize.define(
    'ConcreteRequest',
    {
      description: DataTypes.STRING,
      concreteOrderNumber: DataTypes.STRING,
      truckSpacingHours: DataTypes.STRING,
      notes: DataTypes.STRING,
      slump: DataTypes.STRING,
      concreteQuantityOrdered: DataTypes.STRING,
      concreteConfirmedOn: DataTypes.DATE,
      isConcreteConfirmed: DataTypes.BOOLEAN,
      pumpLocation: DataTypes.STRING,
      pumpOrderedDate: DataTypes.STRING,
      pumpWorkStart: DataTypes.DATE,
      pumpWorkEnd: DataTypes.DATE,
      pumpConfirmedOn: DataTypes.DATE,
      isPumpConfirmed: DataTypes.BOOLEAN,
      isPumpRequired: DataTypes.BOOLEAN,
      cubicYardsTotal: DataTypes.STRING,
      hoursToCompletePlacement: DataTypes.STRING,
      minutesToCompletePlacement: DataTypes.STRING,
      isDeleted: DataTypes.BOOLEAN,
      status: DataTypes.STRING,
      ProjectId: DataTypes.INTEGER,
      publicSchemaId: DataTypes.INTEGER,
      ConcreteRequestId: DataTypes.INTEGER,
      requestType: DataTypes.STRING,
      primerForPump: DataTypes.STRING,
      concretePlacementStart: DataTypes.DATE,
      concretePlacementEnd: DataTypes.DATE,
      createdBy: DataTypes.INTEGER,
      recurrenceId: DataTypes.INTEGER,
      LocationId: DataTypes.INTEGER,
      isCreatedByGuestUser: DataTypes.BOOLEAN,
      approvedBy: DataTypes.INTEGER,
      approved_at: DataTypes.DATE,
      OriginationAddress: DataTypes.TEXT,
      vehicleType: DataTypes.TEXT,
      OriginationAddressPump: DataTypes.TEXT,
      vehicleTypePump: DataTypes.TEXT,
    },
    {},
  );

  ConcreteRequest.associate = (models) => {
    ConcreteRequest.hasMany(models.ConcreteRequestResponsiblePerson, {
      as: 'memberDetails',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.belongsTo(models.Project);
    ConcreteRequest.belongsTo(models.Member, {
      as: 'createdUserDetails',
      foreignKey: 'createdBy',
    });
    ConcreteRequest.hasMany(models.ConcreteRequestLocation, {
      as: 'locationDetails',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.hasMany(models.ConcreteRequestMixDesign, {
      as: 'mixDesignDetails',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.hasMany(models.ConcreteRequestPumpSize, {
      as: 'pumpSizeDetails',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.hasMany(models.ConcreteRequestCompany, {
      as: 'concreteSupplierDetails',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.hasMany(models.VoidList, {
      as: 'voidList',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.hasMany(models.ConcreteGate, {
      as: 'gateDetails',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.hasMany(models.ConcreteEquipment, {
      as: 'equipmentDetails',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.belongsTo(models.Member, {
      as: 'approverDetails',
      foreignKey: 'approvedBy',
    });
    ConcreteRequest.belongsTo(models.RequestRecurrenceSeries, {
      as: 'recurrence',
      foreignKey: 'recurrenceId',
    });
    ConcreteRequest.belongsTo(models.Locations, {
      as: 'location',
      foreignKey: 'LocationId',
    });
    ConcreteRequest.hasMany(models.ConcreteRequestAttachment, {
      as: 'attachments',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.hasMany(models.ConcreteRequestComment, {
      as: 'comments',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.hasMany(models.ConcreteRequestHistory, {
      as: 'history',
      foreignKey: 'ConcreteRequestId',
    });
  };
  ConcreteRequest.createInstance = async (paramData) => {
    const concreteRequest = await ConcreteRequest.create(paramData);
    return concreteRequest;
  };

  const buildSearchConditions = (commonSearch, search) => {
    if (!search) return commonSearch;

    return {
      [Op.and]: [
        {
          ...commonSearch,
          [Op.or]: [
            { description: { [Sequelize.Op.iLike]: `%${search}%` } },
            { concreteOrderNumber: { [Sequelize.Op.iLike]: `%${search}%` } },
            { slump: { [Sequelize.Op.iLike]: `%${search}%` } },
            { primerForPump: { [Sequelize.Op.iLike]: `%${search}%` } },
            { status: { [Sequelize.Op.iLike]: `%${search}%` } },
            { pumpLocation: { [Sequelize.Op.iLike]: `%${search}%` } },
            { cubicYardsTotal: { [Sequelize.Op.iLike]: `%${search}%` } },
            { concreteQuantityOrdered: { [Sequelize.Op.iLike]: `%${search}%` } },
            { truckSpacingHours: { [Sequelize.Op.iLike]: `%${search}%` } },
            { notes: { [Sequelize.Op.iLike]: `%${search}%` } },
            { hoursToCompletePlacement: { [Sequelize.Op.iLike]: `%${search}%` } },
            { minutesToCompletePlacement: { [Sequelize.Op.iLike]: `%${search}%` } },
            { '$location.locationPath$': { [Sequelize.Op.iLike]: `%${search}%` } },
            { '$mixDesignDetails.ConcreteMixDesign.mixDesign$': { [Sequelize.Op.iLike]: `%${search}%` } },
            { '$pumpSizeDetails.ConcretePumpSize.pumpSize$': { [Sequelize.Op.iLike]: `%${search}%` } },
            { '$concreteSupplierDetails.Company.companyName$': { [Sequelize.Op.iLike]: `%${search}%` } }
          ]
        }
      ]
    };
  };

  const timeToSeconds = (timeString) => {
    const [hours, minutes, seconds] = timeString.split(':');
    return +hours * 60 * 60 + +minutes * 60 + +seconds;
  };

  const buildDateRangeCondition = (startDate, endDate, timezoneOffset, eventStartTime, eventEndTime, singleQuery) => {
    const startDateTime = moment(startDate, 'YYYY-MM-DD').format('YYYY-MM-DD');
    const endDateTime = moment(endDate, 'YYYY-MM-DD').format('YYYY-MM-DD');
    const nextDay = moment(startDate).add(1, 'days');
    const queryStartDate = nextDay.format('YYYY-MM-DD');

    // Convert timezone offset from minutes to PostgreSQL format (e.g., 330 -> '+05:30')
    const convertTimezoneOffset = (offsetMinutes) => {
      if (!offsetMinutes) return 'UTC';
      const totalMinutes = Math.abs(offsetMinutes);
      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;
      const sign = offsetMinutes >= 0 ? '+' : '-';
      return `${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    };

    const timezone = convertTimezoneOffset(timezoneOffset);

    if (singleQuery) {
      return {
        [Op.and]: [
          sequelize.literal(`(DATE_TRUNC('day', "ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}') BETWEEN '${startDateTime}' AND '${endDateTime}'
            AND ("ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
            AND ("ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}')::time <= '${eventEndTime}')`)
        ]
      };
    }

    return {
      [Op.or]: [
        sequelize.literal(`(DATE_TRUNC('day', "ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}') BETWEEN '${startDateTime}' AND '${endDateTime}'
          AND ("ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
          AND ("ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}')::time <= '23:59:59')`),
        sequelize.literal(`(DATE_TRUNC('day', "ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}') BETWEEN '${queryStartDate}' AND '${endDateTime}'
          AND ("ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}')::time >= '00:00:00'
          AND ("ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}')::time <= '${eventEndTime}')`)
      ]
    };
  };

  const buildFilterConditions = (commonSearch, filters) => {
    const {
      company: companyFilter,
      status: statusFilter,
      member: memberFilter,
      location: locationFilter
    } = filters;

    let conditions = { ...commonSearch };

    if (companyFilter) {
      conditions = {
        [Op.and]: [
          {
            ...conditions,
            [Op.or]: [{ '$concreteSupplierDetails.Company.id$': +companyFilter }]
          }
        ]
      };
    }

    if (statusFilter) {
      const statusMapping = {
        'Pending': 'Tentative',
        'Delivered': 'Completed'
      };
      const mappedStatus = statusMapping[statusFilter] || statusFilter;
      conditions = {
        [Op.and]: [
          {
            ...conditions,
            [Op.or]: [{ status: mappedStatus }]
          }
        ]
      };
    }

    if (memberFilter > 0) {
      conditions = {
        [Op.and]: [
          {
            ...conditions,
            [Op.or]: [{ '$memberDetails.Member.id$': +memberFilter }]
          }
        ]
      };
    }

    if (locationFilter !== 0) {
      conditions = {
        [Op.and]: [
          {
            ...conditions,
            [Op.or]: [{ '$location.id$': { [Op.eq]: locationFilter } }]
          }
        ]
      };
    }

    return conditions;
  };

  const buildOrderQuery = (sortByFieldName, sortByColumnType) => {
    const sortType = String(sortByColumnType || 'DESC');

    let orderQuery;
    if (sortByFieldName === 'member') {
      orderQuery = [['memberDetails', 'Member', 'User', 'firstName', sortType]];
    } else if (sortByFieldName === 'company') {
      orderQuery = [['concreteSupplierDetails', 'Company', 'companyName', sortType]];
    } else if (sortByFieldName === 'approvedUser') {
      orderQuery = [['approverDetails', 'User', 'firstName', sortType]];
    } else if (sortByFieldName === 'pumpsize') {
      orderQuery = [['pumpSizeDetails', 'ConcretePumpSize', 'pumpSize', sortType]];
    } else if (sortByFieldName === 'mixDesign') {
      orderQuery = [['mixDesignDetails', 'ConcreteMixDesign', 'mixDesign', sortType]];
    } else if (
      sortByFieldName === 'description' ||
      sortByFieldName === 'id' ||
      sortByFieldName === 'status' ||
      sortByFieldName === 'slump' ||
      sortByFieldName === 'concreteOrderNumber' ||
      sortByFieldName === 'truckSpacingHours' ||
      sortByFieldName === 'primerForPump' ||
      sortByFieldName === 'concreteQuantityOrdered' ||
      sortByFieldName === 'concretePlacementStart'
    ) {
      orderQuery = [[sortByFieldName, sortType]];
    } else {
      // Default fallback
      orderQuery = [['id', sortType]];
    }

    return orderQuery;
  };

  ConcreteRequest.getAll = async (options) => {
    const {
      req,
      attr = {},
      filters = {},
      sort = {}
    } = options;

    const {
      description: descriptionFilter,
      location: locationFilter,
      concreteSupplier: concreteSupplierFilter,
      orderNumber: orderNumberFilter,
      status: statusFilter,
      mixDesign: mixDesignFilter,
      member: memberFilter,
      search
    } = filters;

    const {
      order,
      sort: sortType,
      sortColumn
    } = sort;

    // Extract date range from attr.concretePlacementStart if available
    let startdate, enddate;
    if (attr.concretePlacementStart && attr.concretePlacementStart[Op.between]) {
      const [startDate, endDate] = attr.concretePlacementStart[Op.between];
      startdate = moment(startDate).format('YYYY-MM-DD');
      enddate = moment(endDate).format('YYYY-MM-DD');
    }

    const sortByFieldName = sortColumn || 'id';
    let sortByColumnType = sortType || 'DESC';
    if (order) {
      sortByColumnType = order;
    }

    const orderQuery = buildOrderQuery(sortByFieldName, sortByColumnType);

    let commonSearch;

    if (descriptionFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...attr,
            isDeleted: false,
            [Op.or]: [{ description: { [Sequelize.Op.iLike]: `%${descriptionFilter}%` } }]
          }
        ]
      };
    } else {
      commonSearch = {
        ...attr,
        isDeleted: false
      };
    }

    if (locationFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ '$location.id$': { [Op.eq]: locationFilter } }]
          }
        ]
      };
    }

    if (req.body.locationPathFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ '$location.locationPath$': { [Sequelize.Op.iLike]: req.body.locationPathFilter } }]
          }
        ]
      };
    }

    // Use default time range if eventStartTime/eventEndTime are not provided
    const eventStartTime = req.body.eventStartTime || '00:00:00';
    const eventEndTime = req.body.eventEndTime || '23:59:59';

    const finalFromTimeSeconds = timeToSeconds(eventStartTime);
    const finalToTimeSeconds = timeToSeconds(eventEndTime);
    const singleQuery = finalFromTimeSeconds <= finalToTimeSeconds;

    let dateRangeCondition = {};

    // Only apply date range condition if we have valid dates
    if (startdate && enddate) {
      dateRangeCondition = buildDateRangeCondition(
        startdate,
        enddate,
        req.headers.timezoneoffset,
        eventStartTime,
        eventEndTime,
        singleQuery
      );
    }

    commonSearch = {
      [Op.and]: [
        commonSearch,
        dateRangeCondition
      ]
    };

    if (concreteSupplierFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ '$concreteSupplierDetails.Company.companyName$': { [Sequelize.Op.iLike]: `%${concreteSupplierFilter}%` } }]
          }
        ]
      };
    }

    if (orderNumberFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ concreteOrderNumber: { [Sequelize.Op.iLike]: `%${orderNumberFilter}%` } }]
          }
        ]
      };
    }

    if (statusFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ status: statusFilter }]
          }
        ]
      };
    }

    if (memberFilter > 0) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ '$memberDetails.Member.id$': +memberFilter }]
          }
        ]
      };
    }

    if (mixDesignFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ '$mixDesignDetails.ConcreteMixDesign.mixDesign$': { [Sequelize.Op.iLike]: `%${mixDesignFilter}%` } }]
          }
        ]
      };
    }

    commonSearch = buildSearchConditions(commonSearch, search);

    const memberResponsibleCondition = { isDeleted: false };
    const requiredCondition = false;

    return await ConcreteRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          required: requiredCondition,
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              where: memberResponsibleCondition,
              attributes: ['id', 'UserId'],
              include: [
                {
                  association: 'User',
                  where: { isDeleted: false },
                  attributes: ['id', 'email', 'firstName', 'lastName']
                }
              ]
            }
          ]
        },
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }]
        },
        {
          required: false,
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }]
        },
        {
          association: 'Project',
          where: { isDeleted: false },
          attributes: ['id', 'projectName']
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              where: { isDeleted: false },
              attributes: ['email', 'id', 'firstName', 'lastName']
            }
          ]
        },
        {
          association: 'locationDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteLocation', attributes: ['location', 'id'] }]
        },
        {
          association: 'mixDesignDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteMixDesign', attributes: ['mixDesign', 'id'] }]
        },
        {
          association: 'pumpSizeDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcretePumpSize', attributes: ['pumpSize', 'id'] }]
        },
        {
          association: 'concreteSupplierDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }]
        },
        {
          association: 'voidList',
          attributes: ['id', 'MemberId', 'ProjectId', 'ConcreteRequestId']
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName']
            }
          ]
        },
        {
          association: 'location',
          required: false,
          attributes: ['id', 'locationPath']
        }
      ],
      where: commonSearch,
      attributes: [
        'id', 'description', 'concreteOrderNumber', 'truckSpacingHours',
        'notes', 'slump', 'concreteQuantityOrdered', 'concreteConfirmedOn',
        'isConcreteConfirmed', 'pumpLocation', 'pumpOrderedDate', 'pumpWorkStart',
        'pumpWorkEnd', 'pumpConfirmedOn', 'isPumpConfirmed', 'isPumpRequired',
        'cubicYardsTotal', 'hoursToCompletePlacement', 'minutesToCompletePlacement',
        'status', 'ProjectId', 'ConcreteRequestId', 'requestType', 'primerForPump',
        'concretePlacementStart', 'concretePlacementEnd', 'createdBy',
        'isCreatedByGuestUser'
      ],
      order: orderQuery
    });
  };

  ConcreteRequest.getSingleConcreteRequestData = async (attr) => {
    const newConcreteRequest = await ConcreteRequest.findOne({
      subQuery: false,
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              where: { isDeleted: false },
              attributes: ['id', 'isGuestUser'],
              include: [
                {
                  association: 'User',
                  where: { isDeleted: false },
                  attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              where: { isDeleted: false },
              attributes: ['email', 'id', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          required: false,
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          required: false,
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          association: 'locationDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteLocation', attributes: ['location', 'id'] }],
        },
        {
          association: 'mixDesignDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteMixDesign', attributes: ['mixDesign', 'id'] }],
        },
        {
          association: 'pumpSizeDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcretePumpSize', attributes: ['pumpSize', 'id'] }],
        },
        {
          association: 'concreteSupplierDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              attributes: ['companyName', 'id'],
            },
          ],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
            'chosenDateOfMonthValue',
          ],
        },
        {
          association: 'location',
          required: false,
          attributes: ['id', 'locationPath'],
        },
      ],
      where: { ...attr, isDeleted: false },
      attributes: [
        'id',
        'description',
        'concreteOrderNumber',
        'truckSpacingHours',
        'notes',
        'slump',
        'concreteQuantityOrdered',
        'concreteConfirmedOn',
        'isConcreteConfirmed',
        'pumpLocation',
        'pumpOrderedDate',
        'pumpWorkStart',
        'pumpWorkEnd',
        'pumpConfirmedOn',
        'isPumpConfirmed',
        'isPumpRequired',
        'cubicYardsTotal',
        'hoursToCompletePlacement',
        'minutesToCompletePlacement',
        'status',
        'ProjectId',
        'ConcreteRequestId',
        'requestType',
        'primerForPump',
        'concretePlacementStart',
        'concretePlacementEnd',
        'createdBy',
        'isCreatedByGuestUser',
        'OriginationAddress',
        'vehicleType',
        'OriginationAddressPump',
        'vehicleTypePump',
      ],
    });
    return newConcreteRequest;
  };

  ConcreteRequest.upcomingConcreteRequest = async (condition, ProjectId) => {
    const commonSearch = {
      isDeleted: false,
      ProjectId,
      concretePlacementStart: { [Op.gt]: new Date() },
      requestType: 'concreteRequest',
      ...condition,
    };
    const CraneRequestData = await ConcreteRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'UserId'],
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'concreteSupplierDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              attributes: ['companyName', 'id'],
            },
          ],
        },
        {
          association: 'locationDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteLocation', attributes: ['location', 'id'] }],
        },
        {
          association: 'location',
          required: false,
          attributes: ['id', 'locationPath'],
        },
      ],
      where: commonSearch,
      attributes: [
        'id',
        'description',
        'concreteOrderNumber',
        'truckSpacingHours',
        'notes',
        'slump',
        'concreteQuantityOrdered',
        'concreteConfirmedOn',
        'isConcreteConfirmed',
        'pumpLocation',
        'pumpOrderedDate',
        'pumpWorkStart',
        'pumpWorkEnd',
        'pumpConfirmedOn',
        'isPumpConfirmed',
        'isPumpRequired',
        'cubicYardsTotal',
        'hoursToCompletePlacement',
        'minutesToCompletePlacement',
        'status',
        'ProjectId',
        'ConcreteRequestId',
        'requestType',
        'primerForPump',
        'concretePlacementStart',
        'concretePlacementEnd',
        'createdBy',
        'isCreatedByGuestUser',
      ],
      order: orderQuery,
    });
    return CraneRequestData;
  };

  ConcreteRequest.getWeeklyCalendarList = async (options) => {
    const {
      attr = {},
      memberId,
      filters = {},
      dateRange = {},
      timezone,
      eventStartTime,
      eventEndTime
    } = options;

    const {
      company: companyFilter,
      status: statusFilter,
      member: memberFilter,
      location: locationFilter
    } = filters;

    const {
      startDate,
      endDate
    } = dateRange;

    let commonSearch;

    const finalFromTimeSeconds = timeToSeconds(eventStartTime);
    const finalToTimeSeconds = timeToSeconds(eventEndTime);
    const singleQuery = finalFromTimeSeconds <= finalToTimeSeconds;

    const startDate1 = startDate;
    const endDate1 = endDate;

    commonSearch = buildDateRangeCondition(
      startDate1,
      endDate1,
      timezone,
      eventStartTime,
      eventEndTime,
      singleQuery
    );

    commonSearch = buildFilterConditions(commonSearch, {
      company: companyFilter,
      status: statusFilter,
      member: memberFilter,
      location: locationFilter
    });

    const memberResponsibleCondition = memberId === 4 ? { isDeleted: false, id: memberId } : { isDeleted: false };
    const requiredCondition = memberId === 4;

    return await ConcreteRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          required: requiredCondition,
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              where: memberResponsibleCondition,
              attributes: ['id', 'UserId'],
              include: [
                {
                  association: 'User',
                  where: { isDeleted: false },
                  attributes: ['id', 'email', 'firstName', 'lastName']
                }
              ]
            }
          ]
        },
        {
          association: 'Project',
          where: { isDeleted: false },
          attributes: ['id', 'projectName']
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              where: { isDeleted: false },
              attributes: ['email', 'id', 'firstName', 'lastName']
            }
          ]
        },
        {
          association: 'locationDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteLocation', attributes: ['location', 'id'] }]
        },
        {
          association: 'mixDesignDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteMixDesign', attributes: ['mixDesign', 'id'] }]
        },
        {
          association: 'pumpSizeDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcretePumpSize', attributes: ['pumpSize', 'id'] }]
        },
        {
          association: 'concreteSupplierDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }]
        },
        {
          association: 'voidList',
          attributes: ['id', 'MemberId', 'ProjectId', 'ConcreteRequestId']
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName']
            }
          ]
        },
        {
          association: 'location',
          required: false,
          attributes: ['id', 'locationPath']
        }
      ],
      where: { ...attr, ...commonSearch },
      attributes: [
        'id', 'description', 'concreteOrderNumber', 'truckSpacingHours',
        'notes', 'slump', 'concreteQuantityOrdered', 'concreteConfirmedOn',
        'isConcreteConfirmed', 'pumpLocation', 'pumpOrderedDate', 'pumpWorkStart',
        'pumpWorkEnd', 'pumpConfirmedOn', 'isPumpConfirmed', 'isPumpRequired',
        'cubicYardsTotal', 'hoursToCompletePlacement', 'minutesToCompletePlacement',
        'status', 'ProjectId', 'ConcreteRequestId', 'requestType', 'primerForPump',
        'concretePlacementStart', 'concretePlacementEnd', 'createdBy',
        'isCreatedByGuestUser'
      ]
    });
  };

  ConcreteRequest.upcomingConcreteRequestForMobile = async (condition, ProjectId) => {
    const commonSearch = {
      isDeleted: false,
      ProjectId,
      concretePlacementStart: { [Op.gt]: new Date() },
      requestType: 'concreteRequest',
      ...condition,
    };
    const CraneRequestData = await ConcreteRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'UserId'],
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              where: { isDeleted: false },
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'locationDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteLocation', attributes: ['location', 'id'] }],
        },
        {
          association: 'mixDesignDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteMixDesign', attributes: ['mixDesign', 'id'] }]
        },
        {
          association: 'pumpSizeDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcretePumpSize', attributes: ['pumpSize', 'id'] }]
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'concreteSupplierDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              attributes: ['companyName', 'id'],
            },
          ],
        },
        {
          association: 'location',
          required: false,
          attributes: ['id', 'locationPath'],
        },
        {
          association: 'locationDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteLocation', attributes: ['location', 'id'] }],
        },
      ],
      where: commonSearch,
      limit: 2,
      attributes: [
        'id',
        'description',
        'concreteOrderNumber',
        'truckSpacingHours',
        'notes',
        'slump',
        'concreteQuantityOrdered',
        'concreteConfirmedOn',
        'isConcreteConfirmed',
        'pumpLocation',
        'pumpOrderedDate',
        'pumpWorkStart',
        'pumpWorkEnd',
        'pumpConfirmedOn',
        'isPumpConfirmed',
        'isPumpRequired',
        'cubicYardsTotal',
        'hoursToCompletePlacement',
        'minutesToCompletePlacement',
        'status',
        'ProjectId',
        'ConcreteRequestId',
        'requestType',
        'primerForPump',
        'concretePlacementStart',
        'concretePlacementEnd',
        'createdBy',
        'isCreatedByGuestUser',
      ],
      order: [['concretePlacementStart', 'ASC']],
    });
    return CraneRequestData;
  };
  ConcreteRequest.guestSingleConcreteRequestData = async (attr) => {
    const newConcreteRequest = await ConcreteRequest.findOne({
      subQuery: false,
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              where: { isDeleted: false },
              attributes: ['id'],
              include: [
                {
                  association: 'User',
                  where: { isDeleted: false },
                  attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              where: { isDeleted: false },
              attributes: ['email', 'id', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'locationDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteLocation', attributes: ['location', 'id'] }],
        },
        {
          association: 'mixDesignDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteMixDesign', attributes: ['mixDesign', 'id'] }],
        },
        {
          association: 'pumpSizeDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcretePumpSize', attributes: ['pumpSize', 'id'] }],
        },
        {
          association: 'concreteSupplierDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              attributes: ['companyName', 'id'],
            },
          ],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
            'chosenDateOfMonthValue',
          ],
        },
        {
          association: 'location',
          required: false,
          attributes: ['id', 'locationPath'],
        },
        {
          association: 'comments',
          required: false,
          include: [
            {
              association: 'Member',
              attributes: ['id'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'firstName', 'lastName', 'profilePic'],
                },
              ],
            },
          ],
          order: [['id', 'DESC']],
        },
        {
          association: 'attachments',
          required: false,
          where: { isDeleted: false },
        },
        {
          association: 'history',
          required: false,
          include: [
            {
              association: 'Member',
              include: [
                { association: 'User', attributes: ['firstName', 'lastName', 'profilePic'] },
              ],
            },
          ],
          order: [['id', 'DESC']],
        },
      ],
      where: { ...attr, isDeleted: false },
      attributes: [
        'id',
        'description',
        'concreteOrderNumber',
        'truckSpacingHours',
        'notes',
        'slump',
        'concreteQuantityOrdered',
        'concreteConfirmedOn',
        'isConcreteConfirmed',
        'pumpLocation',
        'pumpOrderedDate',
        'pumpWorkStart',
        'pumpWorkEnd',
        'pumpConfirmedOn',
        'isPumpConfirmed',
        'isPumpRequired',
        'cubicYardsTotal',
        'hoursToCompletePlacement',
        'minutesToCompletePlacement',
        'status',
        'ProjectId',
        'ConcreteRequestId',
        'requestType',
        'primerForPump',
        'concretePlacementStart',
        'concretePlacementEnd',
        'createdBy',
        'isCreatedByGuestUser',
      ],
    });
    return newConcreteRequest;
  };

  return ConcreteRequest;
};
