module.exports = (sequelize, DataTypes) => {
  const BookingTemplates = sequelize.define(
    'BookingTemplates',
    {
      template_name: DataTypes.STRING,
      description: DataTypes.STRING,
      responsible_company: DataTypes.STRING,
      responsible_person: DataTypes.STRING,
      date: DataTypes.DATE,
      from_time: DataTypes.DATE,
      to_time: DataTypes.DATE,
      time_zone: DataTypes.INTEGER,
      location: DataTypes.INTEGER,
      notes: DataTypes.TEXT,
      picking_from: DataTypes.TEXT,
      picking_to: DataTypes.TEXT,
      is_escort_needed: DataTypes.BOOLEAN,
      dfow: DataTypes.STRING,
      gate: DataTypes.STRING,
      equipment: DataTypes.STRING,
      recurrence: DataTypes.TEXT,
      additional_location: DataTypes.TEXT,
      concrete_details: DataTypes.TEXT,
      pump_details: DataTypes.TEXT,
      template_type: DataTypes.STRING,
      isDeleted: DataTypes.BOOLEAN,
      ProjectId: DataTypes.INTEGER,
      ParentCompanyId: DataTypes.INTEGER,
      originationAddress: DataTypes.TEXT,
      vehicleType: DataTypes.TEXT,
      createdBy: {
        type: DataTypes.INTEGER,
        references: {
          model: 'Users', // name of Target model
          key: 'id', // key in Target model that we're referencing
        },
      },
    },
    {},
  );
  BookingTemplates.associate = (models) => {
    BookingTemplates.belongsTo(models.User, {
      as: 'createdUser',
      foreignKey: 'createdBy',
    });
    return BookingTemplates;
  };

  BookingTemplates.createTempalte = async (data) => {
    const newTemplate = await BookingTemplates.create(data);
    return newTemplate;
  };

  BookingTemplates.get = async (attr, limit, offset, sort, sortColumn) => {
    let templates;
    const sortByFieldName = sortColumn || 'id';
    const sortByColumnType = sort || 'DESC';
    if (limit === 0) {
      templates = await BookingTemplates.findAll({
        where: { ...attr },
        include: [
          { association: 'createdUser', attributes: ['id', 'email', 'firstName', 'lastName'] },
        ],
        order: [[`${sortByFieldName}`, `${sortByColumnType}`]],
      });
    } else {
      templates = await BookingTemplates.findAndCountAll({
        where: { ...attr },
        include: [
          { association: 'createdUser', attributes: ['id', 'email', 'firstName', 'lastName'] },
        ],
        limit,
        offset,
        order: [[`${sortByFieldName}`, `${sortByColumnType}`]],
      });
    }
    return templates;
  }

  BookingTemplates.updateInstance = async (id, attributes) => {
    const template = await BookingTemplates.update(attributes, { where: { id } });
    return template;
  };

  BookingTemplates.getTemplates = async (attr, sort, sortColumn) => {
    let templates;
    const sortByFieldName = sortColumn || 'id';
    const sortByColumnType = sort || 'DESC';
    templates = await BookingTemplates.findAndCountAll({
      where: { ...attr },
      include: [
        { association: 'createdUser', attributes: ['id', 'email', 'firstName', 'lastName'] },
      ],
      order: [[`${sortByFieldName}`, `${sortByColumnType}`]],
    });
    return templates;
  }

  return BookingTemplates;
};
