module.exports = (sequelize, DataTypes) => {
  const AccountProject = sequelize.define(
    'AccountProject',
    {
      ProjectId: DataTypes.INTEGER,
      UserId: DataTypes.INTEGER,
      isActive: DataTypes.BOOLEAN,
      isDeleted: DataTypes.BOOLEAN,
      EnterpriseId: DataTypes.INTEGER,
      status: DataTypes.STRING,
    },
    {},
  );
  AccountProject.associate = (models) => {
    // associations can be defined here
    AccountProject.belongsTo(models.User);
    AccountProject.belongsTo(models.Project);
    AccountProject.belongsTo(models.Enterprise);
  };
  AccountProject.createMultipleInstance = async (paramData) => {
    const newAccountProject = await AccountProject.bulkCreate(paramData);
    return newAccountProject;
  };
  AccountProject.createInstance = async (paramData) => {
    const newAccountProject = await AccountProject.create(paramData);
    return newAccountProject;
  };
  return AccountProject;
};
