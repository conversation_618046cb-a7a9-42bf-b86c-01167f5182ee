module.exports = (sequelize, DataTypes) => {
    const WasteLog = sequelize.define(
        'WasteLog',
        {
            hauler: DataTypes.STRING,
            wasteType: DataTypes.STRING,
            quantity: DataTypes.STRING,
            landfillDestination: DataTypes.STRING,
            haulVehicle: DataTypes.STRING,
            wasteTickets: DataTypes.STRING,
            projectId: DataTypes.INTEGER
        },
        {},
    );
    WasteLog.createWasteLog = async (equipmentData) => {
        const newData = await WasteLog.create(equipmentData);
        return newData;
    };
    WasteLog.getAll = async (attr, limit, offset, searchCondition, sort, sortColumn) => {
        let WasteLogs;
        const sortByFieldName = sortColumn || 'id';
        const sortByColumnType = sort || 'DESC';

        WasteLogs = await WasteLog.findAndCountAll({
            where: { ...attr, ...searchCondition },
            limit,
            offset,
            order: [[`${sortByFieldName}`, `${sortByColumnType}`]]
        });

        return WasteLogs;
    };
    return WasteLog;
};