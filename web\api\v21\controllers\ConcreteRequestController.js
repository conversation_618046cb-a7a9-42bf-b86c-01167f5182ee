const status = require('http-status');
const { concreteRequestService } = require('../services');
const { ProjectSettings } = require('../models');

const ConcreteRequestController = {
  async createConcreteRequest(req, res, next) {
    try {
      await concreteRequestService.newConcreteRequest(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Concrete Booking Created Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async editConcreteRequest(req, res, next) {
    try {
      await concreteRequestService.editConcreteRequest(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Concrete Booking Updated Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getConcreteRequestList(req, res, next) {
    try {
      await concreteRequestService.listConcreteBookingRequest(req, async (response, error) => {
        if (error) {
          next(error);
        } else {
          let response1;
          if (req.params.ProjectId !== '') {
            response1 = await ProjectSettings.getCalendarStatusColor(req.params.ProjectId);
          }
          res.status(status.OK).json({
            message: 'Concrete Booking listed Successfully.',
            data: response,
            statusData: response1,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getConcreteRequestData(req, res, next) {
    try {
      await concreteRequestService.getConcreteRequestData(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Concrete Booking listed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async updateConcreteRequestStatus(req, res, next) {
    try {
      await concreteRequestService.updateConcreteRequestStatus(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          const statusMessage = req.body.status;
          res.status(status.OK).json({
            message: `${statusMessage} Successfully.`,
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getLastConcreteRequestId(req, res, next) {
    try {
      await concreteRequestService.lastConcreteRequest(req, (lastDetail, error1) => {
        if (!error1) {
          res.status(status.OK).json({
            message: 'Delivery Booking Last Id Viewed Successfully.',
            lastId: lastDetail,
          });
        } else {
          next(error1);
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getSingleConcreteRequest(req, res, next) {
    try {
      await concreteRequestService.getSingleConcreteRequest(req, (response, error1) => {
        if (!error1) {
          res.status(status.OK).json({
            message: 'Concrete Booking Viewed Successfully.',
            data: response,
          });
        } else {
          next(error1);
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async upcomingConcreteRequest(req, res, next) {
    try {
      const concreteRequestData = await concreteRequestService.upcomingConcreteRequest(req);
      if (concreteRequestData.status === 200) {
        res.status(status.OK).json({
          message: 'Upcoming Concrete Booking Viewed Successfully.',
          data: concreteRequestData.data,
        });
      } else {
        res.status(status.UNPROCESSABLE_ENTITY).json({
          message: concreteRequestData.msg,
          data: [],
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async upcomingRequestList(req, res, next) {
    try {
      const concreteRequestData = await concreteRequestService.upcomingRequestList(req);
      if (concreteRequestData.status === 200) {
        res.status(status.OK).json({
          message: 'Upcoming Concrete Booking Viewed Successfully.',
          data: concreteRequestData.data,
        });
      } else {
        res.status(status.UNPROCESSABLE_ENTITY).json({
          message: concreteRequestData.msg,
          data: [],
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async getConcreteDropdownDetail(req, res, next) {
    try {
      const getConcreteDropdownDetail = await concreteRequestService.getConcreteDropdownDetail(req);
      if (getConcreteDropdownDetail.status === 200) {
        res.status(status.OK).json({
          message: 'Concrete Booking Dropdown Details Viewed Successfully.',
          data: getConcreteDropdownDetail.data,
        });
      } else {
        res.status(status.UNPROCESSABLE_ENTITY).json({
          message: getConcreteDropdownDetail.msg,
          data: [],
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async deleteConcreteRequest(req, res, next) {
    try {
      await concreteRequestService.deleteConcreteRequest(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Concrete Booking Deleted Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async editMultipleDeliveryRequest(req, res, next) {
    try {
      const editDeliveryRequests = await concreteRequestService.editMultipleDeliveryRequest(req);
      if (editDeliveryRequests) {
        res.status(status.OK).json({
          status: 200,
          message: 'concrete Booking Updated Successfully.',
        });
      } else {
        res.status(status.UNPROCESSABLE_ENTITY).json({
          status: 422,
          message: 'Cannot able to update concrete booking.',
        });
      }
    } catch (e) {
      next(e);
    }
  },
};

module.exports = ConcreteRequestController;
