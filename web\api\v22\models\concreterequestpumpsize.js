module.exports = (sequelize, DataTypes) => {
  const ConcreteRequestPumpSize = sequelize.define(
    'ConcreteRequestPumpSize',
    {
      ConcreteRequestId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      ConcreteRequestCode: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ConcretePumpSizeId: DataTypes.INTEGER,
      ProjectId: DataTypes.INTEGER,
    },
    {},
  );
  ConcreteRequestPumpSize.associate = (models) => {
    ConcreteRequestPumpSize.belongsTo(models.ConcreteRequest, {
      as: 'concreteRequest',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequestPumpSize.belongsTo(models.ConcretePumpSize);
  };
  ConcreteRequestPumpSize.createInstance = async (paramData) => {
    const newConcreteRequestPumpSize = await ConcreteRequestPumpSize.create(paramData);
    return newConcreteRequestPumpSize;
  };
  return ConcreteRequestPumpSize;
};
