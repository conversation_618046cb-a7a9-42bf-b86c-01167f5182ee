const moment = require('moment');

const excelCraneReportService = {
  async craneReport(workbook, responseData, selectedHeaders, timezoneoffset) {
    const worksheet = workbook.addWorksheet('Crane Report');
    /* Note */
    /* Column headers */
    const rowValues = [];
    const columns = [];
    let isIdSelected = false;
    let isDescriptionSelected = false;
    let isDateSelected = false;
    let isStatusSelected = false;
    let isApprovedBySelected = false;
    let isEquipmentSelected = false;
    let isDfowSelected = false;
    let isGateSelected = false;
    let isCompanySelected = false;
    let isPersonSelected = false;
    let isPickingFromSelected = false;
    let isPickingToSelected = false;
    let isLocationSelected = false;
    selectedHeaders.map((object) => {
      if (object.isActive === true) {
        rowValues.push(object.title);
        if (object.key === 'id') {
          columns.push({ key: object.key, width: 5 });
        } else {
          columns.push({ key: object.key, width: 32 });
        }
        if (object.key === 'id') isIdSelected = true;
        if (object.key === 'description') isDescriptionSelected = true;
        if (object.key === 'date') isDateSelected = true;
        if (object.key === 'status') isStatusSelected = true;
        if (object.key === 'approvedby') isApprovedBySelected = true;
        if (object.key === 'equipment') isEquipmentSelected = true;
        if (object.key === 'dfow') isDfowSelected = true;
        if (object.key === 'gate') isGateSelected = true;
        if (object.key === 'company') isCompanySelected = true;
        if (object.key === 'name') isPersonSelected = true;
        if (object.key === 'pickingFrom') isPickingFromSelected = true;
        if (object.key === 'pickingTo') isPickingToSelected = true;
        if (object.key === 'location') isLocationSelected = true;
      }
      return object;
    });

    worksheet.getRow(1).values = rowValues;
    worksheet.columns = columns;
    const cellRange = {
      0: 'A',
      1: 'B',
      2: 'C',
      3: 'D',
      4: 'E',
      5: 'F',
      6: 'G',
      7: 'H',
      8: 'I',
      9: 'J',
      10: 'K',
      11: 'L',
      12: 'M',
    };
    // Insert data into worksheet
    for (let index = 1; index <= responseData.length; index += 1) {
      worksheet.addRow();
      if (isIdSelected) {
        const cellValue = cellRange[rowValues.indexOf('Id')];
        worksheet.getCell(`${cellValue}${index + 1}`).value =
          responseData[index - 1].CraneRequestId;
      }
      if (isDescriptionSelected) {
        const cellValue = cellRange[rowValues.indexOf('Description')];
        worksheet.getCell(`${cellValue}${index + 1}`).value = responseData[index - 1].description;
      }
      if (isDateSelected) {
        const cellValue = cellRange[rowValues.indexOf('Date & Time')];
        const current = responseData[index - 1];

        if (current.requestType === 'craneRequest') {
          const start = moment(current.craneDeliveryStart).add(Number(timezoneoffset), 'minutes');
          const end = moment(current.craneDeliveryEnd).add(Number(timezoneoffset), 'minutes');

          worksheet.getCell(`${cellValue}${index + 1}`).value =
            `${start.format('MM/DD/YYYY hh:mm a')} - ${end.format('hh:mm a')}`;
        } else {
          const start = moment(current.deliveryStart).add(Number(timezoneoffset), 'minutes');
          const end = moment(current.deliveryEnd).add(Number(timezoneoffset), 'minutes');

          worksheet.getCell(`${cellValue}${index + 1}`).value =
            `${start.format('MM/DD/YYYY hh:mm a')} - ${end.format('hh:mm a')}`;
        }
      }

      if (isStatusSelected) {
        const cellValue = cellRange[rowValues.indexOf('Status')];
        worksheet.getCell(`${cellValue}${index + 1}`).value = responseData[index - 1].status;
      }
      if (isApprovedBySelected) {
        const cellValue = cellRange[rowValues.indexOf('Approved By')];
        worksheet.getCell(`${cellValue}${index + 1}`).value =
          responseData[index - 1].approverDetails &&
            responseData[index - 1].approverDetails.User.firstName
            ? `${responseData[index - 1].approverDetails.User.firstName} ${responseData[index - 1].approverDetails.User.lastName
            }`
            : '-';
      }
      if (isEquipmentSelected) {
        // const cellValue = cellRange[rowValues.indexOf('Equipment')];
        // worksheet.getCell(`${cellValue}${index + 1}`).value =
        //   responseData[index - 1].equipmentDetails && responseData[index - 1].equipmentDetails[0]
        //     ? responseData[index - 1].equipmentDetails[0].Equipment.equipmentName
        //     : '-';

        const cellValue = cellRange[rowValues.indexOf('Equipment')];
        if (
          responseData[index - 1].equipmentDetails &&
          responseData[index - 1].equipmentDetails.length > 0
        ) {
          const equipmentValues = [];
          for (let m = 0; m < responseData[index - 1].equipmentDetails.length; m += 1) {
            if (
              responseData[index - 1].equipmentDetails &&
              responseData[index - 1].equipmentDetails[m] &&
              responseData[index - 1].equipmentDetails[m].Equipment &&
              responseData[index - 1].equipmentDetails[m].Equipment.PresetEquipmentType
            ) {
              if (
                responseData[index - 1].equipmentDetails[m].Equipment.PresetEquipmentType
                  .isCraneType
              ) {
                equipmentValues.push(
                  responseData[index - 1].equipmentDetails[m].Equipment.equipmentName,
                );
              }
            }
          }
          const equipment = equipmentValues.join(', ');
          worksheet.getCell(`${cellValue}${index + 1}`).value = equipment;
        } else {
          worksheet.getCell(`${cellValue}${index + 1}`).value = '-';
        }
      }
      if (isDfowSelected) {
        const cellValue = cellRange[rowValues.indexOf('Definable Feature of Work')];
        if (
          responseData[index - 1].defineWorkDetails &&
          responseData[index - 1].defineWorkDetails.length > 0
        ) {
          const dfowValues = [];
          for (let m = 0; m < responseData[index - 1].defineWorkDetails.length; m += 1) {
            if (
              responseData[index - 1].defineWorkDetails &&
              responseData[index - 1].defineWorkDetails[m]
            ) {
              dfowValues.push(responseData[index - 1].defineWorkDetails[m].DeliverDefineWork.DFOW);
            }
          }
          const dfow = dfowValues.join(', ');
          worksheet.getCell(`${cellValue}${index + 1}`).value = dfow;
        } else {
          worksheet.getCell(`${cellValue}${index + 1}`).value = '-';
        }
      }
      if (isGateSelected) {
        const cellValue = cellRange[rowValues.indexOf('Gate')];
        worksheet.getCell(`${cellValue}${index + 1}`).value =
          responseData[index - 1].gateDetails && responseData[index - 1].gateDetails[0]
            ? responseData[index - 1].gateDetails[0].Gate.gateName
            : '-';
      }
      if (isCompanySelected) {
        const cellValue = cellRange[rowValues.indexOf('Responsible Company')];
        if (
          responseData[index - 1].companyDetails &&
          responseData[index - 1].companyDetails.length > 0
        ) {
          const companyValues = [];
          for (let m = 0; m < responseData[index - 1].companyDetails.length; m += 1) {
            if (
              responseData[index - 1].companyDetails &&
              responseData[index - 1].companyDetails[m]
            ) {
              companyValues.push(responseData[index - 1].companyDetails[m].Company.companyName);
            }
          }
          const company = companyValues.join(', ');
          worksheet.getCell(`${cellValue}${index + 1}`).value = company;
        } else {
          worksheet.getCell(`${cellValue}${index + 1}`).value = '-';
        }
      }
      if (isPersonSelected) {
        const cellValue = cellRange[rowValues.indexOf('Responsible Person')];
        if (
          responseData[index - 1].memberDetails &&
          responseData[index - 1].memberDetails.length > 0
        ) {
          const memberValues = [];
          for (let m = 0; m < responseData[index - 1].memberDetails.length; m += 1) {
            if (
              responseData[index - 1].memberDetails &&
              responseData[index - 1].memberDetails[m] &&
              responseData[index - 1].memberDetails[m].Member &&
              responseData[index - 1].memberDetails[m].Member.User
            ) {
              memberValues.push(
                `${responseData[index - 1].memberDetails[m].Member.User.firstName} ${responseData[index - 1].memberDetails[m].Member.User.lastName
                }`,
              );
            }
          }
          const member = memberValues.join(', ');
          worksheet.getCell(`${cellValue}${index + 1}`).value = member;
        } else {
          worksheet.getCell(`${cellValue}${index + 1}`).value = '-';
        }
      }
      if (isPickingFromSelected) {
        let pickingFromValue;
        const cellValue = cellRange[rowValues.indexOf('Picking From')];
        if (responseData[index - 1].requestType === 'craneRequest') {
          pickingFromValue = responseData[index - 1].pickUpLocation;
        } else if (responseData[index - 1].requestType === 'deliveryRequestWithCrane') {
          pickingFromValue = responseData[index - 1].cranePickUpLocation;
        } else {
          pickingFromValue = '-';
        }
        worksheet.getCell(`${cellValue}${index + 1}`).value = pickingFromValue;
      }
      if (isPickingToSelected) {
        let pickingToValue;
        const cellValue = cellRange[rowValues.indexOf('Picking To')];
        if (responseData[index - 1].requestType === 'craneRequest') {
          pickingToValue = responseData[index - 1].dropOffLocation;
        } else if (responseData[index - 1].requestType === 'deliveryRequestWithCrane') {
          pickingToValue = responseData[index - 1].craneDropOffLocation;
        } else {
          pickingToValue = '-';
        }
        worksheet.getCell(`${cellValue}${index + 1}`).value = pickingToValue;
      }
      if (isLocationSelected) {
        const cellValue = cellRange[rowValues.indexOf('Location')];
        worksheet.getCell(`${cellValue}${index + 1}`).value =
          responseData[index - 1].location && responseData[index - 1].location.locationPath
            ? `${responseData[index - 1].location.locationPath}`
            : '-';
      }
    }
    return workbook;
  },
};
module.exports = excelCraneReportService;
