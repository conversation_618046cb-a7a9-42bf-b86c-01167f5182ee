/* eslint-disable no-await-in-loop */
const status = require('http-status');
const { notificationPreferenceService } = require('../services');
const {
  NotificationPreferenceItem,
  NotificationPreference,
  Member,
  Project,
} = require('../models');

const NotificationPreferenceController = {
  async setNotificationPreference(req, res, next) {
    notificationPreferenceService.setNotificationPreference(req, async (notification, error) => {
      if (error) {
        return next(error);
      }
      res.status(status.OK).json({
        message: 'Notification Preference Updated Successfully.',
        data: notification,
      });
    });
  },
  async listNotificationPreference(req, res, next) {
    notificationPreferenceService.listNotificationPreference(req, async (notification, error) => {
      if (error) {
        next(error);
      } else {
        res.status(status.OK).json({
          message: 'Notification Preference Listed Successfully',
          data: notification,
        });
      }
    });
  },
  async notificationPreferenceItems(req, res, next) {
    notificationPreferenceService.notificationPreferenceItems(req, async (notification, error) => {
      if (error) {
        next(error);
      } else {
        res.status(status.OK).json({
          message: 'Notification Preference Items Listed Successfully',
          data: notification,
        });
      }
    });
  },
  async addNotificationPreferenceToAllMembers(req, res) {
    try {
      const [notificationItems, projects] = await Promise.all([
        this.getNotificationPreferenceItems(),
        this.getActiveProjects()
      ]);

      for (const project of projects) {
        await this.processProjectMembers(project, notificationItems);
      }

      res.status(status.OK).json({
        message: 'Success',
      });
    } catch (error) {
      res.status(status.INTERNAL_SERVER_ERROR).json({
        message: 'Error adding notification preferences',
        error: error.message,
      });
    }
  },
  async getNotificationPreferenceItems() {
    return NotificationPreferenceItem.findAll({
      where: { isDeleted: false },
    });
  },
  async getActiveProjects() {
    return Project.findAll({
      where: { isDeleted: false },
    });
  },
  async processProjectMembers(project, notificationItems) {
    const members = await Member.findAll({
      where: {
        isDeleted: false,
        ProjectId: +project.id,
        status: 'completed'
      },
    });

    for (const member of members) {
      await this.processMemberPreferences(member, notificationItems);
    }
  },
  async processMemberPreferences(member, notificationItems) {
    const timeZoneId = await this.getTimeZoneId(member.ProjectId);
    await this.updateMemberTimeZone(member.id, timeZoneId);

    const createPromises = notificationItems.map(async (item) => {
      const isCommentNotification = this.isCommentNotification(item);
      const isInAppNotification = item.inappNotification === true;

      const object = {
        MemberId: member.id,
        ProjectId: member.ProjectId,
        ParentCompanyId: member.ParentCompanyId,
        NotificationPreferenceItemId: item.id,
        instant: isCommentNotification || isInAppNotification,
        dailyDigest: !(isCommentNotification || isInAppNotification),
        isDeleted: false,
      };

      return NotificationPreference.createInstance(object);
    });

    await Promise.all(createPromises);
  },
  async getTimeZoneId(projectId) {
    const project = await Project.findOne({
      where: {
        id: +projectId,
        isDeleted: false,
      },
      include: [
        {
          where: { isDeleted: false },
          association: 'TimeZone',
          required: false,
          attributes: ['id', 'location', 'timeZoneOffsetInMinutes'],
        },
      ],
    });

    return project?.TimeZone?.id ?? 3; // Default timezone if not found
  },
  async updateMemberTimeZone(memberId, timeZoneId) {
    await Member.update(
      {
        time: '05:00',
        timeFormat: 'AM',
        TimeZoneId: timeZoneId,
      },
      { where: { id: memberId } }
    );
  },
  isCommentNotification(item) {
    return (
      item.id === 7 &&
      item.description === 'When a comment is added to a delivery/crane/concrete request' &&
      item.itemId === 4 &&
      item.emailNotification === true &&
      item.inappNotification === false &&
      item.isDeleted === false
    );
  },
  async addNotificationPreferenceToAMember(req, res) {
    const getNotificationPreferenceItemsList = await NotificationPreferenceItem.findAll({
      where: { isDeleted: false },
    });
    const memberData = await Member.findOne({
      where: {
        isDeleted: false,
        id: +req.body.MemberId,
        ProjectId: +req.body.ProjectId,
        ParentCompanyId: +req.body.ParentCompanyId,
      },
    });
    const getProject = await Project.findOne({
      where: {
        isDeleted: false,
        id: +memberData.ProjectId,
      },
      include: [
        {
          where: { isDeleted: false },
          association: 'TimeZone',
          required: false,
          attributes: ['id', 'location', 'timeZoneOffsetInMinutes'],
        },
      ],
    });
    const attr = {
      time: '05:00',
      timeFormat: 'AM',
    };
    let projectObject;
    if (getProject) {
      projectObject = getProject.toJSON();
    }
    if (projectObject?.TimeZone) {
      attr.TimeZoneId = projectObject.TimeZone.id;
    } else {
      attr.TimeZoneId = 3;
    }
    await Member.update(attr, { where: { id: memberData.id } });
    getNotificationPreferenceItemsList.map(async (item) => {
      if (
        (item.id === 7 &&
          item.description === 'When a comment is added to a delivery/crane/concrete request' &&
          item.itemId === 4 &&
          item.emailNotification === true &&
          item.inappNotification === false &&
          item.isDeleted === false) ||
        item.inappNotification === true
      ) {
        const object = {
          MemberId: memberData.id,
          ProjectId: memberData.ProjectId,
          ParentCompanyId: memberData.ParentCompanyId,
          NotificationPreferenceItemId: item.id,
          instant: true,
          dailyDigest: false,
          isDeleted: false,
        };
        await NotificationPreference.createInstance(object);
      } else {
        const object = {
          MemberId: memberData.id,
          ProjectId: memberData.ProjectId,
          ParentCompanyId: memberData.ParentCompanyId,
          NotificationPreferenceItemId: item.id,
          instant: false,
          dailyDigest: true,
          isDeleted: false,
        };
        await NotificationPreference.createInstance(object);
      }
    });
    res.status(status.OK).json({
      message: 'Success',
    });
  },
  async removeNotificationPreferenceForDeactivatedMembers(req, res) {
    const deactivatedMembersList = await Member.findAll({
      where: {
        isActive: false,
        isDeleted: false,
      },
    });
    deactivatedMembersList.forEach(async (element) => {
      await NotificationPreference.update(
        { instant: false, dailyDigest: false },
        {
          where: {
            MemberId: +element.id,
            ProjectId: +element.ProjectId,
            ParentCompanyId: +element.ParentCompanyId,
          },
        },
      );
    });
    res.status(status.OK).json({
      message: 'Success',
    });
  },
  async addOneNotificationPreferenceToMembers(req, res) {
    try {
      const notificationItem = await NotificationPreferenceItem.findOne({
        where: { isDeleted: false, id: 12 },
      });

      const projects = await Project.findAll({
        where: { isDeleted: false },
      });

      for (const project of projects) {
        const members = await Member.findAll({
          where: { isDeleted: false, ProjectId: +project.id, status: 'completed' },
        });

        for (const member of members) {
          const object = {
            MemberId: member.id,
            ProjectId: member.ProjectId,
            ParentCompanyId: member.ParentCompanyId,
            NotificationPreferenceItemId: notificationItem.id,
            instant: true,
            dailyDigest: false,
            isDeleted: false,
          };
          await NotificationPreference.createInstance(object);
        }
      }

      res.status(status.OK).json({
        message: 'Success',
      });
    } catch (error) {
      res.status(status.INTERNAL_SERVER_ERROR).json({
        message: 'Error adding notification preferences',
        error: error.message,
      });
    }
  },
};
module.exports = NotificationPreferenceController;
