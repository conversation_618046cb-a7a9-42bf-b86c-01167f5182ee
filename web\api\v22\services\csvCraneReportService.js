const { ExportToCsv } = require('export-to-csv');
const moment = require('moment');
const awsConfig = require('../middlewares/awsConfig');

const csvCraneReportService = {
  async exportCraneReportInCsvFormat(
    data,
    selectedHeaders,
    timezoneoffset,
    fileName,
    exportType,
    done,
  ) {
    /* Column headers */
    const rowValues = [];
    const columns = [];
    let isIdSelected = false;
    let isDescriptionSelected = false;
    let isDateSelected = false;
    let isStatusSelected = false;
    let isApprovedBySelected = false;
    let isEquipmentSelected = false;
    let isDfowSelected = false;
    let isGateSelected = false;
    let isCompanySelected = false;
    let isPersonSelected = false;
    let isPickingFromSelected = false;
    let isPickingToSelected = false;
    let isLocationSelected = false;
    selectedHeaders.map((object) => {
      if (object.isActive === true) {
        rowValues.push(object.title);
        if (object.key === 'id') {
          columns.push({ key: object.key, width: 5 });
        } else {
          columns.push({ key: object.key, width: 32 });
        }
        if (object.key === 'id') isIdSelected = true;
        if (object.key === 'description') isDescriptionSelected = true;
        if (object.key === 'date') isDateSelected = true;
        if (object.key === 'status') isStatusSelected = true;
        if (object.key === 'approvedby') isApprovedBySelected = true;
        if (object.key === 'equipment') isEquipmentSelected = true;
        if (object.key === 'dfow') isDfowSelected = true;
        if (object.key === 'gate') isGateSelected = true;
        if (object.key === 'company') isCompanySelected = true;
        if (object.key === 'name') isPersonSelected = true;
        if (object.key === 'pickingFrom') isPickingFromSelected = true;
        if (object.key === 'pickingTo') isPickingToSelected = true;
        if (object.key === 'location') isLocationSelected = true;
      }
      return object;
    });
    const values = [];
    for (let index = 0; index < data.length; index += 1) {
      const object = {};
      if (isIdSelected) {
        object.Id = data[index].CraneRequestId;
      }
      if (isDescriptionSelected) {
        object.Description = data[index].description;
      }
      if (isDateSelected) {
        if (data[index].requestType === 'craneRequest') {
          const start = moment(data[index].craneDeliveryStart).add(Number(timezoneoffset), 'minutes');
          const end = moment(data[index].craneDeliveryEnd).add(Number(timezoneoffset), 'minutes');

          object['Date & Time'] = `${start.format('MMM-DD-YYYY hh:mm a')} - ${end.format('hh:mm a')}`;
        } else {
          const start = moment(data[index].deliveryStart).add(Number(timezoneoffset), 'minutes');
          const end = moment(data[index].deliveryEnd).add(Number(timezoneoffset), 'minutes');

          object['Date & Time'] = `${start.format('MMM-DD-YYYY hh:mm a')} - ${end.format('hh:mm a')}`;
        }
      }

      if (isStatusSelected) {
        object.Status = data[index].status;
      }
      if (isApprovedBySelected) {
        object['Approved By'] =
          data[index].approverDetails && data[index].approverDetails.User.firstName
            ? `${data[index].approverDetails.User.firstName} ${data[index].approverDetails.User.lastName} `
            : '-';
      }
      if (isEquipmentSelected) {
        let equipment = null;
        if (data[index].equipmentDetails && data[index].equipmentDetails.length > 0) {
          const equipmentValues = [];
          for (let m = 0; m < data[index].equipmentDetails.length; m += 1) {
            if (
              data[index].equipmentDetails &&
              data[index].equipmentDetails[m] &&
              data[index].equipmentDetails[m].Equipment &&
              data[index].equipmentDetails[m].Equipment.PresetEquipmentType
            ) {
              if (data[index].equipmentDetails[m].Equipment.PresetEquipmentType.isCraneType) {
                equipmentValues.push(data[index].equipmentDetails[m].Equipment.equipmentName);
              }
            }
          }
          equipment = equipmentValues.join(', ');
        }
        object.Equipment = equipment || '-';

        // object.Equipment =
        //   data[index].equipmentDetails && data[index].equipmentDetails[0]
        //     ? data[index].equipmentDetails[0].Equipment.equipmentName
        //     : '-';
      }
      if (isDfowSelected) {
        let dfow = null;
        if (data[index].defineWorkDetails && data[index].defineWorkDetails.length > 0) {
          const dfowValues = [];
          for (let m = 0; m < data[index].defineWorkDetails.length; m += 1) {
            if (data[index].defineWorkDetails && data[index].defineWorkDetails[m]) {
              dfowValues.push(data[index].defineWorkDetails[m].DeliverDefineWork.DFOW);
            }
          }
          dfow = dfowValues.join(', ');
        }
        object['Definable Feature of Work'] = dfow || '-';
      }
      if (isGateSelected) {
        object.Gate =
          data[index].gateDetails && data[index].gateDetails[0]
            ? data[index].gateDetails[0].Gate.gateName
            : '-';
      }
      if (isCompanySelected) {
        let company = null;
        if (data[index].companyDetails && data[index].companyDetails.length > 0) {
          const companyValues = [];
          for (let m = 0; m < data[index].companyDetails.length; m += 1) {
            if (data[index].companyDetails && data[index].companyDetails[m]) {
              companyValues.push(data[index].companyDetails[m].Company.companyName);
            }
          }
          company = companyValues.join(', ');
        }
        object['Responsible Company'] = company || '-';
      }
      if (isPersonSelected) {
        let member;
        if (data[index].memberDetails && data[index].memberDetails.length > 0) {
          const memberValues = [];
          for (let m = 0; m < data[index].memberDetails.length; m += 1) {
            if (
              data[index].memberDetails &&
              data[index].memberDetails[m] &&
              data[index].memberDetails[m].Member &&
              data[index].memberDetails[m].Member.User
            ) {
              memberValues.push(
                `${data[index].memberDetails[m].Member.User.firstName} ${data[index].memberDetails[m].Member.User.lastName} `,
              );
            }
          }
          member = memberValues.join(', ');
        }
        object['Responsible Person'] = member || '-';
      }
      if (isPickingFromSelected) {
        if (data[index].requestType === 'craneRequest') {
          object['Picking From'] = data[index].pickUpLocation;
        } else if (data[index].requestType === 'deliveryRequestWithCrane') {
          object['Picking From'] = data[index].cranePickUpLocation;
        } else {
          object['Picking From'] = '-';
        }
      }
      if (isPickingToSelected) {
        if (data[index].requestType === 'craneRequest') {
          object['Picking To'] = data[index].dropOffLocation;
        } else if (data[index].requestType === 'deliveryRequestWithCrane') {
          object['Picking To'] = data[index].craneDropOffLocation;
        } else {
          object['Picking To'] = '-';
        }
      }
      if (isLocationSelected) {
        object.Location =
          data[index].location && data[index].location.locationPath
            ? `${data[index].location.locationPath} `
            : '-';
      }
      values.push(object);
    }
    const options = {
      showLabels: true,
      showTitle: false,
      useTextFile: false,
      useBom: false,
      useKeysAsHeaders: true,
    };

    const csvExporter = new ExportToCsv(options);
    const csvFile = await csvExporter.generateCsv(values, true);
    if (csvFile) {
      const buffer = Buffer.from(csvFile, 'utf-8');
      awsConfig.reportUpload(buffer, fileName, exportType, async (result, error1) => {
        if (!error1) {
          return done(result, false);
        }
        return done(null, { message: 'cannot export document' });
      });
    }
  },
};
module.exports = csvCraneReportService;
