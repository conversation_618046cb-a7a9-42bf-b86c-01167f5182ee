const status = require('http-status');
const { cornService } = require('../services');

const CornController = {
  async checkOverDue() {
    await cornService.checkOverDue();
  },
  async checkNDRExpiration() {
    await cornService.checkNDRExpiration();
  },
  async checkDailyDigestEmailNotification() {
    await cornService.checkDailyDigestEmailNotification();
  },
  async schedulerReportRequest(req, res, next) {
    try {
      await cornService.schedulerReportRequest(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Success.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getSchedulerReportRequest(req, res, next) {
    try {
      await cornService.getSchedulerReportRequest(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Success.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getRerunReportRequest(req, res, next) {
    try {
      await cornService.getRerunReportRequest(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Success.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getSchedulerTimelineNames(req, res, next) {
    try {
      await cornService.getSchedulerTimelineNames(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Success.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async runtimeScheduler() {
    try {
      await cornService.runtimeScheduler();
    } catch (e) {
      console.log('*************ERROR*****************', e);
    }
  },
};
module.exports = CornController;
