module.exports = (sequelize, DataTypes) => {
  const DigestNotification = sequelize.define(
    'DigestNotification',
    {
      MemberId: DataTypes.INTEGER,
      ProjectId: DataTypes.INTEGER,
      ParentCompanyId: DataTypes.INTEGER,
      description: DataTypes.TEXT,
      isDeleted: DataTypes.BOOLEAN,
      isSent: DataTypes.BOOLEAN,
    },
    {},
  );
  DigestNotification.associate = (models) => {
    DigestNotification.belongsTo(models.Member);
  };
  return DigestNotification;
};
