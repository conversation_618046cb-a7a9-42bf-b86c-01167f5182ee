/* eslint-disable global-require */
const fs = require('fs');
const path = require('path');
const Sequelize = require('sequelize');
const Umzug = require('umzug');
const SequelizeStorage = require('umzug');

const basename = path.basename(__filename);
const env = process.env.NODE_ENV || 'development';
const config = require('../../../config/db')[env];

console.log('db config', config);

const db = {};

let sequelize;
if (config.use_env_variable) {
  sequelize = new Sequelize(process.env[config.use_env_variable], config);
} else {
  sequelize = new Sequelize(config.database, config.username, config.password, config);
}

fs.readdirSync(__dirname)
  .filter((file) => {
    return file.indexOf('.') !== 0 && file !== basename && file.slice(-3) === '.js';
  })
  .forEach((file) => {
    // eslint-disable-next-line import/no-dynamic-require
    const model = require(path.join(__dirname, file))(sequelize, Sequelize.DataTypes);
    db[model.name] = model;
  });

Object.keys(db).forEach((modelName) => {
  if (db[modelName].associate) {
    db[modelName].associate(db);
  }
});

db.sequelize = sequelize;
db.Sequelize = Sequelize;

async function syncToSchema(schema) {
  const sequelizeUmzug = new Sequelize(config.database, config.username, config.password, {
    ...config,
    schema,
  });

  // const umzug = new Umzug({
  //   storage: 'sequelize',
  //   storageOptions: {
  //     sequelize: sequelizeUmzug,
  //   },
  //   migrations: {
  //     params: [
  //       sequelizeUmzug.getQueryInterface(), // queryInterface
  //       sequelizeUmzug.constructor, // DataTypes
  //       schema, // schema
  //       () => {
  //         throw new Error(
  //           'Migration tried to use old style "done" callback. Please upgrade to "umzug" and return a promise instead.',
  //         );
  //       },
  //     ],
  //     path: `${__dirname}/../../../../config/db/sequelize/migrate`,
  //     pattern: /\.js$/,
  //   },
  //   logging: console.log,
  // });

  const umzug = new Umzug({
    migrations: { glob: `${__dirname}/../../../../config/db/sequelize/migrate` },
    context: sequelizeUmzug.getQueryInterface(),
    storage: new SequelizeStorage({ sequelizeUmzug }),
    logger: console,
  });

  const allSchema = await sequelize.showAllSchemas();
  if (!allSchema.includes(schema)) await sequelize.createSchema(schema);
  console.info(`${schema} ---- started migration`);
  await umzug.up();
  console.info(`${schema} ---- finished migration`);
}

db.syncToSchema = syncToSchema;

module.exports = db;
