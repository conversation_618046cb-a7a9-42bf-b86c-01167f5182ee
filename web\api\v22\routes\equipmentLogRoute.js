const { Router } = require('express');
const { validate } = require('express-validation');
const { equipmentValidation } = require('../middlewares/validations');
const EquipmentLogController = require('../controllers/equipmentLogController');
const passportConfig = require('../config/passport');
const checkAdmin = require('../middlewares/checkAdmin');

const equipmentLogRoute = {
    get router() {
        const router = Router();
        router.post(
            '/add_equipmentlog',
            // validate(equipmentValidation.addEquipment, { keyByField: true }, { abortEarly: false }),
            passportConfig.isAuthenticated,
            // checkAdmin.isProjectAdmin,
            EquipmentLogController.addEquipmentLog,
        );
        router.get(
            '/equipmentlog_list/:pageSize/:pageNo',
            // validate(equipmentValidation.equipmentDetail, { keyByField: true }, { abortEarly: false }),
            passportConfig.isAuthenticated,
            EquipmentLogController.listEquipmentLog,
        );
        return router;
    },
};
module.exports = equipmentLogRoute;