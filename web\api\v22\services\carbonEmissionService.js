
const awsConfig = require('../middlewares/awsConfig');
const xlsx = require('xlsx');
const path = require('path');

const {
    Sequelize,
    Utilities,
    co2EmissionPerUnits
} = require('../models');
const { Op, fn, col, literal } = require('sequelize');



const CO2_EMISSION_EXCEL_PATH = '../../../config/power_profiler_zipcode_tool.xlsx';

const carbonEmissionService = {
    async fileUpload(inputData, done) {
        try {
            await awsConfig.carbonEmissionFileUpload(inputData, async (result, err) => {
                if (!err) {
                    done(result, err);
                } else {
                    done(null, err);
                }
            });
        } catch (e) {
            done(null, e);
        }
    },
    async calculateCO2Emissions(zipCode, totalEnergyConsumedMWh) {
        try {
            const filePath = path.resolve(__dirname, CO2_EMISSION_EXCEL_PATH);
            const workbook = xlsx.readFile(filePath);
            const zipSubregionData = xlsx.utils.sheet_to_json(workbook.Sheets["Zip-subregion"]);
            // console.log(zipSubregionData, 'zipSubregionData');
            const co2RatesSheet = workbook.Sheets["Subregion Rates (kg-MWh)"];
            const grossLossSheet = workbook.Sheets["Subregion Rates (lbs-MWh)"];

            const zipCodeInt = parseInt(zipCode, 10);
            console.log(zipCodeInt, 'zipCodeInt');

            const matchingRow = zipSubregionData.find(row => row.zip === JSON.stringify(zipCodeInt));
            console.log(matchingRow, 'matchingRow');
            const subregion = matchingRow ? matchingRow["Subregion 1"] : null;
            console.log('subregion', subregion);
            console.log('totalEnergyConsumedMWh', totalEnergyConsumedMWh);
            const co2Match = xlsx.utils.sheet_to_json(co2RatesSheet).find(row => row[Object.keys(row)[1]] === subregion);
            const co2PerMWh = co2Match ? parseFloat(co2Match[Object.keys(co2Match)[2]]) : null;
            const totalCO2WoLineLoss = co2PerMWh ? +(totalEnergyConsumedMWh * co2PerMWh * 0.001).toFixed(2) : 0;

            const grossLossMatch = xlsx.utils.sheet_to_json(grossLossSheet).find(row => row[Object.keys(row)[1]] === subregion);
            const gridGrossLossFactor = grossLossMatch ? grossLossMatch[Object.keys(grossLossMatch)[8]] : null;
            const adjustmentFactor = 0.041;
            const gridLossEmission = co2PerMWh ?
                +((adjustmentFactor * co2PerMWh * totalEnergyConsumedMWh * 0.001) / (1 - adjustmentFactor)).toFixed(2) : null;

            const totalCO2Emission = totalCO2WoLineLoss + (gridLossEmission || 0);

            return {
                subregion,
                co2PerMWh,
                totalCO2WoLineLoss,
                gridGrossLossFactor,
                gridLossEmission,
                totalCO2Emission
            };
        } catch (err) {
            return null;
        }
    },

    async getEmissionValues() {
        try {
            const emissionValues = await co2EmissionPerUnits.findAll();
            return emissionValues;
        } catch (error) {
            console.error("Error fetching emission values:", error);
            throw error;

        }
    },

    async getDashboardData(payload, done) {
        try {
            console.log(payload, 'payload');

            const utilitiesData = await Utilities.findAll({
                where: {
                    projectId: payload.ProjectId,
                    parentId: { [Op.ne]: null }
                },
                attributes: [
                    'utilityType',
                    [fn('SUM', literal(`CAST(NULLIF("quantity", '') AS NUMERIC)`)), 'totalQuantity'],
                    [fn('SUM', literal(`CAST(NULLIF(REPLACE("cost", '$', ''), '') AS NUMERIC)`)), 'totalCost'],
                    [fn('SUM', literal(`CAST(NULLIF("co2_emissions", '') AS NUMERIC)`)), 'totalEmissions']
                ],
                group: ['utilityType']
            });

            console.log(utilitiesData, 'utilitiesData');
            done(utilitiesData, null);
        } catch (error) {
            console.log(error, 'error');
            return error;
        }
    }

}
module.exports = carbonEmissionService;
