const Joi = require('joi');

const adminValidation = {
  updateUser: {
    body: Joi.object({
      name: Joi.string(),
    }),
  },
  login: {
    body: Joi.object({
      email: Joi.string().required(),
      password: Joi.string().required(),
    }),
  },
  createAccountAdmin: {
    body: Joi.object({
      basicDetails: Joi.object({
        email: Joi.string().email().required(),
        phoneNumber: Joi.string().required(),
        phoneCode: Joi.string(),
        projectCount: Joi.number().required(),
      }).required(),
      companyDetails: Joi.object({
        companyName: Joi.string().min(3).max(50),
        fullName: Joi.string().min(3).max(30).required(),
        scope: Joi.string().min(2),
        address: Joi.string().min(3),
        country: Joi.string().min(2),
        city: Joi.string().min(2),
        state: Joi.string().min(2),
        zipCode: Joi.string(),
        website: Joi.string().min(2),
        isParent: Joi.boolean(),
      }).required(),
      amountDetails: Joi.object({
        currencyType: Joi.string().required(),
        currencySymbol: Joi.string().required(),
        amount: Joi.number().required(),
      }).required(),
      domainDetails: Joi.object({
        name: Joi.string().required(),
      }).required(),
      group: Joi.boolean().required(),
    }),
  },
};

module.exports = adminValidation;
