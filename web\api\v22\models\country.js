module.exports = (sequelize, DataTypes) => {
  const Country = sequelize.define(
    'Country',
    {
      countryName: DataTypes.STRING,
      countryDialCode: DataTypes.STRING,
    },
    {},
  );
  Country.associate = (models) => {
    Country.hasMany(models.State);
    return Country;
  };
  Country.getAll = async (attr) => {
    const country = await Country.findAll({
      where: { ...attr },
    });
    return country;
  };
  Country.createInstance = async (paramData) => {
    const country = await Country.create(paramData);
    return country;
  };
  return Country;
};
