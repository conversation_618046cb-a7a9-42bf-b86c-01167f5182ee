const { Router } = require('express');
const { validate } = require('express-validation');
const multer = require('multer');
const passportConfig = require('../config/passport');
const { DeliveryController } = require('../controllers');
const { deliveryValidation } = require('../middlewares/validations');
const upload = multer({ dest: 'uploads/' }); // NOSONAR
const cacheMiddleware = require('../middlewares/cacheMiddleware');

const deliveryRoute = {
  get router() {
    const router = Router();
    router.post(
      '/new_request',
      validate(deliveryValidation.newRequest, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      cacheMiddleware.invalidateAfterAllCalendarMutation(),
      cacheMiddleware.invalidateAfterDeliveryNDRListMutation(),
      cacheMiddleware.invalidateAfterDeliverySingleNDRMutation(),
      cacheMiddleware.invalidateAfterHistoryMutation(),
      DeliveryController.newRequest,
    );
    router.post(
      '/edit_request',
      validate(deliveryValidation.editRequest, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      cacheMiddleware.invalidateAfterAllCalendarMutation(),
      cacheMiddleware.invalidateAfterDeliveryNDRListMutation(),
      cacheMiddleware.invalidateAfterDeliverySingleNDRMutation(),
      cacheMiddleware.invalidateAfterHistoryMutation(),
      DeliveryController.editRequest,
    );
    router.post(
      '/edit_queued_request',
      validate(deliveryValidation.editQueuedRequest, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      cacheMiddleware.invalidateAfterAllCalendarMutation(),
      cacheMiddleware.invalidateAfterDeliveryNDRListMutation(),
      cacheMiddleware.invalidateAfterDeliverySingleNDRMutation(),
      cacheMiddleware.invalidateAfterHistoryMutation(),
      DeliveryController.editRequest,
    );
    router.post(
      '/update_to_current_NDR',
      validate(deliveryValidation.updateQueuedRequest, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      cacheMiddleware.invalidateAfterAllCalendarMutation(),
      cacheMiddleware.invalidateAfterDeliveryNDRListMutation(),
      cacheMiddleware.invalidateAfterDeliverySingleNDRMutation(),
      cacheMiddleware.invalidateAfterHistoryMutation(),
      DeliveryController.editRequest,
    );
    router.post(
      '/update_status',
      validate(deliveryValidation.updateNDRStatus, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      cacheMiddleware.invalidateAfterAllCalendarMutation(),
      cacheMiddleware.invalidateAfterDeliveryNDRListMutation(),
      cacheMiddleware.invalidateAfterDeliverySingleNDRMutation(),
      cacheMiddleware.invalidateAfterHistoryMutation(),
      DeliveryController.updateNDRStatus,
    );
    router.post(
      '/list_NDR/:ProjectId/:pageSize/:pageNo/:void',
      validate(deliveryValidation.listNDR, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      cacheMiddleware.cacheDeliveryNDRList(),
      DeliveryController.listNDR,
    );
    router.post(
      '/list_Queued_NDR/:ProjectId/:pageSize/:pageNo/:void',
      validate(deliveryValidation.listNDR, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      cacheMiddleware.cacheDeliveryNDRList(),
      DeliveryController.listNDR,
    );
    router.get(
      '/get_single_NDR/:DeliveryRequestId/?:ParentCompanyId',
      validate(deliveryValidation.getNDRData, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      cacheMiddleware.cacheDeliverySingleNDR(),
      DeliveryController.getNDRData,
    );
    router.get(
      '/get_user_role/:ProjectId/?:ParentCompanyId',
      validate(deliveryValidation.getMemberData, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      DeliveryController.getMemberData,
    );
    router.post(
      '/sample_delivery_request_template/?:ProjectId/?:ParentCompanyId',
      validate(
        deliveryValidation.uploadBulkNDRTemplate,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      DeliveryController.sampleBulkDeliveryRequestTemplate,
    );
    router.post(
      '/bulk_upload_delivery_request/?:ProjectId/?:ParentCompanyId',
      upload.single('delivery_request'),
      validate(
        deliveryValidation.bulkUploadDeliveryRequest,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      cacheMiddleware.invalidateAfterAllCalendarMutation(),
      cacheMiddleware.invalidateAfterDeliveryNDRListMutation(),
      cacheMiddleware.invalidateAfterDeliverySingleNDRMutation(),
      cacheMiddleware.invalidateAfterHistoryMutation(),
      DeliveryController.bulkUploadDeliveryRequest,
    );
    router.post(
      '/delete_queued_Ndr',
      passportConfig.isAuthenticated,
      cacheMiddleware.invalidateAfterAllCalendarMutation(),
      cacheMiddleware.invalidateAfterDeliveryNDRListMutation(),
      cacheMiddleware.invalidateAfterDeliverySingleNDRMutation(),
      cacheMiddleware.invalidateAfterHistoryMutation(),
      DeliveryController.deleteQueuedNdr,
    );
    router.post(
      '/edit_multiple_request',
      passportConfig.isAuthenticated,
      cacheMiddleware.invalidateAfterAllCalendarMutation(),
      cacheMiddleware.invalidateAfterDeliveryNDRListMutation(),
      cacheMiddleware.invalidateAfterDeliverySingleNDRMutation(),
      cacheMiddleware.invalidateAfterHistoryMutation(),
      DeliveryController.editMultipleDeliveryRequest,
    );
    router.get(
      '/get_last_delivery_request_id/:ProjectId/?:ParentCompanyId',
      passportConfig.isAuthenticated,
      DeliveryController.getLastDeliveryRequestId,
    );
    router.post('/decrypt', passportConfig.isAuthenticated, DeliveryController.decryption);

    router.get(
      '/readall_notification',
      passportConfig.isAuthenticated,
      DeliveryController.setReadAllNotification,
    );
    router.get(
      '/markall_notification/:ProjectId',
      passportConfig.isAuthenticated,
      DeliveryController.markAllNotification,
    );
    return router;
  },
};
module.exports = deliveryRoute;
