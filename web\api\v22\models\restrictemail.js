module.exports = (sequelize, DataTypes) => {
  const RestrictEmail = sequelize.define(
    'RestrictEmail',
    {
      domainName: DataTypes.STRING,
      isActive: DataTypes.BOOLEAN,
    },
    {},
  );
  RestrictEmail.getBy = async (attr) => {
    const restrictEmail = await RestrictEmail.findOne({ where: { ...attr } });

    return restrictEmail;
  };
  RestrictEmail.createInstance = async (paramData) => {
    const newRestrictEmail = await RestrictEmail.create(paramData);
    return newRestrictEmail;
  };
  RestrictEmail.getMailList = async (limit, offset) => {
    let restrictEmail;
    if (limit === 0 || offset === 0) {
      restrictEmail = await RestrictEmail.findAll({});
    } else {
      restrictEmail = await RestrictEmail.findAll({
        limit,
        offset,
      });
    }

    return restrictEmail;
  };
  return RestrictEmail;
};
