const { RestrictEmail, Sequelize } = require('../models');

const { Op } = Sequelize;

const restrictMailService = {
  async addRestrictMail(inputData, done) {
    try {
      const existData = await RestrictEmail.getBy({ domainName: inputData.body.domainName });
      if (existData) {
        done(null, 'Domain Already exist.');
      } else {
        const newData = await RestrictEmail.createInstance(inputData.body);
        if (newData) {
          done(newData, false);
        } else {
          done(null, 'Something went wrong.');
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
  async updateRestrictMail(inputData, done) {
    try {
      const ids = [inputData.body.id];
      const existData = await RestrictEmail.findOne({
        where: Sequelize.and({
          domainName: inputData.body.domainName,
          id: { [Op.notIn]: ids },
        }),
      });
      if (existData) {
        done(null, 'Domain Already exist.');
      } else {
        const updateData = await RestrictEmail.update(
          { domainName: inputData.body.domainName, isActive: inputData.body.isActive },
          { where: { id: { [Op.in]: ids } } },
        );
        if (updateData) {
          done(updateData, false);
        } else {
          done(null, 'Something went wrong.');
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
  async deleteRestrictMail(inputData, done) {
    try {
      const ids = [inputData.body.id];
      const updateData = await RestrictEmail.destroy({ where: { id: { [Op.in]: ids } } });
      if (updateData) {
        done(updateData, false);
      } else {
        done(null, 'Something went wrong.');
      }
    } catch (e) {
      done(null, e);
    }
  },
};
module.exports = restrictMailService;
