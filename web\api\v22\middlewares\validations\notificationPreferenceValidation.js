const Joi = require('joi');

const notificationPreferenceValidation = {
  setNotificationPreference: {
    query: Joi.object({
      MemberId: Joi.number().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
    }),
    body: Joi.object({
      inAppNotification: Joi.array().items({
        id: Joi.number().required(),
        MemberId: Joi.number().required(),
        ProjectId: Joi.number().required(),
        dailyDigest: Joi.boolean().required(),
        ParentCompanyId: Joi.number().required(),
        NotificationPreferenceItemId: Joi.number().required(),
        instant: Joi.boolean().required(),
        NotificationPreferenceItem: Joi.object({
          id: Joi.number().required(),
          description: Joi.string().required(),
          inappNotification: Joi.boolean().required(),
          emailNotification: Joi.boolean().required(),
          itemId: Joi.number().required(),
        }),
      }),
      emailNotification: Joi.array().items({
        id: Joi.number().required(),
        MemberId: Joi.number().required(),
        ProjectId: Joi.number().required(),
        ParentCompanyId: Joi.number().required(),
        NotificationPreferenceItemId: Joi.number().required(),
        instant: Joi.boolean().required(),
        dailyDigest: Joi.boolean().required(),
        NotificationPreferenceItem: Joi.object({
          id: Joi.number().required(),
          description: Joi.string().required(),
          inappNotification: Joi.boolean().required(),
          emailNotification: Joi.boolean().required(),
          itemId: Joi.number().required(),
        }),
      }),
      dailyDigestTiming: Joi.object({
        time: Joi.string().required(),
        timeFormat: Joi.string().required(),
        TimeZoneId: Joi.number().required(),
      }),
    }),
  },
  listNotificationPreference: {
    query: Joi.object({
      MemberId: Joi.number().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
    }),
  },
};
module.exports = notificationPreferenceValidation;
