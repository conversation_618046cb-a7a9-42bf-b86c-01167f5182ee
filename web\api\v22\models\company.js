module.exports = (sequelize, DataTypes) => {
  const Company = sequelize.define(
    'Company',
    {
      companyName: DataTypes.STRING,
      scope: DataTypes.STRING,
      address: DataTypes.STRING,
      secondAddress: DataTypes.STRING,
      country: DataTypes.STRING,
      city: DataTypes.STRING,
      logo: DataTypes.STRING,
      state: DataTypes.STRING,
      zipCode: DataTypes.STRING,
      ProjectId: {
        type: DataTypes.INTEGER,
      },
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      companyAutoId: {
        type: DataTypes.INTEGER,
      },
      createdBy: {
        type: DataTypes.INTEGER,
        references: {
          model: 'Users', // name of Target model
          key: 'id', // key in Target model that we're referencing
        },
      },
      isDeleted: DataTypes.BOOLEAN,
      ParentCompanyId: DataTypes.INTEGER,
      isParent: DataTypes.BOOLEAN,
      website: DataTypes.STRING,
    },
    {},
  );
  Company.associate = (models) => {
    Company.belongsTo(models.User, {
      as: 'createdUser',
      foreignKey: 'createdBy',
    });
    Company.belongsTo(models.Project);
    Company.belongsTo(models.ParentCompany);
    Company.hasMany(models.Member);
    Company.hasMany(models.CompanyDefine, {
      as: 'define',
      foreignKey: 'CompanyId',
    });
    Company.hasMany(models.CompanyDefine, {
      as: 'defineEditData',
      foreignKey: 'CompanyId',
    });
    return Company;
  };
  Company.getAll = async (attr, limit, offset, searchCondition, sort, sortColumn) => {
    const sortByFieldName = sortColumn || 'id';
    const sortByColumnType = sort || 'DESC';
    let orderQuery = [[`${sortByFieldName}`, `${sortByColumnType}`]];
    if (sortByFieldName === 'companyName') {
      orderQuery = [[`${sortByFieldName}`, `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'DFOW') {
      orderQuery = [['define', 'DeliverDefineWork', `${sortByFieldName}`, `${sortByColumnType}`]];
    }
    const company = await Company.findAndCountAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          association: 'createdUser',
          attributes: ['id', 'firstName', 'email'],
        },
        {
          association: 'Members',
          attributes: ['id'],
        },
        {
          association: 'define',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [
            {
              association: 'DeliverDefineWork',
              required: false,
              attributes: ['id', 'DFOW'],
            },
          ],
        },
        {
          association: 'defineEditData',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [
            {
              association: 'DeliverDefineWork',
              required: false,
              attributes: ['id', 'DFOW'],
            },
          ],
        },
        'Project',
      ],
      where: { ...attr, ...searchCondition },
      attributes: [
        'id',
        'companyName',
        'website',
        'address',
        'secondAddress',
        'country',
        'city',
        'companyAutoId',
        'state',
        'zipCode',
        'scope',
        'logo',
      ],
      order: orderQuery,
    });

    return company;
  };
  Company.getAllCompany = async (attr) => {
    const company = await Company.findAndCountAll({
      where: { ...attr },
      attributes: ['id', 'companyName', 'createdAt', 'ProjectId'],
      order: [['id', 'DESC']],
    });

    return company;
  };

  Company.getBy = async (attr) => {
    const company = await Company.findOne({
      include: [
        { association: 'createdUser', attributes: { exclude: ['password', 'resetPasswordToken'] } },
        'Project',
      ],
      where: { ...attr },
      order: [['id', 'DESC']],
    });
    return company;
  };
  Company.createInstance = async (paramData) => {
    const company = await Company.create(paramData);
    return company;
  };
  return Company;
};
