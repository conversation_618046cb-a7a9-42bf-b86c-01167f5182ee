module.exports = (sequelize, DataTypes) => {
  const ConcreteRequestMixDesign = sequelize.define(
    'ConcreteRequestMixDesign',
    {
      ConcreteRequestId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      ConcreteRequestCode: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ConcreteMixDesignId: DataTypes.INTEGER,
      ProjectId: DataTypes.INTEGER,
    },
    {},
  );
  ConcreteRequestMixDesign.associate = (models) => {
    ConcreteRequestMixDesign.belongsTo(models.ConcreteRequest, {
      as: 'concreteRequest',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequestMixDesign.belongsTo(models.ConcreteMixDesign);
  };
  ConcreteRequestMixDesign.createInstance = async (paramData) => {
    const newConcreteRequestMixDesign = await ConcreteRequestMixDesign.create(paramData);
    return newConcreteRequestMixDesign;
  };
  return ConcreteRequestMixDesign;
};
