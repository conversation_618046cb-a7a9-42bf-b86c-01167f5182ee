const status = require('http-status');
const AuthController = require('../AuthController');

// Mock dependencies
jest.mock('../../models', () => ({
  User: {
    findOne: jest.fn(),
  },
  StripePlan: {
    getBy: jest.fn(),
  },
  ParentCompany: {
    getBy: jest.fn(),
  },
  Enterprise: {
    findOne: jest.fn(),
  },
  RestrictEmail: {
    getBy: jest.fn(),
  },
  Member: {
    findAll: jest.fn(),
  },
  Sequelize: {
    and: jest.fn((...args) => args),
    or: jest.fn((...args) => args),
    where: jest.fn(),
    fn: jest.fn(),
    col: jest.fn(),
    Op: {
      and: 'and',
      or: 'or',
      iLike: 'iLike',
    },
  },
}));

jest.mock('../helpers/apiError', () =>
  jest.fn().mockImplementation((message, statusCode) => ({ message, statusCode })),
);

// Mock the serializers module
jest.mock('../../serializers', () => ({
  UserSerializer: {
    serialize: jest.fn((user) => ({ ...user, serialized: true }))
  },
}));

jest.mock('../../services', () => ({
  authService: {
    createUser: jest.fn(),
    login: jest.fn(),
    adminLogin: jest.fn(),
    forgotPassword: jest.fn(),
    resetPasswordByEmail: jest.fn(),
    resetPassword: jest.fn(),
    getDynamicModel: jest.fn(),
  },
}));

jest.mock('../../mailer', () => ({
  sendMail: jest.fn(),
}));

const {
  User,
  StripePlan,
  ParentCompany,
  Enterprise,
  RestrictEmail,
  Member,
  Sequelize,
} = require('../../models');

const { authService } = require('../../services');
const ApiError = require('../helpers/apiError');
const { UserSerializer } = require('../../serializers');

describe('AuthController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    jest.clearAllMocks();

    mockReq = {
      body: {},
      params: {},
      headers: {},
      get: jest.fn(),
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    mockNext = jest.fn();
  });

  describe('register', () => {
    it('should register user successfully', async () => {
      // Arrange
      const userData = {
        basicDetails: { email: '<EMAIL>', phoneNumber: '**********' },
        planData: { id: 1 },
      };
      const planRecord = { id: 1, name: 'Basic Plan' };
      const userDetail = { id: 1, email: '<EMAIL>' };

      mockReq.body = userData;
      mockReq.headers.timezoneoffset = '-300';
      mockReq.get.mockReturnValue('https://app.example.com');
      User.findOne.mockResolvedValue(null);
      StripePlan.getBy.mockResolvedValue(planRecord);
      authService.createUser.mockImplementation((data, callback) => callback(userDetail, null));
      UserSerializer.serialize.mockReturnValue({ ...userDetail, serialized: true });

      // Act
      await AuthController.register(mockReq, mockRes, mockNext);

      // Assert
      expect(User.findOne).toHaveBeenCalled();
      expect(StripePlan.getBy).toHaveBeenCalledWith({ id: 1 });
      expect(authService.createUser).toHaveBeenCalledWith(
        { ...userData, planData: planRecord, timezoneoffset: '-300' },
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.CREATED);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Registered Successfully.',
        user: { ...userDetail, serialized: true },
      });
    });

    it('should return error for subdomain registration', async () => {
      // Arrange
      mockReq.get.mockReturnValue('https://company-prod.folloit.com');

      // Act
      await AuthController.register(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Not allowed to register',
          statusCode: status.BAD_REQUEST,
        }),
      );
    });

    it('should return error when user already exists', async () => {
      // Arrange
      const userData = {
        basicDetails: { email: '<EMAIL>', phoneNumber: '**********' },
      };
      const existingUser = { id: 1, email: '<EMAIL>' };

      mockReq.body = userData;
      mockReq.get.mockReturnValue('https://app.example.com');
      User.findOne.mockResolvedValue(existingUser);

      // Act
      await AuthController.register(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Email/Mobile Number already exist',
          statusCode: status.BAD_REQUEST,
        }),
      );
    });

    it('should handle service error', async () => {
      // Arrange
      const userData = {
        basicDetails: { email: '<EMAIL>', phoneNumber: '**********' },
        planData: { id: 1 },
      };
      const planRecord = { id: 1, name: 'Basic Plan' };
      const error = new Error('Service error');

      mockReq.body = userData;
      mockReq.get.mockReturnValue('https://app.example.com');
      User.findOne.mockResolvedValue(null);
      StripePlan.getBy.mockResolvedValue(planRecord);
      authService.createUser.mockImplementation((data, callback) => callback(null, error));

      // Act
      await AuthController.register(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('existEmail', () => {
    it('should return not exist when user does not exist', async () => {
      // Arrange
      const userData = { email: '<EMAIL>', phoneNumber: '**********' };

      mockReq.body = userData;
      User.findOne.mockResolvedValue(null);
      RestrictEmail.getBy.mockResolvedValue(null);
      ParentCompany.getBy.mockResolvedValue(null);

      // Act
      await AuthController.existEmail(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ company: {}, message: 'Not exist' });
    });

    it('should return error when email is restricted', async () => {
      // Arrange
      const userData = { email: '<EMAIL>', phoneNumber: '**********' };
      const restrictEmail = { domainName: 'facebook.com', isActive: true };

      mockReq.body = userData;
      User.findOne.mockResolvedValue(null);
      RestrictEmail.getBy.mockResolvedValue(restrictEmail);

      // Act
      await AuthController.existEmail(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Please use your work email address to register',
          statusCode: status.BAD_REQUEST,
        }),
      );
    });

    it('should return error when user already exists', async () => {
      // Arrange
      const userData = { email: '<EMAIL>', phoneNumber: '**********' };
      const existingUser = { id: 1, email: '<EMAIL>' };

      mockReq.body = userData;
      User.findOne.mockResolvedValue(existingUser);

      // Act
      await AuthController.existEmail(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Email/Mobile Number already exist',
          statusCode: status.BAD_REQUEST,
        }),
      );
    });

    it('should return conflict when user has pending invitation', async () => {
      // Arrange
      const userData = { email: '<EMAIL>', phoneNumber: '**********' };
      const existingUser = { id: 1, email: '<EMAIL>' };
      const checkUserEmail = { id: 1, email: '<EMAIL>' };
      const userProjects = [
        {
          id: 1,
          status: 'pending',
          Project: { projectName: 'Test Project' },
        },
      ];

      mockReq.body = userData;
      User.findOne.mockResolvedValue(existingUser);
      User.findOne.mockResolvedValueOnce(existingUser).mockResolvedValueOnce(checkUserEmail);
      Member.findAll.mockResolvedValue(userProjects);

      // Act
      await AuthController.existEmail(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(status.CONFLICT);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: status.CONFLICT,
        message: 'You have already been invited to the Test Project',
      });
    });

    it('should return error when enterprise exists and is completed', async () => {
      // Arrange
      const userData = { email: '<EMAIL>', phoneNumber: '**********' };
      const parentCompany = { id: 1, emailDomainName: 'company.com' };
      const enterprise = { id: 1, ParentCompanyId: 1, status: 'completed' };

      mockReq.body = userData;
      User.findOne.mockResolvedValue(null);
      RestrictEmail.getBy.mockResolvedValue(null);
      ParentCompany.getBy.mockResolvedValue(parentCompany);
      Enterprise.findOne.mockResolvedValue(enterprise);

      // Act
      await AuthController.existEmail(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'This Email Domain already is in Enterprise level, Please Contact Account Admin',
          statusCode: status.BAD_REQUEST,
        }),
      );
    });

    it('should return success when parent company exists but no enterprise', async () => {
      // Arrange
      const userData = { email: '<EMAIL>', phoneNumber: '**********' };
      const parentCompany = { id: 1, emailDomainName: 'company.com' };
      const companyDetail = { id: 1, name: 'Test Company' };

      mockReq.body = userData;
      User.findOne.mockResolvedValue(null);
      RestrictEmail.getBy.mockResolvedValue(null);
      ParentCompany.getBy.mockResolvedValue(parentCompany);
      Enterprise.findOne.mockResolvedValue(null);
      ParentCompany.getCompany = jest.fn().mockResolvedValue(companyDetail);

      // Act
      await AuthController.existEmail(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        company: companyDetail,
        message: 'Not exist'
      });
    });
  });

  describe('login', () => {
    it('should login user successfully', async () => {
      // Arrange
      const userDetail = { id: 1, email: '<EMAIL>', token: 'jwt-token' };

      authService.login.mockImplementation((req, callback) => callback(userDetail, null));
      UserSerializer.serialize.mockReturnValue({ ...userDetail, serialized: true });

      // Act
      await AuthController.login(mockReq, mockRes, mockNext);

      // Assert
      expect(authService.login).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(UserSerializer.serialize).toHaveBeenCalledWith(userDetail);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: "Let's get to work!",
        token: 'jwt-token',
        userDetails: { ...userDetail, serialized: true },
      });
    });

    it('should handle login error', async () => {
      // Arrange
      const error = new Error('Invalid credentials');
      authService.login.mockImplementation((req, callback) => callback(null, error));

      // Act
      await AuthController.login(mockReq, mockRes, mockNext);

      // Assert
      expect(authService.login).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(error);
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Service error');
      authService.login.mockImplementation(() => {
        throw error;
      });

      // Act
      await AuthController.login(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('checkResetToken', () => {
    it('should return success when token exists', async () => {
      // Arrange
      const UserModel = { getBy: jest.fn() };
      const tokenData = { id: 1, resetPasswordToken: 'valid-token' };

      mockReq.params = { resetToken: 'valid-token' };
      authService.getDynamicModel.mockResolvedValue(UserModel);
      UserModel.getBy.mockResolvedValue(tokenData);

      // Act
      await AuthController.checkResetToken(mockReq, mockRes, mockNext);

      // Assert
      expect(authService.getDynamicModel).toHaveBeenCalledWith(mockReq);
      expect(UserModel.getBy).toHaveBeenCalledWith({ resetPasswordToken: 'valid-token' });
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Token exist.' });
    });

    it('should return error when token does not exist', async () => {
      // Arrange
      const UserModel = { getBy: jest.fn() };

      mockReq.params = { resetToken: 'invalid-token' };
      authService.getDynamicModel.mockResolvedValue(UserModel);
      UserModel.getBy.mockResolvedValue(null);

      // Act
      await AuthController.checkResetToken(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Reset Token expired',
          statusCode: status.BAD_REQUEST,
        }),
      );
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Service error');
      authService.getDynamicModel.mockRejectedValue(error);

      // Act
      await AuthController.checkResetToken(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('adminLogin', () => {
    it('should login admin successfully', async () => {
      // Arrange
      const adminData = { email: '<EMAIL>', password: 'admin123' };
      const adminDetail = { id: 1, email: '<EMAIL>', token: 'admin-token' };

      mockReq.body = adminData;
      authService.adminLogin.mockImplementation((data, callback) => callback(adminDetail, null));

      // Act
      await AuthController.adminLogin(mockReq, mockRes, mockNext);

      // Assert
      expect(authService.adminLogin).toHaveBeenCalledWith(adminData, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Login Successfully.',
        token: 'admin-token',
      });
    });

    it('should handle admin login error', async () => {
      // Arrange
      const adminData = { email: '<EMAIL>', password: 'wrongpassword' };
      const error = new Error('Invalid admin credentials');

      mockReq.body = adminData;
      authService.adminLogin.mockImplementation((data, callback) => callback(null, error));

      // Act
      await AuthController.adminLogin(mockReq, mockRes, mockNext);

      // Assert
      expect(authService.adminLogin).toHaveBeenCalledWith(adminData, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(error);
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Service error');
      authService.adminLogin.mockImplementation(() => {
        throw error;
      });

      // Act
      await AuthController.adminLogin(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('forgotPassword', () => {
    it('should send forgot password email successfully', async () => {
      // Arrange
      const UserModel = { findOne: jest.fn() };
      const user = { id: 1, email: '<EMAIL>' };

      mockReq.body = { email: '<EMAIL>' };
      authService.getDynamicModel.mockResolvedValue(UserModel);
      UserModel.findOne.mockResolvedValue(user);
      authService.forgotPassword.mockImplementation((user, callback) => callback({}, null));

      // Act
      await AuthController.forgotPassword(mockReq, mockRes, mockNext);

      // Assert
      expect(authService.getDynamicModel).toHaveBeenCalledWith(mockReq);
      expect(UserModel.findOne).toHaveBeenCalled();
      expect(authService.forgotPassword).toHaveBeenCalledWith(user, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Reset password email sent successfully',
      });
    });

    it('should handle forgot password error', async () => {
      // Arrange
      const UserModel = { findOne: jest.fn() };
      const user = { id: 1, email: '<EMAIL>' };
      const error = new Error('Email service error');

      mockReq.body = { email: '<EMAIL>' };
      authService.getDynamicModel.mockResolvedValue(UserModel);
      UserModel.findOne.mockResolvedValue(user);
      authService.forgotPassword.mockImplementation((user, callback) => callback(null, error));

      // Act
      await AuthController.forgotPassword(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Service error');
      authService.getDynamicModel.mockRejectedValue(error);

      // Act
      await AuthController.forgotPassword(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });

    it('should return error when user does not exist', async () => {
      // Arrange
      const UserModel = { findOne: jest.fn() };

      mockReq.body = { email: '<EMAIL>' };
      authService.getDynamicModel.mockResolvedValue(UserModel);
      UserModel.findOne.mockResolvedValue(null);

      // Act
      await AuthController.forgotPassword(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: "Email doesn't exist",
          statusCode: status.NOT_FOUND,
        }),
      );
    });

    it('should return error when user type is invalid', async () => {
      // Arrange
      const UserModel = { findOne: jest.fn() };
      const user = { id: 1, email: '<EMAIL>', userType: 'invalid_type' };

      mockReq.body = { email: '<EMAIL>' };
      authService.getDynamicModel.mockResolvedValue(UserModel);
      UserModel.findOne.mockResolvedValue(user);

      // Act
      await AuthController.forgotPassword(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: "Email doesn't exist",
          statusCode: status.NOT_FOUND,
        }),
      );
    });
  });

  describe('resetPasswordByEmail', () => {
    it('should reset password successfully', async () => {
      // Arrange
      const UserModel = { getBy: jest.fn() };
      const user = { id: 1, email: '<EMAIL>' };

      mockReq.params = { reset_password_token: 'valid-token' };
      mockReq.body = { password: 'newpassword123' };
      authService.getDynamicModel.mockResolvedValue(UserModel);
      UserModel.getBy.mockResolvedValue(user);
      authService.resetPassword = jest.fn().mockImplementation((req, user, params, callback) => callback({}, null));

      // Act
      await AuthController.resetPasswordByEmail(mockReq, mockRes, mockNext);

      // Assert
      expect(authService.getDynamicModel).toHaveBeenCalledWith(mockReq);
      expect(UserModel.getBy).toHaveBeenCalledWith({ resetPasswordToken: 'valid-token' });
      expect(authService.resetPassword).toHaveBeenCalledWith(mockReq, user, { password: 'newpassword123' }, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Password updated Successfully.' });
    });

    it('should handle reset password error', async () => {
      // Arrange
      const UserModel = { getBy: jest.fn() };
      const user = { id: 1, email: '<EMAIL>' };
      const error = new Error('Reset failed');

      mockReq.params = { reset_password_token: 'valid-token' };
      authService.getDynamicModel.mockResolvedValue(UserModel);
      UserModel.getBy.mockResolvedValue(user);
      authService.resetPassword = jest.fn().mockImplementation((req, user, params, callback) => callback(null, error));

      // Act
      await AuthController.resetPasswordByEmail(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });

    it('should return error when token does not exist', async () => {
      // Arrange
      const UserModel = { getBy: jest.fn() };

      mockReq.params = { reset_password_token: 'invalid-token' };
      authService.getDynamicModel.mockResolvedValue(UserModel);
      UserModel.getBy.mockResolvedValue(null);

      // Act
      await AuthController.resetPasswordByEmail(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Invalid Token',
          statusCode: status.BAD_REQUEST,
        }),
      );
    });
  });

  describe('removeFromInvitedProject', () => {
    it('should remove user from invited project successfully', async () => {
      // Arrange
      const user = { id: 1, email: '<EMAIL>' };
      const memberData = { id: 1, UserId: 1 };

      mockReq.body = { email: '<EMAIL>' };
      User.findOne.mockResolvedValue(user);
      Member.findOne = jest.fn().mockResolvedValue(memberData);
      Member.update = jest.fn().mockResolvedValue([1]);
      User.update = jest.fn().mockResolvedValue([1]);

      // Act
      await AuthController.removeFromInvitedProject(mockReq, mockRes, mockNext);

      // Assert
      expect(User.findOne).toHaveBeenCalled();
      expect(Member.findOne).toHaveBeenCalledWith({ where: { UserId: 1, isDeleted: false } });
      expect(Member.update).toHaveBeenCalledWith({ isDeleted: true }, { where: { id: 1, isDeleted: false } });
      expect(User.update).toHaveBeenCalledWith({ isDeleted: true }, { where: { id: 1 } });
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Successfully removed from the already invited project.!',
      });
    });

    it('should return error when user does not exist', async () => {
      // Arrange
      mockReq.body = { email: '<EMAIL>' };
      User.findOne.mockResolvedValue(null);

      // Act
      await AuthController.removeFromInvitedProject(mockReq, mockRes, mockNext);

      // Assert
      expect(User.findOne).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 404,
        message: 'Email Id not exist',
      });
    });
  });

  describe('getResendInviteLink', () => {
    it('should resend invite link successfully', async () => {
      // Arrange
      const user = { id: 1, email: '<EMAIL>' };
      const memberData = {
        id: 1,
        UserId: 1,
        ParentCompanyId: 1,
        Role: { roleName: 'member' }
      };
      const MAILER = require('../../mailer');

      mockReq.body = { email: '<EMAIL>' };
      User.findOne.mockResolvedValue(user);
      Member.findOne = jest.fn().mockResolvedValue(memberData);
      MAILER.sendMail = jest.fn().mockImplementation((data, template, subject, title, callback) => {
        callback({ messageId: '123' }, null);
      });

      // Act
      await AuthController.getResendInviteLink(mockReq, mockRes, mockNext);

      // Assert
      expect(User.findOne).toHaveBeenCalled();
      expect(Member.findOne).toHaveBeenCalledWith({
        where: { UserId: 1, isDeleted: false },
        include: [{ association: 'Role' }]
      });
      expect(MAILER.sendMail).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Invite Link sent successfully.!',
      });
    });

    it('should return error when user does not exist', async () => {
      // Arrange
      mockReq.body = { email: '<EMAIL>' };
      User.findOne.mockResolvedValue(null);

      // Act
      await AuthController.getResendInviteLink(mockReq, mockRes, mockNext);

      // Assert
      expect(User.findOne).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 404,
        message: 'Email Id not exist',
      });
    });

    it('should handle mailer error', async () => {
      // Arrange
      const user = { id: 1, email: '<EMAIL>' };
      const memberData = {
        id: 1,
        UserId: 1,
        ParentCompanyId: 1,
        Role: { roleName: 'member' }
      };
      const MAILER = require('../../mailer');
      const error = new Error('Email service error');

      mockReq.body = { email: '<EMAIL>' };
      User.findOne.mockResolvedValue(user);
      Member.findOne = jest.fn().mockResolvedValue(memberData);
      MAILER.sendMail = jest.fn().mockImplementation((data, template, subject, title, callback) => {
        callback(null, error);
      });

      // Act
      await AuthController.getResendInviteLink(mockReq, mockRes, mockNext);

      // Assert
      expect(MAILER.sendMail).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(null, expect.objectContaining({
        message: 'Email service error',
        statusCode: status.BAD_REQUEST,
      }));
    });
  });
});
