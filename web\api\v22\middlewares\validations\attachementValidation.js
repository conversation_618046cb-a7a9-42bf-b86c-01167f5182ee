const Joi = require('joi');

const attachementValidation = {
  createAttachement: {
    params: Joi.object({
      DeliveryRequestId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  createInspectionAttachement: {
    params: Joi.object({
      InspectionRequestId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  getAttachement: {
    params: Joi.object({
      DeliveryRequestId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  deleteAttachement: {
    params: Joi.object({
      id: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
};
module.exports = attachementValidation;
