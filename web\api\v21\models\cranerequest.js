const moment = require('moment');
const { Sequelize } = require('sequelize');

const { Op } = Sequelize;
module.exports = (sequelize, DataTypes) => {
  const CraneRequest = sequelize.define(
    'CraneRequest',
    {
      description: DataTypes.STRING,
      isDeleted: DataTypes.BOOLEAN,
      CompanyId: DataTypes.INTEGER,
      isEscortNeeded: DataTypes.BOOLEAN,
      additionalNotes: DataTypes.STRING,
      CraneRequestId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      status: {
        type: DataTypes.ENUM,
        values: ['Pending', 'Approved', 'Declined', 'Completed', 'Expired'],
      },
      approved_at: DataTypes.DATE,
      approvedBy: DataTypes.INTEGER,
      craneDeliveryStart: DataTypes.DATE,
      craneDeliveryEnd: DataTypes.DATE,
      ProjectId: DataTypes.INTEGER,
      pickUpLocation: DataTypes.STRING,
      dropOffLocation: DataTypes.STRING,
      createdBy: DataTypes.INTEGER,
      isAssociatedWithDeliveryRequest: DataTypes.BOOLEAN,
      requestType: DataTypes.STRING,
      recurrenceId: DataTypes.INTEGER,
      LocationId: DataTypes.INTEGER,
      isCreatedByGuestUser: DataTypes.BOOLEAN,
    },
    {},
  );
  CraneRequest.associate = (models) => {
    CraneRequest.hasMany(models.CraneRequestResponsiblePerson, {
      as: 'memberDetails',
      foreignKey: 'CraneRequestId',
    });
    CraneRequest.belongsTo(models.Member, {
      as: 'approverDetails',
      foreignKey: 'approvedBy',
    });
    CraneRequest.belongsTo(models.Project);
    CraneRequest.belongsTo(models.Member, {
      as: 'createdUserDetails',
      foreignKey: 'createdBy',
    });
    CraneRequest.hasMany(models.CraneRequestCompany, {
      as: 'companyDetails',
      foreignKey: 'CraneRequestId',
    });
    CraneRequest.hasMany(models.CraneRequestEquipment, {
      as: 'equipmentDetails',
      foreignKey: 'CraneRequestId',
    });
    CraneRequest.hasMany(models.CraneGate, {
      as: 'gateDetails',
      foreignKey: 'CraneRequestId',
    });
    CraneRequest.hasMany(models.CraneRequestDefinableFeatureOfWork, {
      as: 'defineWorkDetails',
      foreignKey: 'CraneRequestId',
    });
    CraneRequest.hasMany(models.VoidList, {
      as: 'voidList',
      foreignKey: 'CraneRequestId',
    });
    CraneRequest.belongsTo(models.RequestRecurrenceSeries, {
      as: 'recurrence',
      foreignKey: 'recurrenceId',
    });
    CraneRequest.belongsTo(models.Locations, {
      as: 'location',
      foreignKey: 'LocationId',
    });
    CraneRequest.hasMany(models.CraneRequestAttachment, {
      as: 'attachments',
      foreignKey: 'CraneRequestId',
    });
    CraneRequest.hasMany(models.CraneRequestComment, {
      as: 'comments',
      foreignKey: 'CraneRequestId',
    });
    CraneRequest.hasMany(models.CraneRequestHistory, {
      as: 'history',
      foreignKey: 'CraneRequestId',
    });
  };
  CraneRequest.createInstance = async (paramData) => {
    const newCraneRequest = await CraneRequest.create(paramData);
    return newCraneRequest;
  };
  const buildOrderQuery = (sortByFieldName, sortByColumnType) => {
    const orderMappings = {
      equipment: [['equipmentDetails', 'Equipment', 'equipmentName', sortByColumnType]],
      approvedUser: [['approverDetails', 'User', 'firstName', sortByColumnType]],
      company: [['companyDetails', 'Company', 'companyName', sortByColumnType]],
      dfow: [['defineWorkDetails', 'DeliverDefineWork', 'DFOW', sortByColumnType]],
      member: [['memberDetails', 'Member', 'User', 'firstName', sortByColumnType]],
      datetime: [['craneDeliveryStart', sortByColumnType]]
    };

    if (orderMappings[sortByFieldName]) {
      return orderMappings[sortByFieldName];
    }

    const directFields = [
      'description', 'id', 'status', 'pickUpLocation',
      'dropOffLocation', 'requestType'
    ];

    if (directFields.includes(sortByFieldName)) {
      return [[sortByFieldName, sortByColumnType]];
    }

    return null;
  };

  const buildSearchConditions = (commonSearch, search) => {
    if (!search) return commonSearch;

    return {
      [Op.and]: [
        {
          ...commonSearch,
          [Op.or]: [
            { description: { [Sequelize.Op.iLike]: `%${search}%` } },
            { pickUpLocation: { [Sequelize.Op.iLike]: `%${search}%` } },
            { dropOffLocation: { [Sequelize.Op.iLike]: `%${search}%` } },
            { '$equipmentDetails.Equipment.equipmentName$': { [Sequelize.Op.iLike]: `%${search}%` } },
            { '$location.locationPath$': { [Sequelize.Op.iLike]: `%${search}%` } }
          ]
        }
      ]
    };
  };

  const buildDateRangeCondition = (commonSearch, startdate, enddate, req) => {
    if (!startdate) return commonSearch;

    const startDateTime = moment(startdate, 'YYYY-MM-DD')
      .startOf('day')
      .utcOffset(Number(req.headers.timezoneoffset), true);
    const endDateTime = moment(enddate, 'YYYY-MM-DD')
      .endOf('day')
      .utcOffset(Number(req.headers.timezoneoffset), true);

    return {
      [Op.and]: [
        {
          ...commonSearch,
          [Op.or]: [
            {
              craneDeliveryStart: {
                [Op.between]: [moment(startDateTime), moment(endDateTime)]
              }
            }
          ]
        }
      ]
    };
  };

  const buildLocationCondition = (commonSearch, locationFilter) => {
    if (!locationFilter) return commonSearch;

    const isNumeric = !isNaN(parseFloat(locationFilter)) && isFinite(locationFilter);
    return {
      [Op.and]: [
        {
          ...commonSearch,
          [Op.or]: [
            isNumeric ? { '$location.id$': { [Op.eq]: parseInt(locationFilter, 10) } } : {},
            !isNumeric ? { '$location.locationPath$': { [Op.iLike]: `%${locationFilter}%` } } : {}
          ]
        }
      ]
    };
  };

  const buildFilterConditions = (commonSearch, filters) => {
    const {
      description: descriptionFilter,
      company: companyFilter,
      member: memberFilter,
      equipment: equipmentFilter,
      status: statusFilter,
      id: idFilter,
      pickFrom,
      pickTo
    } = filters;

    let conditions = { ...commonSearch };

    if (descriptionFilter) {
      conditions = {
        [Op.and]: [
          {
            ...conditions,
            [Op.or]: [{ description: { [Sequelize.Op.iLike]: `%${descriptionFilter}%` } }]
          }
        ]
      };
    }

    if (companyFilter && typeof companyFilter === 'string' && companyFilter !== '') {
      conditions = {
        [Op.and]: [
          {
            ...conditions,
            [Op.or]: [{ '$companyDetails.Company.companyName$': { [Sequelize.Op.iLike]: companyFilter } }]
          }
        ]
      };
    }

    if (memberFilter > 0) {
      conditions = {
        [Op.and]: [
          {
            ...conditions,
            [Op.or]: [{ '$memberDetails.Member.id$': +memberFilter }]
          }
        ]
      };
    }

    if (equipmentFilter) {
      conditions = {
        [Op.and]: [
          {
            ...conditions,
            [Op.or]: [{ '$equipmentDetails.Equipment.equipmentName$': { [Sequelize.Op.iLike]: equipmentFilter } }]
          }
        ]
      };
    }

    if (statusFilter) {
      conditions = {
        [Op.and]: [
          {
            ...conditions,
            [Op.or]: [{ status: statusFilter }]
          }
        ]
      };
    }

    if (idFilter) {
      conditions = {
        [Op.and]: [
          {
            ...conditions,
            [Op.or]: [{ CraneRequestId: idFilter }]
          }
        ]
      };
    }

    if (pickFrom) {
      conditions = {
        [Op.and]: [
          {
            ...conditions,
            [Op.or]: [{ pickUpLocation: { [Sequelize.Op.iLike]: `%${pickFrom}%` } }]
          }
        ]
      };
    }

    if (pickTo) {
      conditions = {
        [Op.and]: [
          {
            ...conditions,
            [Op.or]: [{ dropOffLocation: { [Sequelize.Op.iLike]: `%${pickTo}%` } }]
          }
        ]
      };
    }

    return conditions;
  };

  const buildWeeklyDateRangeCondition = (startDate, endDate, timezone, eventStartTime, eventEndTime, singleQuery) => {
    const startDateTime = moment(startDate, 'YYYY-MM-DD').format('YYYY-MM-DD');
    const endDateTime = moment(endDate, 'YYYY-MM-DD').format('YYYY-MM-DD');
    const nextDay = moment(startDate).add(1, 'days');
    const queryStartDate = nextDay.format('YYYY-MM-DD');

    if (singleQuery) {
      return {
        [Op.and]: [
          sequelize.literal(`(DATE_TRUNC('day', "CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDateTime}' AND '${endDateTime}'
            AND ("CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
            AND ("CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time <= '${eventEndTime}')`)
        ]
      };
    }

    return {
      [Op.or]: [
        sequelize.literal(`(DATE_TRUNC('day', "CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDateTime}' AND '${endDateTime}'
          AND ("CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
          AND ("CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time <= '23:59:59')`),
        sequelize.literal(`(DATE_TRUNC('day', "CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${queryStartDate}' AND '${endDateTime}'
          AND ("CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time >= '00:00:00'
          AND ("CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time <= '${eventEndTime}')`)
      ]
    };
  };

  CraneRequest.getAll = async (options) => {
    const {
      req,
      roleId,
      attr = {},
      filters = {},
      dateRange = {},
      search,
      order,
      sort,
      sortColumn
    } = options;

    let commonSearch = {
      ...attr,
      isDeleted: false,
    };

    const sortByFieldName = sortColumn || 'id';
    const sortByColumnType = order || sort || 'DESC';
    const orderQuery = buildOrderQuery(sortByFieldName, sortByColumnType);

    commonSearch = buildDateRangeCondition(commonSearch, dateRange.startdate, dateRange.enddate, req);
    commonSearch = buildLocationCondition(commonSearch, req.body.locationFilter);
    commonSearch = buildFilterConditions(commonSearch, filters);
    commonSearch = buildSearchConditions(commonSearch, search);

    const requiredCondition = roleId !== 2;

    return await CraneRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          required: requiredCondition,
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'UserId'],
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName']
                }
              ]
            }
          ]
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }]
        },
        {
          association: 'Project',
          attributes: ['projectName']
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName']
            }
          ]
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName']
            }
          ]
        },
        {
          required: false,
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }]
        },
        {
          required: false,
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              include: [
                {
                  required: true,
                  where: { isDeleted: false, isActive: true, isCraneType: true },
                  association: 'PresetEquipmentType',
                  attributes: ['id', 'equipmentType', 'isCraneType']
                }
              ],
              association: 'Equipment',
              attributes: ['equipmentName', 'id']
            }
          ]
        },
        {
          association: 'voidList',
          attributes: ['id', 'MemberId', 'ProjectId', 'CraneRequestId']
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id', 'recurrence', 'recurrenceStartDate', 'recurrenceEndDate',
            'dateOfMonth', 'monthlyRepeatType', 'repeatEveryCount', 'days',
            'requestType', 'repeatEveryType', 'chosenDateOfMonth', 'createdBy',
            'chosenDateOfMonthValue'
          ]
        },
        {
          association: 'location',
          where: { isDeleted: false },
          required: false,
          attributes: ['id', 'locationPath']
        }
      ],
      where: commonSearch,
      attributes: [
        'id', 'description', 'craneDeliveryStart', 'craneDeliveryEnd',
        'status', 'additionalNotes', 'CraneRequestId', 'approved_at',
        'isEscortNeeded', 'pickUpLocation', 'dropOffLocation',
        'isAssociatedWithDeliveryRequest', 'requestType', 'LocationId',
        'isCreatedByGuestUser'
      ],
      order: orderQuery
    });
  };
  CraneRequest.getCalendarData = async (attr, searchCondition, order, sort, sortColumn) => {
    const sortByFieldName = sortColumn || 'id';
    let sortByColumnType = sort || 'DESC';
    if (order) {
      sortByColumnType = order;
    }
    let orderQuery;
    if (sortByFieldName === 'equipment') {
      orderQuery = [['equipmentDetails', 'Equipment', 'equipmentName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'approvedUser') {
      orderQuery = [['approverDetails', 'User', 'firstName', `${sortByColumnType}`]];
    }
    if (
      sortByFieldName === 'description' ||
      sortByFieldName === 'deliveryStart' ||
      sortByFieldName === 'id' ||
      sortByFieldName === 'status'
    ) {
      orderQuery = [[`${sortByFieldName}`, `${sortByColumnType}`]];
    }
    const craneRequest = await CraneRequest.findAndCountAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id'],
              include: [{ association: 'User', attributes: ['email', 'firstName', 'lastName'] }],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'Project',
          attributes: ['projectName'],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          required: false,
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          association: 'location',
          where: { isDeleted: false },
          required: false,
        },
        // {
        //   association: 'voidList',
        //   attributes: ['id', 'MemberId', 'ProjectId', 'DeliveryRequestId'],
        // },
      ],
      where: { ...attr, ...searchCondition, isDeleted: false },
      attributes: [
        'id',
        'description',
        'craneDeliveryStart',
        'craneDeliveryEnd',
        'status',
        'additionalNotes',
        'CraneRequestId',
        'approved_at',
        'isEscortNeeded',
        'pickUpLocation',
        'dropOffLocation',
        'isAssociatedWithDeliveryRequest',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
      order: orderQuery,
    });
    return craneRequest;
  };
  CraneRequest.getSingleCraneRequestData = async (attr) => {
    const newCraneRequest = await CraneRequest.findOne({
      subQuery: false,
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'isGuestUser'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          required: false,
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
            'chosenDateOfMonthValue',
          ],
        },
        {
          association: 'location',
          where: { isDeleted: false },
          required: false,
        },
      ],
      where: { ...attr, isDeleted: false },
      attributes: [
        'id',
        'description',
        'craneDeliveryStart',
        'craneDeliveryEnd',
        'status',
        'additionalNotes',
        'CraneRequestId',
        'approved_at',
        'isEscortNeeded',
        'pickUpLocation',
        'dropOffLocation',
        'isAssociatedWithDeliveryRequest',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
    });
    return newCraneRequest;
  };
  CraneRequest.upcomingCraneRequest = async (condition, ProjectId) => {
    const commonSearch = {
      isDeleted: false,
      ProjectId,
      isAssociatedWithDeliveryRequest: false,
      requestType: 'craneRequest',
      craneDeliveryStart: { [Op.gt]: new Date() },
      ...condition,
    };
    const CraneRequestData = await CraneRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'UserId'],
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              attributes: ['companyName', 'id'],
            },
          ],
        },
        {
          association: 'location',
          where: { isDeleted: false },
          required: false,
        },
      ],
      where: commonSearch,
      attributes: [
        'id',
        'description',
        'craneDeliveryStart',
        'craneDeliveryEnd',
        'status',
        'additionalNotes',
        'CraneRequestId',
        'approved_at',
        'isEscortNeeded',
        'pickUpLocation',
        'dropOffLocation',
        'isAssociatedWithDeliveryRequest',
        'requestType',
        'ProjectId',
        'LocationId',
        'isCreatedByGuestUser',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
      order: [['craneDeliveryStart', 'ASC']],
    });
    return CraneRequestData;
  };
  CraneRequest.getWeeklyCalendarList = async (options) => {
    const {
      req,
      roleId,
      attr = {},
      start,
      end,
      timezone,
      eventStartTime,
      eventEndTime
    } = options;

    const startDate1 = req.body.startDate || start;
    const endDate1 = req.body.endDate || end;
    const finalFromTimeSeconds = timeToSeconds(eventStartTime);
    const finalToTimeSeconds = timeToSeconds(eventEndTime);
    const singleQuery = finalFromTimeSeconds <= finalToTimeSeconds;

    const commonSearch = buildWeeklyDateRangeCondition(startDate1, endDate1, timezone, eventStartTime, eventEndTime, singleQuery);

    const requiredCondition = roleId !== 2;

    return await CraneRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          required: requiredCondition,
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'UserId'],
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              attributes: ['companyName', 'id'],
            },
          ],
        },
        {
          association: 'Project',
          attributes: ['projectName'],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          required: false,
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          required: false,
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              include: [
                {
                  required: true,
                  where: { isDeleted: false, isActive: true, isCraneType: true },
                  association: 'PresetEquipmentType',
                  attributes: ['id', 'equipmentType', 'isCraneType'],
                },
              ],
              association: 'Equipment',
              attributes: ['equipmentName', 'id'],
            },
          ],
        },
        {
          association: 'voidList',
          attributes: ['id', 'MemberId', 'ProjectId', 'CraneRequestId'],
        },
        {
          association: 'location',
          where: { isDeleted: false },
          required: false,
        },
      ],
      where: { ...attr, ...commonSearch, isDeleted: false },
      attributes: [
        'id',
        'description',
        'craneDeliveryStart',
        'craneDeliveryEnd',
        'status',
        'additionalNotes',
        'CraneRequestId',
        'approved_at',
        'isEscortNeeded',
        'pickUpLocation',
        'dropOffLocation',
        'isAssociatedWithDeliveryRequest',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
      ],
    });
  };

  CraneRequest.upcomingCraneRequestForMobile = async (condition, ProjectId) => {
    const commonSearch = {
      isDeleted: false,
      ProjectId,
      isAssociatedWithDeliveryRequest: false,
      requestType: 'craneRequest',
      craneDeliveryStart: { [Op.gt]: new Date() },
      ...condition,
    };
    const CraneRequestData = await CraneRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'UserId'],
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              attributes: ['companyName', 'id'],
            },
          ],
        },
        {
          association: 'location',
          where: { isDeleted: false },
          required: false,
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          required: false,
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          required: false,
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              include: [
                {
                  required: true,
                  where: { isDeleted: false, isActive: true, isCraneType: true },
                  association: 'PresetEquipmentType',
                  attributes: ['id', 'equipmentType', 'isCraneType'],
                },
              ],
              association: 'Equipment',
              attributes: ['equipmentName', 'id'],
            },
          ],
        },
      ],
      where: commonSearch,
      limit: 2,
      attributes: [
        'id',
        'description',
        'craneDeliveryStart',
        'craneDeliveryEnd',
        'status',
        'additionalNotes',
        'CraneRequestId',
        'approved_at',
        'isEscortNeeded',
        'pickUpLocation',
        'dropOffLocation',
        'isAssociatedWithDeliveryRequest',
        'requestType',
        'ProjectId',
        'LocationId',
        'isCreatedByGuestUser',
      ],
      order: [['craneDeliveryStart', 'ASC']],
    });
    return CraneRequestData;
  };
  CraneRequest.guestSingleCraneRequestData = async (attr) => {
    const newCraneRequest = await CraneRequest.findOne({
      subQuery: false,
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
            'chosenDateOfMonthValue',
          ],
        },
        {
          association: 'location',
          where: { isDeleted: false },
          required: false,
        },
        {
          association: 'comments',
          required: false,
          include: [
            {
              association: 'Member',
              attributes: ['id'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'firstName', 'lastName', 'profilePic'],
                },
              ],
            },
          ],
          order: [['id', 'DESC']],
        },
        {
          association: 'attachments',
          required: false,
          where: { isDeleted: false },
        },
        {
          association: 'history',
          required: false,
          include: [
            {
              association: 'Member',
              include: [
                { association: 'User', attributes: ['firstName', 'lastName', 'profilePic'] },
              ],
            },
          ],
          order: [['id', 'DESC']],
        },
      ],
      where: { ...attr, isDeleted: false },
      attributes: [
        'id',
        'description',
        'craneDeliveryStart',
        'craneDeliveryEnd',
        'status',
        'additionalNotes',
        'CraneRequestId',
        'approved_at',
        'isEscortNeeded',
        'pickUpLocation',
        'dropOffLocation',
        'isAssociatedWithDeliveryRequest',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
    });
    return newCraneRequest;
  };

  return CraneRequest;
};
