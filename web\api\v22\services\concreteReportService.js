const { Sequelize, Enterprise } = require('../models');
let { Member, VoidList, User } = require('../models');
const moment = require('moment');
const { ConcreteRequest } = require('../models');
const exportService = require('./exportService');
const pdfConcreteReportService = require('./pdfConcreteReportService');
const csvConcreteReportService = require('./csvConcreteReportService');
const excelConcreteReportService = require('./excelConcreteReportService');
const awsConfig = require('../middlewares/awsConfig');
const deliveryReportService = require('./deliveryreportService');

const { Op } = Sequelize;
let publicUser;
let publicMember;
const helper = require('../helpers/domainHelper');

const concreteReportRequestService = {
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    const incomeData = inputData;
    let enterpriseValue;
    let ProjectId;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    Member = modelObj.Member;
    User = modelObj.User;
    VoidList = modelObj.VoidList;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return ProjectId;
  },
  async listConcreteRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;
      let order;
      if (params.void >= 1 && params.void <= 0) {
        done(null, { message: 'Please enter void as 1 or 0' });
      } else {
        const memberDetails = await Member.findOne({
          where: Sequelize.and({
            UserId: loginUser.id,
            ProjectId: params.ProjectId,
            isDeleted: false,
            isActive: true,
          }),
        });
        if (memberDetails) {
          const voidConcreteDelivery = [];
          const voidConcreteRequestList = await VoidList.findAll({
            where: {
              ProjectId: params.ProjectId,
              ConcreteRequestId: { [Op.ne]: null },
            },
          });
          voidConcreteRequestList.forEach(async (element) => {
            voidConcreteDelivery.push(element.ConcreteRequestId);
          });
          const offset = (+params.pageNo - 1) * +params.pageSize;
          const concreteCondition = {
            ProjectId: +params.ProjectId,
            isDeleted: false,
          };

          // if (incomeData.concreteSupplierFilter.length) {
          //   concreteCondition['$ConcreteSupplierDetails.Company.id$'] = {
          //     [Op.in]: incomeData.concreteSupplierFilter,
          //   };
          // }
          if (incomeData.statusFilter.length) {
            concreteCondition.status = {
              [Op.in]: incomeData.statusFilter,
            };
          }
          if (incomeData.locationFilter.length) {
            concreteCondition['$location.id$'] = {
              [Op.in]: incomeData.locationFilter,
            };
          }

          // if (incomeData.memberFilter.length) {
          //   concreteCondition['$location.id$'] = {
          //     [Op.eq]: incomeData.locationFilter,
          //   };
          // }

          if (params.void === '0' || params.void === 0) {
            concreteCondition['$ConcreteRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidConcreteDelivery }],
            };
          } else {
            concreteCondition['$ConcreteRequest.id$'] = {
              [Op.and]: [{ [Op.in]: voidConcreteDelivery }],
            };
          }
          if (incomeData.idFilter) {
            concreteCondition.ConcreteRequestId = incomeData.idFilter;
          }
          if (incomeData.quantityFilter) {
            concreteCondition.concreteQuantityOrdered = incomeData.quantityFilter;
          }
          if (incomeData.slumpFilter) {
            concreteCondition.slump = incomeData.slumpFilter;
          }
          if (incomeData.truckspacingFilter) {
            concreteCondition.truckSpacingHours = incomeData.truckspacingFilter;
          }
          if (incomeData.primerFilter) {
            concreteCondition.primerForPump = incomeData.primerFilter;
          }
          const roleId = memberDetails.RoleId;
          const memberId = memberDetails.id;
          const newResult = { count: 0, rows: [] };
          console.log(concreteCondition, "concreteCondition=====")
          const getConcreteRequest = await this.getAllConcreteRequest(
            inputData,
            concreteCondition,
            roleId,
            memberId,
            incomeData.descriptionFilter,
            incomeData.locationFilter,
            incomeData.concreteSupplierFilter,
            incomeData.orderNumberFilter,
            incomeData.statusFilter,
            incomeData.mixDesignFilter,
            incomeData.startdate,
            incomeData.enddate,
            incomeData.memberFilter,
            incomeData.search,
            order,
            incomeData.sort,
            incomeData.sortByField,
          );
          if (inputData.body.exportType) {
            newResult.rows = getConcreteRequest;
          } else {
            newResult.rows = getConcreteRequest.slice(offset, offset + +params.pageSize);
          }
          newResult.count = getConcreteRequest.length;
          done(newResult, false);
        } else {
          done(null, { message: 'Project Id/Member does not exist' });
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async exportReportForScheduler(req) {
    return new Promise(async (res, rej) => {
      try {
        await this.listConcreteRequest(req, async (response, error) => {
          if (!error) {
            if (response.count == 0) {
              return res('No Data Found');
            }
            if (req.body.exportType === 'PDF') {
              const loginUser = req.user;
              await pdfConcreteReportService.pdfFormatOfConcreteRequest(
                req.params,
                loginUser,
                response.rows,
                req,
                async (pdfFile, err) => {
                  if (!err) {
                    res(pdfFile);
                  } else {
                    rej(err);
                  }
                },
              );
            }
            if (req.body.exportType === 'EXCEL') {
              const workbook = await exportService.createWorkbook();
              let reportWorkbook = await excelConcreteReportService.concreteReport(
                workbook,
                response.rows,
                req.body.selectedHeaders,
                req.headers.timezoneoffset,
              );
              if (reportWorkbook) {
                reportWorkbook = await reportWorkbook.xlsx.writeBuffer();
                // console.log("******Buffer.isBuffer(reportWorkbook)******", Buffer.isBuffer(reportWorkbook));
                if (Buffer.isBuffer(reportWorkbook)) {
                  await awsConfig.reportUpload(
                    reportWorkbook,
                    req.body.reportName,
                    req.body.exportType === 'EXCEL' ? 'xlsx' : req.body.exportType,
                    async (result, error1) => {
                      if (!error1) {
                        res(result);
                      } else {
                        rej(error1);
                      }
                    },
                  );
                } else {
                  res('No data found');
                }
              } else {
                res('No data found');
              }
            }
            if (req.body.exportType === 'CSV') {
              await csvConcreteReportService.exportConcreteReportInCsvFormat(
                response.rows,
                req.body.selectedHeaders,
                req.headers.timezoneoffset,
                req.body.reportName,
                req.body.exportType,
                async (csvFile, err) => {
                  if (!err) {
                    res(csvFile);
                  } else {
                    rej(err);
                  }
                },
              );
            }
          } else {
            rej(error);
          }
        });
      } catch (e) {
        rej(e);
      }
    });
  },
  async exportReport(req, done) {
    await this.listConcreteRequest(req, async (response, error) => {
      console.log(error, "error=====")
      if (!error) {
        if (req.body.exportType === 'PDF') {
          const loginUser = req.user;
          await pdfConcreteReportService.pdfFormatOfConcreteRequest(
            req.params,
            loginUser,
            response.rows,
            req,
            async (pdfFile, err) => {
              if (!err) {
                if (req.body.saved) {
                  req.body.reportType = 'Concrete';
                  const savedData = await deliveryReportService.createSavedReports(req, pdfFile);
                  if (savedData) {
                    return done(pdfFile, false);
                  }
                  done(null, { message: 'cannot create reports' });
                } else {
                  return done(pdfFile, false);
                }
              }
            },
          );
        }
        if (req.body.exportType === 'EXCEL') {
          const workbook = await exportService.createWorkbook();
          let reportWorkbook = await excelConcreteReportService.concreteReport(
            workbook,
            response.rows,
            req.body.selectedHeaders,
            req.headers.timezoneoffset,
          );
          if (reportWorkbook) {
            if (req.body.saved) {
              reportWorkbook = await reportWorkbook.xlsx.writeBuffer();
              const excelFile = await deliveryReportService.saveExcelReport(
                reportWorkbook,
                req.body.reportName,
                req.body.exportType,
              );
              if (excelFile) {
                req.body.reportType = 'Concrete';
                const savedData = await deliveryReportService.createSavedReports(req, excelFile);
                return done(excelFile, false);
              }
              done(null, { message: 'cannot create reports' });
            } else {
              return done(reportWorkbook, false);
            }
          }
          done(null, { message: 'cannot export document' });
        }
        if (req.body.exportType === 'CSV') {
          await csvConcreteReportService.exportConcreteReportInCsvFormat(
            response.rows,
            req.body.selectedHeaders,
            req.headers.timezoneoffset,
            req.body.reportName,
            req.body.exportType,
            async (csvFile, err) => {
              if (!err) {
                if (req.body.saved) {
                  req.body.reportType = 'Concrete';
                  const savedData = await deliveryReportService.createSavedReports(req, csvFile);
                  if (savedData) {
                    return done(csvFile, false);
                  }
                  done(null, { message: 'cannot create reports' });
                } else {
                  return done(csvFile, false);
                }
              }
              return done(null, { message: 'cannot export document' });
            },
          );
        }
      }
    });
  },

  async getAllConcreteRequest(
    req,
    attr,
    roleId,
    memberId,
    descriptionFilter,
    locationFilter,
    concreteSupplierFilter,
    orderNumberFilter,
    statusFilter,
    mixDesignFilter,
    startdate,
    enddate,
    memberFilter,
    search,
    order,
    sort,
    sortColumn,
  ) {
    let commonSearch = {
      ...attr,
      isDeleted: false,
    };
    let locationPathFilter;
    if (req.body.locationPathFilter) {
      locationPathFilter = req.body.locationPathFilter;
    }
    const memberResponsibleCondition = {
      isDeleted: false,
    };
    const sortByFieldName = sortColumn || 'id';
    let sortByColumnType = sort || 'DESC';
    if (order) {
      sortByColumnType = order;
    }
    let orderQuery;
    if (sortByFieldName === 'member') {
      orderQuery = [['memberDetails', 'Member', 'User', 'firstName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'company') {
      orderQuery = [['concreteSupplierDetails', 'Company', 'companyName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'approvedUser') {
      orderQuery = [['approverDetails', 'User', 'firstName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'pumpsize') {
      orderQuery = [['pumpSizeDetails', 'ConcretePumpSize', 'pumpSize', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'mixDesign') {
      orderQuery = [['mixDesignDetails', 'ConcreteMixDesign', 'mixDesign', `${sortByColumnType}`]];
    }
    if (
      sortByFieldName === 'description' ||
      sortByFieldName === 'id' ||
      sortByFieldName === 'status' ||
      sortByFieldName === 'slump' ||
      sortByFieldName === 'concreteOrderNumber' ||
      sortByFieldName === 'truckSpacingHours' ||
      sortByFieldName === 'primerForPump' ||
      sortByFieldName === 'concreteQuantityOrdered' ||
      sortByFieldName === 'concretePlacementStart'
    ) {
      orderQuery = [[`${sortByFieldName}`, `${sortByColumnType}`]];
    }
    if (descriptionFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ description: { [Sequelize.Op.iLike]: `%${descriptionFilter}%` } }],
          },
        ],
      };
    }
    if (locationFilter.length) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$location.id$': {
                  [Op.in]: locationFilter,
                },
              },
            ],
          },
        ],
      };
    }
    if (locationPathFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$location.locationPath$': {
                  [Sequelize.Op.iLike]: `${locationPathFilter}`,
                },
              },
            ],
          },
        ],
      };
    }
    if (startdate) {
      const startDateTime = moment(startdate, 'YYYY-MM-DD')
        .startOf('day')
        .utcOffset(Number(req.headers.timezoneoffset), true);
      const endDateTime = moment(enddate, 'YYYY-MM-DD')
        .endOf('day')
        .utcOffset(Number(req.headers.timezoneoffset), true);
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                concretePlacementStart: {
                  [Op.between]: [moment(startDateTime), moment(endDateTime)],
                },
              },
            ],
          },
        ],
      };
    } else {
      const startDateTime = moment(new Date(), 'YYYY-MM-DD')
        .startOf('day')
        .utcOffset(Number(req.headers.timezoneoffset), true);
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                concretePlacementStart: {
                  [Op.gte]: moment(startDateTime),
                },
              },
            ],
          },
        ],
      };
    }
    if (concreteSupplierFilter?.length) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$concreteSupplierDetails.Company.id$': {
                  [Op.in]: concreteSupplierFilter,
                },
              },
            ],
          },
        ],
      };
    }
    if (orderNumberFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ concreteOrderNumber: { [Sequelize.Op.iLike]: `%${orderNumberFilter}%` } }],
          },
        ],
      };
    }
    if (statusFilter.length) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ status: statusFilter }],
          },
        ],
      };
    }
    if (memberFilter > 0) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$memberDetails.Member.id$': +memberFilter,
              },
            ],
          },
        ],
      };
    }
    if (mixDesignFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$mixDesignDetails.ConcreteMixDesign.mixDesign$': {
                  [Sequelize.Op.iLike]: `%${mixDesignFilter}%`,
                },
              },
            ],
          },
        ],
      };
    }
    if (search) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              { description: { [Sequelize.Op.iLike]: `%${search}%` } },
              { concreteOrderNumber: { [Sequelize.Op.iLike]: `%${search}%` } },
              { slump: { [Sequelize.Op.iLike]: `%${search}%` } },
              { primerForPump: { [Sequelize.Op.iLike]: `%${search}%` } },
              { status: { [Sequelize.Op.iLike]: `%${search}%` } },
              { pumpLocation: { [Sequelize.Op.iLike]: `%${search}%` } },
              { cubicYardsTotal: { [Sequelize.Op.iLike]: `%${search}%` } },
              { concreteQuantityOrdered: { [Sequelize.Op.iLike]: `%${search}%` } },
              { truckSpacingHours: { [Sequelize.Op.iLike]: `%${search}%` } },
              { notes: { [Sequelize.Op.iLike]: `%${search}%` } },
              { hoursToCompletePlacement: { [Sequelize.Op.iLike]: `%${search}%` } },
              { minutesToCompletePlacement: { [Sequelize.Op.iLike]: `%${search}%` } },
              {
                '$location.locationPath$': {
                  [Sequelize.Op.iLike]: `%${search}%`,
                },
              },
              {
                '$mixDesignDetails.ConcreteMixDesign.mixDesign$': {
                  [Sequelize.Op.iLike]: `%${search}%`,
                },
              },
              {
                '$pumpSizeDetails.ConcretePumpSize.pumpSize$': {
                  [Sequelize.Op.iLike]: `%${search}%`,
                },
              },
              {
                '$concreteSupplierDetails.Company.companyName$': {
                  [Sequelize.Op.iLike]: `%${search}%`,
                },
              },
            ],
          },
        ],
      };
    }
    // if (roleId === 4) {
    //   memberResponsibleCondition = {
    //     ...memberResponsibleCondition,
    //     id: memberId,
    //   };
    // }
    const requiredCondition = false;
    // if (roleId === 4) {
    //   requiredCondition = true;
    // }
    const newConcreteRequest = await ConcreteRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          required: requiredCondition,
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              where: memberResponsibleCondition,
              attributes: ['id', 'UserId'],
              include: [
                {
                  association: 'User',
                  where: { isDeleted: false },
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'Project',
          where: { isDeleted: false },
          attributes: ['id', 'projectName'],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              where: { isDeleted: false },
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'locationDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteLocation', attributes: ['location', 'id'] }],
        },
        {
          association: 'mixDesignDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteMixDesign', attributes: ['mixDesign', 'id'] }],
        },
        {
          association: 'pumpSizeDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcretePumpSize', attributes: ['pumpSize', 'id'] }],
        },
        {
          association: 'concreteSupplierDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'voidList',
          attributes: ['id', 'MemberId', 'ProjectId', 'ConcreteRequestId'],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'location',
          required: false,
          attributes: ['id', 'locationPath'],
        },
      ],
      where: commonSearch,
      attributes: [
        'id',
        'description',
        'concreteOrderNumber',
        'truckSpacingHours',
        'notes',
        'slump',
        'concreteQuantityOrdered',
        'concreteConfirmedOn',
        'isConcreteConfirmed',
        'pumpLocation',
        'pumpOrderedDate',
        'pumpWorkStart',
        'pumpWorkEnd',
        'pumpConfirmedOn',
        'isPumpConfirmed',
        'isPumpRequired',
        'cubicYardsTotal',
        'hoursToCompletePlacement',
        'minutesToCompletePlacement',
        'status',
        'ProjectId',
        'ConcreteRequestId',
        'requestType',
        'primerForPump',
        'concretePlacementStart',
        'concretePlacementEnd',
        'createdBy',
        'isCreatedByGuestUser',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
      order: orderQuery,
    });
    return newConcreteRequest;
  }
};
module.exports = concreteReportRequestService;
