const status = require('http-status');
const {
    Sequelize,
    WasteLog
} = require('../models');

const wasteLogService = {
    async addWasteLog(equData, done) {
        try {
            // await this.getDynamicModel(equData);
            const inputData = equData.body;
            const newWasteLog = await WasteLog.createWasteLog(inputData);
            done(newWasteLog, false);
        } catch (e) {
            done(null, e);
        }
    },
    async listWasteLog(inputData, done) {
        try {
            // await this.getDynamicModel(inputData);
            const { params } = inputData;
            const incomeData = inputData.body;
            let searchCondition = {};
            const pageNumber = +params.pageNo;
            const pageSize = +params.pageSize;
            const { sort } = inputData.body;
            const { sortByField } = inputData.body;
            const offset = (pageNumber - 1) * pageSize;
            const condition = {
                isDeleted: false,
                projectId: inputData.query.projectId,
            };
            const wasteLogData = await WasteLog.getAll(
                condition,
                pageSize,
                offset,
                searchCondition,
                sort,
                sortByField,
            );
            if (incomeData && incomeData.isFilter) {
                if (wasteLogData.rows) {
                    if (wasteLogData.rows.length > 0) {
                        wasteLogData.rows.sort((a, b) =>
                            a.wasteLogName.toLowerCase() > b.wasteLogName.toLowerCase() ? 1 : -1,
                        );
                    }
                } else if (wasteLogData.length > 0) {
                    wasteLogData.sort((a, b) =>
                        a.wasteLogName.toLowerCase() > b.wasteLogName.toLowerCase() ? 1 : -1,
                    );
                }
            }

            done(wasteLogData, false);
        } catch (e) {
            done(null, e);
        }
    },
};
module.exports = wasteLogService;
