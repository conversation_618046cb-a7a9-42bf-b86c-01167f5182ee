const moment = require('moment');
const { Sequelize } = require('sequelize');

const { Op } = Sequelize;
module.exports = (sequelize, DataTypes) => {
  const ConcreteRequest = sequelize.define(
    'ConcreteRequest',
    {
      description: DataTypes.STRING,
      concreteOrderNumber: DataTypes.STRING,
      truckSpacingHours: DataTypes.STRING,
      notes: DataTypes.STRING,
      slump: DataTypes.STRING,
      concreteQuantityOrdered: DataTypes.STRING,
      concreteConfirmedOn: DataTypes.DATE,
      isConcreteConfirmed: DataTypes.BOOLEAN,
      pumpLocation: DataTypes.STRING,
      pumpOrderedDate: DataTypes.STRING,
      pumpWorkStart: DataTypes.DATE,
      pumpWorkEnd: DataTypes.DATE,
      pumpConfirmedOn: DataTypes.DATE,
      isPumpConfirmed: DataTypes.BOOLEAN,
      isPumpRequired: DataTypes.BOOLEAN,
      cubicYardsTotal: DataTypes.STRING,
      hoursToCompletePlacement: DataTypes.STRING,
      minutesToCompletePlacement: DataTypes.STRING,
      isDeleted: DataTypes.BOOLEAN,
      status: DataTypes.STRING,
      ProjectId: DataTypes.INTEGER,
      publicSchemaId: DataTypes.INTEGER,
      ConcreteRequestId: DataTypes.INTEGER,
      requestType: DataTypes.STRING,
      primerForPump: DataTypes.STRING,
      concretePlacementStart: DataTypes.DATE,
      concretePlacementEnd: DataTypes.DATE,
      createdBy: DataTypes.INTEGER,
      recurrenceId: DataTypes.INTEGER,
      LocationId: DataTypes.INTEGER,
      isCreatedByGuestUser: DataTypes.BOOLEAN,
      // chosenDateOfMonth: {
      //   type: DataTypes.BOOLEAN,
      // },
      // dateOfMonth: {
      //   type: DataTypes.STRING,
      // },
      // monthlyRepeatType: {
      //   type: DataTypes.STRING,
      // },
      // recurrence: {
      //   type: DataTypes.ENUM,
      //   values: ['Does Not Repeat', 'Daily', 'Weekly', 'Monthly', 'Yearly', 'Custom'],
      // },
      // repeatEveryCount: DataTypes.STRING,
      // repeatEveryType: DataTypes.STRING,
      // days: DataTypes.ARRAY(DataTypes.STRING),
      approvedBy: DataTypes.INTEGER,
      approved_at: DataTypes.DATE,
      OriginationAddress: DataTypes.TEXT,
      vehicleType: DataTypes.TEXT,
      OriginationAddressPump: DataTypes.TEXT,
      vehicleTypePump: DataTypes.TEXT,
    },
    {},
  );

  ConcreteRequest.associate = (models) => {
    ConcreteRequest.hasMany(models.ConcreteRequestResponsiblePerson, {
      as: 'memberDetails',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.belongsTo(models.Project);
    ConcreteRequest.belongsTo(models.Member, {
      as: 'createdUserDetails',
      foreignKey: 'createdBy',
    });
    ConcreteRequest.hasMany(models.ConcreteRequestLocation, {
      as: 'locationDetails',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.hasMany(models.ConcreteRequestMixDesign, {
      as: 'mixDesignDetails',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.hasMany(models.ConcreteRequestPumpSize, {
      as: 'pumpSizeDetails',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.hasMany(models.ConcreteRequestCompany, {
      as: 'concreteSupplierDetails',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.hasMany(models.VoidList, {
      as: 'voidList',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.hasMany(models.ConcreteGate, {
      as: 'gateDetails',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.hasMany(models.ConcreteEquipment, {
      as: 'equipmentDetails',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.belongsTo(models.Member, {
      as: 'approverDetails',
      foreignKey: 'approvedBy',
    });
    ConcreteRequest.belongsTo(models.RequestRecurrenceSeries, {
      as: 'recurrence',
      foreignKey: 'recurrenceId',
    });
    ConcreteRequest.belongsTo(models.Locations, {
      as: 'location',
      foreignKey: 'LocationId',
    });
    ConcreteRequest.hasMany(models.ConcreteRequestAttachment, {
      as: 'attachments',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.hasMany(models.ConcreteRequestComment, {
      as: 'comments',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequest.hasMany(models.ConcreteRequestHistory, {
      as: 'history',
      foreignKey: 'ConcreteRequestId',
    });
  };
  ConcreteRequest.createInstance = async (paramData) => {
    const concreteRequest = await ConcreteRequest.create(paramData);
    return concreteRequest;
  };

  ConcreteRequest.getAll = async (
    req,
    attr,
    roleId,
    memberId,
    descriptionFilter,
    locationFilter,
    concreteSupplierFilter,
    orderNumberFilter,
    statusFilter,
    mixDesignFilter,
    startdate,
    enddate,
    memberFilter,
    search,
    order,
    sort,
    sortColumn,
  ) => {
    let commonSearch = {
      ...attr,
      isDeleted: false,
    };
    let locationPathFilter;
    if (req.body.locationPathFilter) {
      locationPathFilter = req.body.locationPathFilter;
    }
    const memberResponsibleCondition = {
      isDeleted: false,
    };
    const sortByFieldName = sortColumn || 'id';
    let sortByColumnType = sort || 'DESC';
    if (order) {
      sortByColumnType = order;
    }
    let orderQuery;
    if (sortByFieldName === 'member') {
      orderQuery = [['memberDetails', 'Member', 'User', 'firstName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'company') {
      orderQuery = [['concreteSupplierDetails', 'Company', 'companyName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'approvedUser') {
      orderQuery = [['approverDetails', 'User', 'firstName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'pumpsize') {
      orderQuery = [['pumpSizeDetails', 'ConcretePumpSize', 'pumpSize', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'mixDesign') {
      orderQuery = [['mixDesignDetails', 'ConcreteMixDesign', 'mixDesign', `${sortByColumnType}`]];
    }
    if (
      sortByFieldName === 'description' ||
      sortByFieldName === 'id' ||
      sortByFieldName === 'status' ||
      sortByFieldName === 'slump' ||
      sortByFieldName === 'concreteOrderNumber' ||
      sortByFieldName === 'truckSpacingHours' ||
      sortByFieldName === 'primerForPump' ||
      sortByFieldName === 'concreteQuantityOrdered' ||
      sortByFieldName === 'concretePlacementStart'
    ) {
      orderQuery = [[`${sortByFieldName}`, `${sortByColumnType}`]];
    }
    if (descriptionFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ description: { [Sequelize.Op.iLike]: `%${descriptionFilter}%` } }],
          },
        ],
      };
    }
    if (locationFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$location.id$': {
                  [Op.eq]: locationFilter,
                },
              },
            ],
          },
        ],
      };
    }
    if (locationPathFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$location.locationPath$': {
                  [Sequelize.Op.iLike]: `${locationPathFilter}`,
                },
              },
            ],
          },
        ],
      };
    }
    if (startdate) {
      const startDateTime = moment(startdate, 'YYYY-MM-DD')
        .startOf('day')
        .utcOffset(Number(req.headers.timezoneoffset), true);
      const endDateTime = moment(enddate, 'YYYY-MM-DD')
        .endOf('day')
        .utcOffset(Number(req.headers.timezoneoffset), true);
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                concretePlacementStart: {
                  [Op.between]: [moment(startDateTime), moment(endDateTime)],
                },
              },
            ],
          },
        ],
      };
    }
    if (concreteSupplierFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$concreteSupplierDetails.Company.companyName$': {
                  [Sequelize.Op.iLike]: `%${concreteSupplierFilter}%`,
                },
              },
            ],
          },
        ],
      };
    }
    if (orderNumberFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ concreteOrderNumber: { [Sequelize.Op.iLike]: `%${orderNumberFilter}%` } }],
          },
        ],
      };
    }
    if (statusFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ status: statusFilter }],
          },
        ],
      };
    }
    if (memberFilter > 0) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$memberDetails.Member.id$': +memberFilter,
              },
            ],
          },
        ],
      };
    }
    if (mixDesignFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$mixDesignDetails.ConcreteMixDesign.mixDesign$': {
                  [Sequelize.Op.iLike]: `%${mixDesignFilter}%`,
                },
              },
            ],
          },
        ],
      };
    }
    if (search) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              { description: { [Sequelize.Op.iLike]: `%${search}%` } },
              { concreteOrderNumber: { [Sequelize.Op.iLike]: `%${search}%` } },
              { slump: { [Sequelize.Op.iLike]: `%${search}%` } },
              { primerForPump: { [Sequelize.Op.iLike]: `%${search}%` } },
              { status: { [Sequelize.Op.iLike]: `%${search}%` } },
              { pumpLocation: { [Sequelize.Op.iLike]: `%${search}%` } },
              { cubicYardsTotal: { [Sequelize.Op.iLike]: `%${search}%` } },
              { concreteQuantityOrdered: { [Sequelize.Op.iLike]: `%${search}%` } },
              { truckSpacingHours: { [Sequelize.Op.iLike]: `%${search}%` } },
              { notes: { [Sequelize.Op.iLike]: `%${search}%` } },
              { hoursToCompletePlacement: { [Sequelize.Op.iLike]: `%${search}%` } },
              { minutesToCompletePlacement: { [Sequelize.Op.iLike]: `%${search}%` } },
              {
                '$location.locationPath$': {
                  [Sequelize.Op.iLike]: `%${search}%`,
                },
              },
              {
                '$mixDesignDetails.ConcreteMixDesign.mixDesign$': {
                  [Sequelize.Op.iLike]: `%${search}%`,
                },
              },
              {
                '$pumpSizeDetails.ConcretePumpSize.pumpSize$': {
                  [Sequelize.Op.iLike]: `%${search}%`,
                },
              },
              {
                '$concreteSupplierDetails.Company.companyName$': {
                  [Sequelize.Op.iLike]: `%${search}%`,
                },
              },
            ],
          },
        ],
      };
    }
    // if (roleId === 4) {
    //   memberResponsibleCondition = {
    //     ...memberResponsibleCondition,
    //     id: memberId,
    //   };
    // }
    const requiredCondition = false;
    // if (roleId === 4) {
    //   requiredCondition = true;
    // }
    const newConcreteRequest = await ConcreteRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          required: requiredCondition,
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              where: memberResponsibleCondition,
              attributes: ['id', 'UserId'],
              include: [
                {
                  association: 'User',
                  where: { isDeleted: false },
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          required: false,
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          association: 'Project',
          where: { isDeleted: false },
          attributes: ['id', 'projectName'],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              where: { isDeleted: false },
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'locationDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteLocation', attributes: ['location', 'id'] }],
        },
        {
          association: 'mixDesignDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteMixDesign', attributes: ['mixDesign', 'id'] }],
        },
        {
          association: 'pumpSizeDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcretePumpSize', attributes: ['pumpSize', 'id'] }],
        },
        {
          association: 'concreteSupplierDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'voidList',
          attributes: ['id', 'MemberId', 'ProjectId', 'ConcreteRequestId'],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'location',
          required: false,
          attributes: ['id', 'locationPath'],
        },
      ],
      where: commonSearch,
      attributes: [
        'id',
        'description',
        'concreteOrderNumber',
        'truckSpacingHours',
        'notes',
        'slump',
        'concreteQuantityOrdered',
        'concreteConfirmedOn',
        'isConcreteConfirmed',
        'pumpLocation',
        'pumpOrderedDate',
        'pumpWorkStart',
        'pumpWorkEnd',
        'pumpConfirmedOn',
        'isPumpConfirmed',
        'isPumpRequired',
        'cubicYardsTotal',
        'hoursToCompletePlacement',
        'minutesToCompletePlacement',
        'status',
        'ProjectId',
        'ConcreteRequestId',
        'requestType',
        'primerForPump',
        'concretePlacementStart',
        'concretePlacementEnd',
        'createdBy',
        'isCreatedByGuestUser',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
      order: orderQuery,
    });
    return newConcreteRequest;
  };

  ConcreteRequest.getSingleConcreteRequestData = async (attr) => {
    const newConcreteRequest = await ConcreteRequest.findOne({
      subQuery: false,
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              where: { isDeleted: false },
              attributes: ['id', 'isGuestUser'],
              include: [
                {
                  association: 'User',
                  where: { isDeleted: false },
                  attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              where: { isDeleted: false },
              attributes: ['email', 'id', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          required: false,
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          required: false,
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          association: 'locationDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteLocation', attributes: ['location', 'id'] }],
        },
        {
          association: 'mixDesignDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteMixDesign', attributes: ['mixDesign', 'id'] }],
        },
        {
          association: 'pumpSizeDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcretePumpSize', attributes: ['pumpSize', 'id'] }],
        },
        {
          association: 'concreteSupplierDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              attributes: ['companyName', 'id'],
            },
          ],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
            'chosenDateOfMonthValue',
          ],
        },
        {
          association: 'location',
          required: false,
          attributes: ['id', 'locationPath'],
        },
      ],
      where: { ...attr, isDeleted: false },
      attributes: [
        'id',
        'description',
        'concreteOrderNumber',
        'truckSpacingHours',
        'notes',
        'slump',
        'concreteQuantityOrdered',
        'concreteConfirmedOn',
        'isConcreteConfirmed',
        'pumpLocation',
        'pumpOrderedDate',
        'pumpWorkStart',
        'pumpWorkEnd',
        'pumpConfirmedOn',
        'isPumpConfirmed',
        'isPumpRequired',
        'cubicYardsTotal',
        'hoursToCompletePlacement',
        'minutesToCompletePlacement',
        'status',
        'ProjectId',
        'ConcreteRequestId',
        'requestType',
        'primerForPump',
        'concretePlacementStart',
        'concretePlacementEnd',
        'createdBy',
        'isCreatedByGuestUser',
        'OriginationAddress',
        'vehicleType',
        'OriginationAddressPump',
        'vehicleTypePump',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
    });
    return newConcreteRequest;
  };

  ConcreteRequest.upcomingConcreteRequest = async (condition, ProjectId) => {
    const commonSearch = {
      isDeleted: false,
      ProjectId,
      concretePlacementStart: { [Op.gt]: new Date() },
      requestType: 'concreteRequest',
      ...condition,
    };
    const CraneRequestData = await ConcreteRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'UserId'],
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'concreteSupplierDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              attributes: ['companyName', 'id'],
            },
          ],
        },
        {
          association: 'locationDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteLocation', attributes: ['location', 'id'] }],
        },
        {
          association: 'location',
          required: false,
          attributes: ['id', 'locationPath'],
        },
      ],
      where: commonSearch,
      attributes: [
        'id',
        'description',
        'concreteOrderNumber',
        'truckSpacingHours',
        'notes',
        'slump',
        'concreteQuantityOrdered',
        'concreteConfirmedOn',
        'isConcreteConfirmed',
        'pumpLocation',
        'pumpOrderedDate',
        'pumpWorkStart',
        'pumpWorkEnd',
        'pumpConfirmedOn',
        'isPumpConfirmed',
        'isPumpRequired',
        'cubicYardsTotal',
        'hoursToCompletePlacement',
        'minutesToCompletePlacement',
        'status',
        'ProjectId',
        'ConcreteRequestId',
        'requestType',
        'primerForPump',
        'concretePlacementStart',
        'concretePlacementEnd',
        'createdBy',
        'isCreatedByGuestUser',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
      order: [['concretePlacementStart', 'ASC']],
    });
    return CraneRequestData;
  };

  ConcreteRequest.getWeeklyCalendarList = async (
    req,
    attr,
    roleId,
    memberId,
    companyFilter,
    statusFilter,
    startDate,
    endDate,
    memberFilter,
    start,
    end,
    startTime,
    endTime,
    typeFormat,
    timezone,
    eventStartTime,
    eventEndTime,
  ) => {
    let commonSearch = {
      ...attr,
      isDeleted: false,
    };
    let memberResponsibleCondition = {
      isDeleted: false,
    };
    let locationFilter;
    if (req.body.locationFilter) {
      locationFilter = req.body.locationFilter;
    }
    let startDate1 = start;
    let endDate1 = end;
    if (req.body.startDate && req.body.endDate) {
      startDate1 = req.body.startDate;
      endDate1 = req.body.endDate;
    }
    const finalFromTimeSeconds = timeToSeconds(eventStartTime);
    const finalToTimeSeconds = timeToSeconds(eventEndTime);

    function timeToSeconds(timeString) {
      const [hours, minutes, seconds] = timeString.split(':');
      return +hours * 60 * 60 + +minutes * 60 + +seconds;
    }

    let singleQuery = true;

    if (finalFromTimeSeconds > finalToTimeSeconds) {
      singleQuery = false;
    } else {
      singleQuery = true;
    }
    if (typeFormat) {
      if (start) {
        const startDateTime = moment(startDate1, 'YYYY-MM-DD');
        const endDateTime = moment(endDate1, 'YYYY-MM-DD');
        const nextDay = moment(startDate1).add(1, 'days');
        const queryStartDate = nextDay.format('YYYY-MM-DD');
        if (singleQuery) {
          commonSearch = {
            [Op.and]: [
              sequelize.literal(`(DATE_TRUNC('day', "ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}') BETWEEN '${startDateTime}' AND '${endDateTime}'
                                    AND ("ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                                    AND ("ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}')::time <= '${eventEndTime}')`),
            ],
          };
        } else {
          commonSearch = {
            [Op.or]: [
              sequelize.literal(`(DATE_TRUNC('day', "ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}') BETWEEN '${startDateTime}' AND '${endDateTime}'
                                    AND ("ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                                    AND ("ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}')::time <= '23:59:59')`),
              sequelize.literal(`(DATE_TRUNC('day', "ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}') BETWEEN '${queryStartDate}' AND '${endDateTime}'
                                    AND ("ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}')::time >= '00:00:00'
                                    AND ("ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}')::time <=  '${eventEndTime}')`),
            ],
          };
        }
      }
    } else {
      const nextDay = moment(startDate1).add(1, 'days');
      const queryStartDate = nextDay.format('YYYY-MM-DD');
      if (singleQuery) {
        commonSearch = {
          [Op.and]: [
            sequelize.literal(`(DATE_TRUNC('day', "ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate1}' AND '${endDate1}'
                                    AND ("ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                                    AND ("ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}')::time <= '${eventEndTime}')`),
          ],
        };
      } else {
        commonSearch = {
          [Op.or]: [
            sequelize.literal(`(DATE_TRUNC('day', "ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate1}' AND '${endDate1}'
                                    AND ("ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                                    AND ("ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}')::time <= '23:59:59')`),
            sequelize.literal(`(DATE_TRUNC('day', "ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}') BETWEEN '${queryStartDate}' AND '${endDate1}'
                                    AND ("ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}')::time >= '00:00:00'
                                    AND ("ConcreteRequest"."concretePlacementStart" AT TIME ZONE '${timezone}')::time <=  '${eventEndTime}')`),
          ],
        };
      }
    }
    if (companyFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$concreteSupplierDetails.Company.id$': +companyFilter,
              },
            ],
          },
        ],
      };
    }
    if (statusFilter) {
      if (statusFilter === 'Pending') {
        commonSearch = {
          [Op.and]: [
            {
              ...commonSearch,
              [Op.or]: [{ status: 'Tentative' }],
            },
          ],
        };
      } else if (statusFilter === 'Delivered') {
        commonSearch = {
          [Op.and]: [
            {
              ...commonSearch,
              [Op.or]: [{ status: 'Completed' }],
            },
          ],
        };
      } else {
        commonSearch = {
          [Op.and]: [
            {
              ...commonSearch,
              [Op.or]: [{ status: statusFilter }],
            },
          ],
        };
      }
    }
    if (memberFilter > 0) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$memberDetails.Member.id$': +memberFilter,
              },
            ],
          },
        ],
      };
    }
    if (roleId === 4) {
      memberResponsibleCondition = {
        ...memberResponsibleCondition,
        id: memberId,
      };
    }
    if (locationFilter !== 0) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$location.id$': {
                  [Op.eq]: locationFilter,
                },
              },
            ],
          },
        ],
      };
    }
    let requiredCondition = false;
    if (roleId === 4) {
      requiredCondition = true;
    }
    const newConcreteRequest = await ConcreteRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          required: requiredCondition,
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              where: memberResponsibleCondition,
              attributes: ['id', 'UserId'],
              include: [
                {
                  association: 'User',
                  where: { isDeleted: false },
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'Project',
          where: { isDeleted: false },
          attributes: ['id', 'projectName'],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              where: { isDeleted: false },
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'locationDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteLocation', attributes: ['location', 'id'] }],
        },
        {
          association: 'mixDesignDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteMixDesign', attributes: ['mixDesign', 'id'] }],
        },
        {
          association: 'pumpSizeDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcretePumpSize', attributes: ['pumpSize', 'id'] }],
        },
        {
          association: 'concreteSupplierDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'voidList',
          attributes: ['id', 'MemberId', 'ProjectId', 'ConcreteRequestId'],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'location',
          required: false,
          attributes: ['id', 'locationPath'],
        },
      ],
      where: { ...attr, ...commonSearch },
      attributes: [
        'id',
        'description',
        'concreteOrderNumber',
        'truckSpacingHours',
        'notes',
        'slump',
        'concreteQuantityOrdered',
        'concreteConfirmedOn',
        'isConcreteConfirmed',
        'pumpLocation',
        'pumpOrderedDate',
        'pumpWorkStart',
        'pumpWorkEnd',
        'pumpConfirmedOn',
        'isPumpConfirmed',
        'isPumpRequired',
        'cubicYardsTotal',
        'hoursToCompletePlacement',
        'minutesToCompletePlacement',
        'status',
        'ProjectId',
        'ConcreteRequestId',
        'requestType',
        'primerForPump',
        'concretePlacementStart',
        'concretePlacementEnd',
        'createdBy',
        'isCreatedByGuestUser',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
    });
    return newConcreteRequest;
  };

  ConcreteRequest.upcomingConcreteRequestForMobile = async (condition, ProjectId) => {
    const commonSearch = {
      isDeleted: false,
      ProjectId,
      concretePlacementStart: { [Op.gt]: new Date() },
      requestType: 'concreteRequest',
      ...condition,
    };
    const CraneRequestData = await ConcreteRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'UserId'],
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              where: { isDeleted: false },
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'locationDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteLocation', attributes: ['location', 'id'] }],
        },
        {
          association: 'mixDesignDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteMixDesign', attributes: ['mixDesign', 'id'] }],
        },
        {
          association: 'pumpSizeDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcretePumpSize', attributes: ['pumpSize', 'id'] }],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'concreteSupplierDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              attributes: ['companyName', 'id'],
            },
          ],
        },
        {
          association: 'location',
          required: false,
          attributes: ['id', 'locationPath'],
        },
        {
          association: 'locationDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteLocation', attributes: ['location', 'id'] }],
        },
      ],
      where: commonSearch,
      limit: 2,
      attributes: [
        'id',
        'description',
        'concreteOrderNumber',
        'truckSpacingHours',
        'notes',
        'slump',
        'concreteQuantityOrdered',
        'concreteConfirmedOn',
        'isConcreteConfirmed',
        'pumpLocation',
        'pumpOrderedDate',
        'pumpWorkStart',
        'pumpWorkEnd',
        'pumpConfirmedOn',
        'isPumpConfirmed',
        'isPumpRequired',
        'cubicYardsTotal',
        'hoursToCompletePlacement',
        'minutesToCompletePlacement',
        'status',
        'ProjectId',
        'ConcreteRequestId',
        'requestType',
        'primerForPump',
        'concretePlacementStart',
        'concretePlacementEnd',
        'createdBy',
        'isCreatedByGuestUser',
      ],
      order: [['concretePlacementStart', 'ASC']],
    });
    return CraneRequestData;
  };
  ConcreteRequest.guestSingleConcreteRequestData = async (attr) => {
    const newConcreteRequest = await ConcreteRequest.findOne({
      subQuery: false,
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              where: { isDeleted: false },
              attributes: ['id'],
              include: [
                {
                  association: 'User',
                  where: { isDeleted: false },
                  attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              where: { isDeleted: false },
              attributes: ['email', 'id', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'locationDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteLocation', attributes: ['location', 'id'] }],
        },
        {
          association: 'mixDesignDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcreteMixDesign', attributes: ['mixDesign', 'id'] }],
        },
        {
          association: 'pumpSizeDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'ConcretePumpSize', attributes: ['pumpSize', 'id'] }],
        },
        {
          association: 'concreteSupplierDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              attributes: ['companyName', 'id'],
            },
          ],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
            'chosenDateOfMonthValue',
          ],
        },
        {
          association: 'location',
          required: false,
          attributes: ['id', 'locationPath'],
        },
        {
          association: 'comments',
          required: false,
          include: [
            {
              association: 'Member',
              attributes: ['id'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'firstName', 'lastName', 'profilePic'],
                },
              ],
            },
          ],
          order: [['id', 'DESC']],
        },
        {
          association: 'attachments',
          required: false,
          where: { isDeleted: false },
        },
        {
          association: 'history',
          required: false,
          include: [
            {
              association: 'Member',
              include: [
                { association: 'User', attributes: ['firstName', 'lastName', 'profilePic'] },
              ],
            },
          ],
          order: [['id', 'DESC']],
        },
      ],
      where: { ...attr, isDeleted: false },
      attributes: [
        'id',
        'description',
        'concreteOrderNumber',
        'truckSpacingHours',
        'notes',
        'slump',
        'concreteQuantityOrdered',
        'concreteConfirmedOn',
        'isConcreteConfirmed',
        'pumpLocation',
        'pumpOrderedDate',
        'pumpWorkStart',
        'pumpWorkEnd',
        'pumpConfirmedOn',
        'isPumpConfirmed',
        'isPumpRequired',
        'cubicYardsTotal',
        'hoursToCompletePlacement',
        'minutesToCompletePlacement',
        'status',
        'ProjectId',
        'ConcreteRequestId',
        'requestType',
        'primerForPump',
        'concretePlacementStart',
        'concretePlacementEnd',
        'createdBy',
        'isCreatedByGuestUser',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
    });
    return newConcreteRequest;
  };

  return ConcreteRequest;
};
