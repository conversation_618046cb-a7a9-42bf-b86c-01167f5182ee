const NodeCache = require('node-cache');

/**
 * Cache configuration constants
 */
const CACHE_TTL = {
  DEFAULT: 14400,        // 4 hours (default)
  SHORT: 1800,          // 30 minutes (for frequently changing data)
  MEDIUM: 3600,         // 1 hour (for moderately changing data)
  LONG: 7200,           // 2 hours (for stable data)
  EXTENDED: 14400,      // 4 hours (for very stable data)
  DAY: 86400,           // 24 hours (for static data)
};

/**
 * NodeCache configuration and setup
 */
class CacheConfig {
  constructor() {
    this.config = {
      stdTTL: CACHE_TTL.DEFAULT, // Default TTL: 2 hours
      checkperiod: 600, // Check for expired keys every 10 minutes
      useClones: false, // Don't clone objects for better performance
      deleteOnExpire: true, // Automatically delete expired keys
      maxKeys: -1, // No limit on number of keys
    };
  }

  /**
   * Create a new NodeCache instance
   * @returns {NodeCache} NodeCache instance
   */
  createCache() {
    const cache = new NodeCache(this.config);

    cache.on('expired', (key, value) => {
      console.log(`Cache key expired: ${key}`);
    });

    cache.on('flush', () => {
      console.log('Cache flushed');
    });

    cache.on('del', (key, value) => {
      console.log(`Cache key deleted: ${key}`);
    });

    console.log('NodeCache created successfully');
    return cache;
  }

  /**
   * Test cache functionality
   * @returns {Promise<boolean>} Test status
   */
  async testCache() {
    const cache = this.createCache();
    try {
      // Test basic operations
      cache.set('test_key', 'test_value', 10);
      const value = cache.get('test_key');

      if (value === 'test_value') {
        console.log('NodeCache functionality test successful');
        cache.flushAll();
        return true;
      } else {
        console.error('NodeCache functionality test failed: value mismatch');
        return false;
      }
    } catch (error) {
      console.error('NodeCache functionality test failed:', error);
      return false;
    }
  }

  /**
   * Get cache statistics
   * @param {NodeCache} cache - Cache instance
   * @returns {Object} Cache statistics
   */
  getStats(cache) {
    try {
      return cache.getStats();
    } catch (error) {
      console.error('Failed to get cache stats:', error);
      return {};
    }
  }

  /**
   * Get cache configuration
   * @returns {Object} Cache configuration
   */
  getConfig() {
    return this.config;
  }

  /**
   * Get TTL constants
   * @returns {Object} TTL constants
   */
  getTTL() {
    return CACHE_TTL;
  }
}

module.exports = new CacheConfig();
module.exports.CACHE_TTL = CACHE_TTL;
