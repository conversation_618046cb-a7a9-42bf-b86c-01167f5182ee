const { Sequelize } = require('sequelize');

const { Op } = Sequelize;

module.exports = (sequelize, DataTypes) => {
  const Project = sequelize.define(
    'Project',
    {
      projectName: DataTypes.STRING,
      projectLocation: DataTypes.STRING,
      isDeleted: DataTypes.BOOLEAN,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      createdBy: {
        type: DataTypes.INTEGER,
        references: {
          model: 'Users', // name of Target model
          key: 'id', // key in Target model that we're referencing
        },
      },
      status: {
        type: DataTypes.STRING,
      },
      startDate: {
        type: DataTypes.DATE,
      },
      endDate: {
        type: DataTypes.DATE,
      },
      PlanId: {
        type: DataTypes.INTEGER,
      },
      EnterpriseId: {
        type: DataTypes.INTEGER,
      },
      isAccount: {
        type: DataTypes.BOOLEAN,
      },
      ParentCompanyId: {
        type: DataTypes.INTEGER,
      },
      subscribedOn: {
        type: DataTypes.DATE,
      },
      mailSendOn: {
        type: DataTypes.DATE,
      },
      StripeSubscriptionId: {
        type: DataTypes.INTEGER,
      },
      isSuperAdminCreatedProject: {
        type: DataTypes.BOOLEAN,
      },
      projectLocationLatitude: {
        type: DataTypes.STRING,
      },
      projectLocationLongitude: {
        type: DataTypes.STRING,
      },
      TimeZoneId: {
        type: DataTypes.INTEGER,
      },
    },
    {},
  );
  Project.associate = (models) => {
    Project.belongsTo(models.StripePlan, {
      as: 'stripePlan',
      foreignKey: 'PlanId',
    });
    Project.belongsTo(models.StripeSubscription);
    Project.belongsTo(models.TimeZone);
    Project.hasMany(models.ProjectBillingHistories);
    Project.hasOne(models.ProjectSettings, {
      as: 'ProjectSettings',
      foreignKey: 'ProjectId',
    });
    Project.belongsTo(models.User, {
      as: 'userDetails',
      foreignKey: 'createdBy',
    });
    Project.hasMany(models.Member, {
      as: 'memberDetails',
      foreignKey: 'ProjectId',
    });
    Project.hasMany(models.Member, {
      as: 'allMemberData',
      foreignKey: 'ProjectId',
    });
    Project.hasMany(models.Company, {
      as: 'companyDetails',
      foreignKey: 'ProjectId',
    });
    Project.hasOne(models.Company, {
      as: 'Company',
      foreignKey: 'ProjectId',
    });
    Project.hasMany(models.DeliveryRequest, {
      as: 'deliveryDetails',
      foreignKey: 'ProjectId',
    });
    Project.hasMany(models.Member, {
      as: 'projectAdminDetails',
      foreignKey: 'ProjectId',
    });
    Project.belongsTo(models.ParentCompany);
    Project.belongsTo(models.Enterprise);
    Project.hasMany(models.CraneRequest, {
      as: 'craneRequestDetails',
      foreignKey: 'ProjectId',
    });
    return Project;
  };
  Project.createInstance = async (paramData) => {
    const newProject = await Project.create(paramData);
    return newProject;
  };
  Project.updateInstance = async (id, args) => {
    const project = await Project.update(args, { where: { id } });
    return project;
  };
  Project.getProject = async (attr) => {
    const project = await Project.findOne({
      include: [
        { association: 'userDetails', attributes: { exclude: ['password', 'resetPasswordToken'] } },
        { association: 'stripePlan', include: ['Plan'] },
        'StripeSubscription',
      ],
      where: { ...attr },
      order: [['id', 'DESC']],
    });
    return project;
  };
  Project.getProjectsProjectAdmin = async (
    inputData,
    condition,
    searchCondition,
    limit,
    offset,
  ) => {
    const project = await Project.findAndCountAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          association: 'memberDetails',
          attributes: ['id'],
          where: { UserId: inputData.id, isDeleted: false, isActive: true },
          include: [{ association: 'User', attributes: ['email', 'firstName', 'lastName'] }],
          required: true,
        },
        {
          association: 'projectAdminDetails',
          attributes: ['id'],
          where: { RoleId: 2 },
          separate: true,
          include: [{ association: 'User', attributes: ['email', 'id', 'firstName', 'lastName'] }],
        },
      ],
      where: { ...condition, ...searchCondition },
      limit,
      offset,
      order: [['id', 'DESC']],
    });
    return project;
  };
  Project.getUserProjects = async (inputData, attr, memberCondition) => {
    const project = await Project.findAll({
      include: [
        {
          association: 'memberDetails',
          where: {
            UserId: inputData.id,
            isDeleted: false,
            isActive: true,
            memberProjectStatus: 'active',
            ...memberCondition,
          },
          attributes: ['id', 'memberProjectStatus'],
          required: true,
        },
        {
          association: 'ParentCompany',
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              where: { isParent: true },
              attributes: ['id', 'companyName'],
              required: true,
            },
          ],
        },
        {
          association: 'StripeSubscription',
          required: false,
          attributes: ['id', 'UserId', 'subscriptionId', 'status'],
        },
      ],
      attributes: [
        'id',
        'projectName',
        'createdAt',
        'isAccount',
        'isSuperAdminCreatedProject',
        'projectLocationLatitude',
        'projectLocationLongitude',
        'StripeSubscriptionId',
        'status',
      ],
      where: { ...attr, isDeleted: false },
      order: [[sequelize.literal('LOWER("projectName")'), 'ASC']],
    });
    return project;
  };
  Project.getProjectCount = async (inputData, attr) => {
    const project = await Project.findAll({
      include: [
        {
          association: 'memberDetails',
          where: { UserId: inputData.id, isDeleted: false, isActive: true },
          attributes: [],
          required: true,
        },
      ],
      where: { ...attr },
    });
    return project;
  };
  Project.getPlansAndProjects = async (
    inputData,
    condition,
    limit,
    offset,
    sort,
    sortByField,
    search,
  ) => {
    let commonSearch = {
      isDeleted: false,
      ...condition,
    };

    if (search) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              { projectName: { [Sequelize.Op.iLike]: `%${search}%` } },
              sequelize.where(sequelize.cast(sequelize.col('Project.id'), 'varchar'), {
                [Op.iLike]: `%${search}%`,
              }),
            ],
          },
        ],
      };
    }
    const project = await Project.findAndCountAll({
      include: [
        {
          association: 'memberDetails',
          attributes: { exclude: ['password', 'resetPasswordToken'] },
          where: { UserId: inputData.id, isActive: true },
          include: [{ association: 'User', attributes: ['email', 'firstName'] }],
          required: true,
        },
        {
          association: 'stripePlan',
          include: ['Plan'],
          attributes: ['stripePlanName', 'stripeProductName'],
        },
        {
          association: 'StripeSubscription',
          attributes: ['id', 'UserId', 'subscriptionId', 'status'],
        },
      ],
      where: commonSearch,
      order: [[sortByField, sort]],
      limit,
      offset,
    });
    return project;
  };
  Project.getAllProjects = async (
    limit,
    offset,
    searchText,
    sortByField,
    sortByType,
    projectId,
    projectName,
    companyName,
  ) => {
    const sortByFieldName = sortByField || 'id';
    const sortByColumnType = sortByType || 'DESC';
    let commonSearch = {
      [Op.and]: {
        isDeleted: false,
      },
    };
    let companyWhereCondition = {
      isParent: true,
    };

    if (searchText) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              { projectName: { [Sequelize.Op.iLike]: `%${searchText}%` } },
              { '$stripePlan.Plan.planType$': { [Sequelize.Op.iLike]: `%${searchText}%` } },
            ],
          },
        ],
      };
      if (Number(searchText)) {
        commonSearch = {
          ...commonSearch,
          [Op.or]: [
            sequelize.where(sequelize.cast(sequelize.col('stripePlan.stripeAmount'), 'varchar'), {
              [Op.iLike]: `%${searchText * 100}%`,
            }),
          ],
        };
      }
      // companyWhereCondition = {
      //   ...companyWhereCondition,
      //   companyName: {
      //     [Sequelize.Op.iLike]: `%${searchText}%`,
      //   },
      // };
    }
    if (projectName) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ projectName: { [Sequelize.Op.iLike]: `%${projectName}%` } }],
          },
        ],
      };
    }
    if (projectId) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ id: { [Sequelize.Op.iLike]: `%${projectId}%` } }],
          },
        ],
      };
    }
    if (companyName) {
      companyWhereCondition = {
        ...companyWhereCondition,
        companyName: {
          [Sequelize.Op.iLike]: `%${companyName}%`,
        },
      };
    }

    let orderQuery;
    if (sortByFieldName === 'companyName') {
      orderQuery = [['ParentCompany', 'Company', 'companyName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'stripeAmount') {
      orderQuery = [['stripePlan', 'stripeAmount', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'planType') {
      orderQuery = [['stripePlan', 'Plan', 'planType', `${sortByColumnType}`]];
    }
    if (
      sortByFieldName === 'projectName' ||
      sortByFieldName === 'startDate' ||
      sortByFieldName === 'endDate'
    ) {
      orderQuery = [[`${sortByFieldName}`, `${sortByColumnType}`]];
    }
    const project = await Project.findAndCountAll({
      subQuery: false,
      where: commonSearch,
      attributes: [
        'id',
        'projectName',
        'startDate',
        'endDate',
        'createdAt',
        'isSuperAdminCreatedProject',
        'projectLocationLatitude',
        'projectLocationLongitude',
      ],
      offset,
      limit,
      include: [
        {
          required: false,
          association: 'stripePlan',
          attributes: ['id', 'stripePlanName', 'stripeCurrency', 'stripeAmount'],
          include: [
            {
              required: false,
              association: 'Plan',
              attributes: ['id', 'planType'],
            },
          ],
        },
        {
          required: false,
          association: 'ParentCompany',
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              where: companyWhereCondition,
              attributes: ['id', 'companyName'],
              required: true,
            },
          ],
        },
      ],
      order: orderQuery,
    });
    return project;
  };
  Project.getMemberProject = async (attr) => {
    const project = await Project.findOne({
      where: { ...attr, isDeleted: false },
      include: [
        {
          association: 'Company',
          attributes: ['id', 'companyName'],
        },
        {
          association: 'userDetails',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phoneCode', 'phoneNumber'],
        },
        {
          association: 'stripePlan',
          attributes: ['id', 'stripePlanName', 'stripeAmount', 'stripeCurrency'],
          include: [
            {
              association: 'Plan',
              attributes: ['id', 'planType'],
            },
          ],
        },
      ],
      attributes: [
        'id',
        'projectName',
        'startDate',
        'endDate',
        'createdAt',
        'projectLocation',
        'projectLocationLatitude',
        'projectLocationLongitude',
        'isSuperAdminCreatedProject',
      ],
    });
    return project;
  };
  Project.getAllNonEnterprise = async (
    attr,
    limit,
    offset,
    searchText,
    sortByField,
    sortByType,
    projectName,
    companyName,
  ) => {
    const sortByFieldName = sortByField || 'id';
    const sortByColumnType = sortByType || 'DESC';
    let commonSearch = attr;
    let companyNameFilter;
    if (searchText) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              { projectName: { [Sequelize.Op.iLike]: `%${searchText}%` } },
              { '$stripePlan.Plan.planType$': { [Sequelize.Op.iLike]: `%${searchText}%` } },
            ],
          },
        ],
      };
    }
    if (projectName) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ projectName: { [Sequelize.Op.iLike]: `%${projectName}%` } }],
          },
        ],
      };
    }
    if (companyName) {
      companyNameFilter = { companyName };
    }
    let orderQuery;
    if (sortByFieldName === 'companyName') {
      orderQuery = [['companyDetails', 'companyName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'planType') {
      orderQuery = [['stripePlan', 'Plan', 'planType', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'firstName') {
      orderQuery = [['projectAdminDetails', 'firstName', `${sortByColumnType}`]];
    }
    if (
      sortByFieldName === 'projectName' ||
      sortByFieldName === 'startDate' ||
      sortByFieldName === 'endDate'
    ) {
      orderQuery = [[`${sortByFieldName}`, `${sortByColumnType}`]];
    }
    const project = await Project.findAndCountAll({
      where: commonSearch,
      attributes: [
        'id',
        'projectName',
        'startDate',
        'endDate',
        'createdAt',
        'isSuperAdminCreatedProject',
        'projectLocationLatitude',
        'projectLocationLongitude',
      ],
      offset,
      limit,
      include: [
        {
          where: companyNameFilter,
          association: 'companyDetails',
          attributes: ['id', 'companyName'],
        },
        {
          association: 'projectAdminDetails',
          attributes: ['id', 'firstName'],
        },
        {
          association: 'stripePlan',
          attributes: ['id', 'stripePlanName', 'stripeCurrency', 'stripeAmount'],
          include: [
            {
              association: 'Plan',
              attributes: ['id', 'planType'],
            },
          ],
        },
      ],
      order: orderQuery,
    });
    return project;
  };
  Project.getAllProjectsForBilling = async (
    limit,
    offset,
    sortByField,
    sortByType,
    userName,
    projectName,
    planType,
    searchText,
  ) => {
    const sortByFieldName = sortByField || 'id';
    const sortByColumnType = sortByType || 'DESC';
    let commonSearch = {
      isDeleted: false,
    };
    if (searchText) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              { projectName: { [Sequelize.Op.iLike]: `%${searchText}%` } },
              { '$userDetails.firstName$': { [Sequelize.Op.iLike]: `%${searchText}%` } },
              { '$userDetails.lastName$': { [Sequelize.Op.iLike]: `%${searchText}%` } },
              { '$stripePlan.Plan.planType$': { [Sequelize.Op.iLike]: `%${searchText}%` } },
            ],
          },
        ],
      };
    }
    if (projectName) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ projectName: { [Sequelize.Op.iLike]: `%${projectName}%` } }],
          },
        ],
      };
    }
    if (planType) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ '$stripePlan.Plan.planType$': { [Sequelize.Op.iLike]: `%${planType}%` } }],
          },
        ],
      };
    }
    if (userName) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              { '$userDetails.firstName$': { [Sequelize.Op.iLike]: `%${userName}%` } },
              { '$userDetails.lastName$': { [Sequelize.Op.iLike]: `%${userName}%` } },
            ],
          },
        ],
      };
    }
    const project = await Project.findAndCountAll({
      where: commonSearch,
      attributes: [
        'id',
        'createdBy',
        'projectName',
        'startDate',
        'endDate',
        'createdAt',
        'isSuperAdminCreatedProject',
      ],
      offset,
      limit,
      include: [
        {
          required: true,
          duplicating: false,
          association: 'userDetails',
          attributes: ['id', 'firstName', 'lastName'],
        },
        {
          required: false,
          duplicating: false,
          association: 'ProjectBillingHistories',
          attributes: [
            'ProjectId',
            'paidUserId',
            'projectCreatedUserId',
            'paidDate',
            'receiptUrl',
            'invoiceUrl',
            'paymentMethod',
            'amountPaid',
            'status',
          ],
        },
        {
          required: true,
          duplicating: false,
          association: 'stripePlan',
          attributes: ['id', 'stripePlanName', 'stripeCurrency', 'stripeAmount'],
          include: [
            {
              association: 'Plan',
              attributes: ['id', 'planType'],
            },
          ],
        },
      ],
      order: [[`${sortByFieldName}`, `${sortByColumnType}`]],
    });
    return project;
  };

  Project.getProjectAndSettings = async (condition) => {
    const project = await Project.findOne({
      where: condition,
      attributes: ['id', 'projectName'],
      include: [
        {
          required: true,
          duplicating: false,
          association: 'ProjectSettings',
          attributes: [
            'id',
            'deliveryWindowTime',
            'deliveryWindowTimeUnit',
            'inspectionWindowTime',
            'inspectionWindowTimeUnit',
            'isAutoApprovalEnabled',
            'deliveryAllowOverlappingBooking',
            'deliveryAllowOverlappingCalenderEvents',
            'craneAllowOverlappingBooking',
            'craneAllowOverlappingCalenderEvents',
            'concreteAllowOverlappingBooking',
            'concreteAllowOverlappingCalenderEvents',
            'inspectionAllowOverlappingBooking',
            'inspectionAllowOverlappingCalenderEvents',
          ],
        },
      ],
    });
    return project;
  };

  Project.retoolParentCompanyWithProjects = async () => {
    const project = await Project.findAll({
      include: [
        {
          association: 'ParentCompany',
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              where: { isParent: true },
              attributes: ['id', 'companyName'],
              required: true,
            },
          ],
        },
      ],
      attributes: [
        'id',
        'projectName',
        'createdAt',
        'isAccount',
        'isSuperAdminCreatedProject',
        'projectLocationLatitude',
        'projectLocationLongitude',
        'StripeSubscriptionId',
        'status',
      ],
      where: {
        isDeleted: false,
      },
      order: [[sequelize.literal('LOWER("projectName")'), 'ASC']],
    });
    return project;
  };

  return Project;
};
