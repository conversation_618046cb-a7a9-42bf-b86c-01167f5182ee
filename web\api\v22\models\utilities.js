module.exports = (sequelize, DataTypes) => {
    const Utilities = sequelize.define(
        'Utilities',
        {
            logMonth: DataTypes.DATE,
            utilityType: DataTypes.STRING,
            bill_name: DataTypes.STRING,
            details: DataTypes.STRING,
            utilityUnits: DataTypes.STRING,
            bill_url: DataTypes.STRING,
            bill_amount: DataTypes.STRING,
            projectId: DataTypes.INTEGER,
            isDeleted: DataTypes.BOOLEAN,
            parentId: DataTypes.INTEGER,
            quantity: DataTypes.STRING,
            cost: DataTypes.STRING,
            co2_emissions: DataTypes.STRING
        },
        {},
    );

    Utilities.associate = function (models) {
        Utilities.hasMany(models.Utilities, { as: 'child', foreignKey: 'parentId' });
    }
    Utilities.createUtilities = async (equipmentData) => {
        const newData = await Utilities.create(equipmentData);
        return newData;
    };
    Utilities.getAll = async (attr, limit, offset, searchCondition, sort, sortColumn) => {
        let Utilitiess;
        const sortByFieldName = sortColumn || 'id';
        const sortByColumnType = sort || 'DESC';

        Utilitiess = await Utilities.findAndCountAll({
            where: { ...attr, ...searchCondition },
            include: [
                {
                    model: Utilities,
                    as: 'child',
                    required: false,
                    attributes: ['id', 'utilityType', 'quantity', 'cost', 'co2_emissions'],
                },
            ],
            limit,
            offset,
            order: [[`${sortByFieldName}`, `${sortByColumnType}`]]
        });

        return Utilitiess;
    };
    return Utilities;
};