const status = require('http-status');
const {
  calendarService,
  deliveryService,
  craneRequestService,
  calendarSettingsService,
  inspectionService,
} = require('../services');
const { ProjectSettings } = require('../models');

const CalendarController = {
  async getEventNDR(req, res, next) {
    try {
      await calendarService.getEventNDR(req, async (response, error) => {
        if (error) {
          next(error);
        } else {
          req.query.start = req.body.start;
          req.query.end = req.body.end;
          req.query.ProjectId = req.params.ProjectId;
          req.query.ParentCompanyId = req.body.ParentCompanyId;
          req.query.search = req.body.search;
          req.body.isApplicableToDelivery = true;
          req.body.isApplicableToInspection = false;
          req.body.isApplicableToCrane = false;
          req.body.isApplicableToConcrete = false;
          let response1 = [];
          let response2;
          let response3;
          if (req.body.filterCount === 0) {
            response1 = await calendarSettingsService.getAll(req, next);
          }
          if (req.params.ProjectId !== '') {
            response2 = await ProjectSettings.getCalendarStatusColor(req.params.ProjectId);
            response3 = await ProjectSettings.getCalendarCard(req.params.ProjectId);
          }

          deliveryService.lastDelivery(req, (lastDetail, error1) => {
            if (!error1) {
              const requestData = [];
              if (response?.rows?.length > 0) {
                requestData.push(...response.rows);
              }
              if (response1?.length > 0) {
                requestData.push(...response1);
              }
              res.status(status.OK).json({
                message: 'Delivery Booking listed Successfully.',
                data: requestData,
                statusData: response2,
                cardData: response3,
                lastId: lastDetail,
              });
            } else {
              next(error1);
            }
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },

  async event_NDRcalender(req, res, next) {
    try {
      await calendarService.getEventNDR(req, async (response, error) => {
        if (error) {
          next(error);
        } else {
          req.query.start = req.body.start;
          req.query.end = req.body.end;
          req.query.ProjectId = req.params.ProjectId;
          req.query.ParentCompanyId = req.body.ParentCompanyId;
          req.query.search = req.body.search;
          req.body.isApplicableToDelivery = true;
          let response1 = [];
          if (req.body.filterCount === 0) {
            response1 = await calendarSettingsService.getAll(req, next);
          }
          deliveryService.lastDelivery(req, (lastDetail, error1) => {
            if (!error1) {
              const requestData = [];
              if (response?.rows?.length > 0) {
                requestData.push(...response.rows);
              }
              if (response1?.length > 0) {
                requestData.push(...response1);
              }
              res.status(status.OK).json({
                message: 'Delivery Booking listed Successfully.',
                data: requestData,
                lastId: lastDetail,
              });
            } else {
              next(error1);
            }
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getDeliveryRequestWithCrane(req, res, next) {
    try {
      await calendarService.getDeliveryRequestWithCrane(req, async (response, error) => {
        if (error) {
          next(error);
        } else {
          req.query.start = req.body.start;
          req.query.end = req.body.end;
          req.query.ProjectId = req.params.ProjectId;
          req.query.ParentCompanyId = req.body.ParentCompanyId;
          req.query.search = req.body.search;
          req.body.isApplicableToCrane = true;
          req.body.isApplicableToInspection = false;
          req.body.isApplicableToDelivery = false;
          req.body.isApplicableToConcrete = false;
          let response1 = [];
          let response2 = '';
          let response3;
          if (req.body.filterCount === 0) {
            response1 = await calendarSettingsService.getAll(req, next);
          }
          if (req.params.ProjectId !== '') {
            response2 = await ProjectSettings.getCalendarStatusColor(req.params.ProjectId);
            response3 = await ProjectSettings.getCalendarCard(req.params.ProjectId);
          }
          craneRequestService.lastCraneRequest(req, (lastDetail, error1) => {
            if (!error1) {
              const requestData = [];
              if (response?.length > 0) {
                requestData.push(...response);
              }
              if (response1?.length > 0) {
                requestData.push(...response1);
              }
              res.status(status.OK).json({
                message:
                  'Delivery Booking Associated With Crane Equipment Type listed Successfully.',
                data: requestData,
                statusData: response2,
                cardData: response3,
                lastId: lastDetail,
              });
            } else {
              next(error1);
            }
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getConcreteRequest(req, res, next) {
    try {
      await calendarService.getConcreteRequest(req, async (response, error) => {
        if (error) {
          next(error);
        } else {
          req.query.start = req.body.start;
          req.query.end = req.body.end;
          req.query.ProjectId = req.params.ProjectId;
          req.query.ParentCompanyId = req.body.ParentCompanyId;
          req.query.search = req.body.search;
          req.body.isApplicableToConcrete = true;
          req.body.isApplicableToDelivery = false;
          req.body.isApplicableToInspection = false;
          req.body.isApplicableToCrane = false;
          let response1 = [];
          let response2 = '';
          let response3;
          if (req.body.filterCount === 0) {
            response1 = await calendarSettingsService.getAll(req, next);
          }
          if (req.params.ProjectId !== '') {
            response2 = await ProjectSettings.getCalendarStatusColor(req.params.ProjectId);
            response3 = await ProjectSettings.getCalendarCard(req.params.ProjectId);
          }
          const requestData = [];
          if (response?.length > 0) {
            requestData.push(...response);
          }
          if (response1?.length > 0) {
            requestData.push(...response1);
          }
          res.status(status.OK).json({
            message: 'Concrete booking listed successfully',
            data: requestData,
            statusData: response2,
            cardData: response3,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },

  async getInspectionEventNDR(req, res, next) {
    try {
      await calendarService.getInspectionEventNDR(req, async (response, error) => {
        if (error) {
          next(error);
        } else {
          req.query.start = req.body.start;
          req.query.end = req.body.end;
          req.query.ProjectId = req.params.ProjectId;
          req.query.ParentCompanyId = req.body.ParentCompanyId;
          req.query.search = req.body.search;
          req.body.isApplicableToInspection = true;
          req.body.isApplicableToDelivery = false;
          req.body.isApplicableToConcrete = false;
          req.body.isApplicableToCrane = false;
          let response1 = [];
          let response2;
          let response3;
          if (req.body.filterCount === 0) {
            response1 = await calendarSettingsService.getAll(req, next);
          }
          if (req.params.ProjectId !== '') {
            response2 = await ProjectSettings.getCalendarStatusColor(req.params.ProjectId);
            response3 = await ProjectSettings.getCalendarCard(req.params.ProjectId);
          }

          inspectionService.lastinspection(req, (lastDetail, error1) => {
            if (!error1) {
              const requestData = [];
              if (response?.rows?.length > 0) {
                requestData.push(...response.rows);
              }
              if (response1?.length > 0) {
                requestData.push(...response1);
              }
              res.status(status.OK).json({
                message: 'Inspection Booking listed Successfully.',
                data: requestData,
                statusData: response2,
                cardData: response3,
                lastId: lastDetail,
              });
            } else {
              next(error1);
            }
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },

  async captureInspectionEventNDR(req, res, next) {
    try {
      req.mockRes = req.mockRes || {};
      await new Promise((resolve, reject) => {
        CalendarController.getInspectionEventNDR(
          req,
          {
            status: (code) => ({
              json: (data) => {
                req.mockRes.inspectionData = data;
                resolve();
              },
            }),
            json: (data) => {
              req.mockRes.inspectionData = data;
              resolve();
            },
          },
          (err) => {
            if (err) reject(err instanceof Error ? err : new Error(err));
          },
        );
      });
      next();
    } catch (err) {
      next(err);
    }
  },

  async captureCraneEventNDR(req, res, next) {
    try {
      req.mockRes = req.mockRes || {};
      await new Promise((resolve, reject) => {
        CalendarController.getDeliveryRequestWithCrane(
          req,
          {
            status: (code) => ({
              json: (data) => {
                req.mockRes.craneData = data;
                resolve();
              },
            }),
            json: (data) => {
              req.mockRes.craneData = data;
              resolve();
            },
          },
          (err) => {
            if (err) reject(err instanceof Error ? err : new Error(err));
          },
        );
      });
      next();
    } catch (err) {
      next(err);
    }
  },

  async captureConcreteEventNDR(req, res, next) {
    try {
      req.mockRes = req.mockRes || {};
      await new Promise((resolve, reject) => {
        CalendarController.getConcreteRequest(
          req,
          {
            status: (code) => ({
              json: (data) => {
                req.mockRes.concreteData = data;
                resolve();
              },
            }),
            json: (data) => {
              req.mockRes.concreteData = data;
              resolve();
            },
          },
          (err) => {
            if (err) reject(err instanceof Error ? err : new Error(err));
          },
        );
      });
      next();
    } catch (err) {
      next(err);
    }
  },

  async captureEventNDR(req, res, next) {
    try {
      await new Promise((resolve, reject) => {
        CalendarController.getEventNDR(
          req,
          {
            status: (code) => ({
              json: (data) => {
                req.mockRes.deliveryData = data;
                resolve();
              },
            }),
            json: (data) => {
              req.mockRes.deliveryData = data;
              resolve();
            },
          },
          (err) => {
            if (err) reject(err instanceof Error ? err : new Error(err));
          },
        );
      });
      next();
    } catch (err) {
      next(err);
    }
  },

  async getAllCalendarData(req, res, next) {
    try {
      res.json({
        message: 'Success',
        datas: {
          inspectionData: req.mockRes.inspectionData,
          deliveryData: req.mockRes.deliveryData,
          craneData: req.mockRes.craneData,
          concreteData: req.mockRes.concreteData,
        },
      });
    } catch (err) {
      console.log('error in all calendar:', err);
      next(err);
    }
  },

  async checkOverlappingToRestore(req, res, next) {
    try {
      const checkOverlapping = await deliveryService.checkDeliveryConflictsWithAlreadyScheduled(
        req.body,
        '',
        req.body?.gateDetails?.[0]?.id
      );
      if (checkOverlapping) {
        res.json({
          message: 'Not Allowed',
          isAllowed: false
        });
      } else {
        res.json({
          message: 'Allowed',
          isAllowed: true
        });
      }
    } catch (err) {
      next(err);
    }
  },
};
module.exports = CalendarController;
