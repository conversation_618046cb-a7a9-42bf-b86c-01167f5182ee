module.exports = (sequelize, DataTypes) => {
  const Gates = sequelize.define(
    'Gates',
    {
      gateName: DataTypes.STRING,
      isActive: DataTypes.BOOLEAN,
      gateAutoId: DataTypes.INTEGER,
      createdBy: {
        type: DataTypes.INTEGER,
        references: {
          model: 'Users', // name of Target model
          key: 'id', // key in Target model that we're referencing
        },
      },
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ProjectId: {
        type: DataTypes.INTEGER,
      },
      isDeleted: DataTypes.BOOLEAN,
    },
    {},
  );
  Gates.associate = (models) => {
    Gates.belongsTo(models.User, {
      as: 'userDetails',
      foreignKey: 'createdBy',
    });

    Gates.belongsTo(models.Project);
    Gates.hasMany(models.Locations);
    return Gates;
  };
  Gates.getGates = async (attr) => {
    const gates = await Gates.findOne({
      include: [
        { association: 'userDetails', attributes: { exclude: ['password', 'resetPasswordToken'] } },
        'Project',
      ],
      where: { ...attr },
      order: [['id', 'DESC']],
    });
    return gates;
  };
  Gates.getAll = async (attr, limit, offset, searchCondition, sort, sortColumn) => {
    let gate;
    const sortByFieldName = sortColumn || 'id';
    const sortByColumnType = sort || 'DESC';
    if (limit === 0) {
      gate = await Gates.findAll({
        where: { ...attr },
        order: [[`${sortByFieldName}`, `${sortByColumnType}`]],
      });
    } else {
      gate = await Gates.findAndCountAll({
        where: { ...attr, ...searchCondition },
        attributes: ['gateName', 'id', 'gateAutoId', 'isActive'],
        limit,
        offset,
        order: [[`${sortByFieldName}`, `${sortByColumnType}`]],
      });
    }
    return gate;
  };
  Gates.updateInstance = async (id, args) => {
    const gate = await Gates.update(args, { where: { id } });
    return gate;
  };

  Gates.createGate = async (gateData) => {
    const newGateData = await Gates.create(gateData);
    return newGateData;
  };
  return Gates;
};
