module.exports = {
  dynamicModels: [
    'User',
    'DeliverAttachement',
    'DeliverComment',
    'DeliverCompanie',
    'DeliverDefineWork',
    'DeliverDefine',
    'DeliverEquipment',
    'DeliverGate',
    'DeliverHistory',
    'Delivery<PERSON>erson',
    'DeliverCompany',
    'DeliveryRequest',
    'Gates',
    'Equipments',
    'NotificationPreference',
    'Member',
    'Project',
    'Plan',
    'StripePlan',
    'StripeSubscription',
    'VoidList',
    'Notification',
    'DeliveryPersonNotification',
    'Company',
    'CompanyDefine',
    'DeviceToken',
    'ParentCompany',
    'Enterprise',
    'Role',
    'ProjectBillingHistories',
    'CraneRequest',
    'CraneRequestCompany',
    'CraneRequestDefinableFeatureOfWork',
    'CraneRequestEquipment',
    'CraneRequestResponsiblePerson',
    'PresetEquipmentType',
    'CraneRequestAttachment',
    'CraneRequestComment',
    'CraneRequestHistory',
    'CalendarSetting',
    'TimeZone',
    'NotificationPreferenceItem',
    'DigestNotification',
    'ConcreteRequestResponsiblePerson',
    'ConcreteRequestAttachment',
    'ConcreteRequestComment',
    'ConcreteRequestHistory',
    'ConcreteRequest',
    'ConcreteRequestCompany',
    'ConcreteLocation',
    'ConcreteMixDesign',
    'ConcretePumpSize',
    'ConcreteRequestLocation',
    'ConcreteRequestMixDesign',
    'ConcreteRequestPumpSize',
    'SchedulerReport',
    'ProjectSettings',
    'SchedulerDateRange',
    'RequestRecurrenceSeries',
    'Locations',
    'LocationNotificationPreferences',
    'BookingTemplates',
    'InspectionRequest',
    'InspectionCompany',
    'InspectionGate',
    'InspectionEquipment',
    'InspectionPerson',
    'InspectionHistory',
    'InspectionPersonNotification',
    'InspectionComment',
    'ConcreteEquipment',
    'ConcreteGate',
    'CraneGate'
  ],
};
