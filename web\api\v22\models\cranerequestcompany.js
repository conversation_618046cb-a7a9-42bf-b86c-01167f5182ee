module.exports = (sequelize, DataTypes) => {
  const CraneRequestCompany = sequelize.define(
    'CraneRequestCompany',
    {
      CraneRequestId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      CraneRequestCode: DataTypes.INTEGER,
      CompanyId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ProjectId: {
        type: DataTypes.INTEGER,
      },
    },
    {},
  );
  CraneRequestCompany.associate = (models) => {
    CraneRequestCompany.belongsTo(models.CraneRequest, {
      as: 'craneRequest',
      foreignKey: 'CraneRequestId',
    });
    CraneRequestCompany.belongsTo(models.Company);
  };
  CraneRequestCompany.getAll = async (attr) => {
    const getCraneCompanies = await CraneRequestCompany.findAll({
      where: { ...attr },
      order: [['id', 'DESC']],
    });
    return getCraneCompanies;
  };
  CraneRequestCompany.createInstance = async (paramData) => {
    const newCraneCompany = await CraneRequestCompany.create(paramData);
    return newCraneCompany;
  };
  return CraneRequestCompany;
};
