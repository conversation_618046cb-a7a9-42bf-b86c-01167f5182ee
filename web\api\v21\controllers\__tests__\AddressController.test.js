const status = require('http-status');
const AddressController = require('../AddressController');

// Mock the models
jest.mock('../../models', () => ({
  Country: {
    getAll: jest.fn(),
  },
  State: {
    getAll: jest.fn(),
  },
  City: {
    getAll: jest.fn(),
  },
}));

const { Country, State, City } = require('../../models');

describe('AddressController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();

    // Setup mock request, response, and next function
    mockReq = {
      params: {},
      query: {},
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    mockNext = jest.fn();
  });

  describe('getCountry', () => {
    it('should return country list successfully', async () => {
      // Arrange
      const mockCountryList = [
        { id: 1, name: 'United States', code: 'US' },
        { id: 2, name: 'Canada', code: 'CA' },
        { id: 3, name: 'India', code: 'IN' },
      ];

      Country.getAll.mockResolvedValue(mockCountryList);

      // Act
      await AddressController.getCountry(mockReq, mockRes, mockNext);

      // Assert
      expect(Country.getAll).toHaveBeenCalledWith({});
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ countryList: mockCountryList });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return empty country list when no countries exist', async () => {
      // Arrange
      Country.getAll.mockResolvedValue([]);

      // Act
      await AddressController.getCountry(mockReq, mockRes, mockNext);

      // Assert
      expect(Country.getAll).toHaveBeenCalledWith({});
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ countryList: [] });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle errors and call next with error', async () => {
      // Arrange
      const error = new Error('Database connection failed');
      Country.getAll.mockRejectedValue(error);

      // Act
      await AddressController.getCountry(mockReq, mockRes, mockNext);

      // Assert
      expect(Country.getAll).toHaveBeenCalledWith({});
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getState', () => {
    it('should return state list successfully for a given country', async () => {
      // Arrange
      const mockReqWithCountryId = {
        params: { CountryId: '1' },
        query: {},
      };

      const mockStateList = [
        { id: 1, name: 'California', CountryId: 1 },
        { id: 2, name: 'Texas', CountryId: 1 },
        { id: 3, name: 'New York', CountryId: 1 },
      ];

      State.getAll.mockResolvedValue(mockStateList);

      // Act
      await AddressController.getState(mockReqWithCountryId, mockRes, mockNext);

      // Assert
      expect(State.getAll).toHaveBeenCalledWith({ CountryId: '1' });
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ stateList: mockStateList });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return empty state list when no states exist for country', async () => {
      // Arrange
      const mockReqWithCountryId = {
        params: { CountryId: '999' },
        query: {},
      };

      State.getAll.mockResolvedValue([]);

      // Act
      await AddressController.getState(mockReqWithCountryId, mockRes, mockNext);

      // Assert
      expect(State.getAll).toHaveBeenCalledWith({ CountryId: '999' });
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ stateList: [] });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle errors and call next with error', async () => {
      // Arrange
      const mockReqWithCountryId = {
        params: { CountryId: '1' },
        query: {},
      };

      const error = new Error('Database error');
      State.getAll.mockRejectedValue(error);

      // Act
      await AddressController.getState(mockReqWithCountryId, mockRes, mockNext);

      // Assert
      expect(State.getAll).toHaveBeenCalledWith({ CountryId: '1' });
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle missing CountryId parameter', async () => {
      // Arrange
      const mockReqWithoutCountryId = {
        params: {},
        query: {},
      };

      State.getAll.mockResolvedValue([]);

      // Act
      await AddressController.getState(mockReqWithoutCountryId, mockRes, mockNext);

      // Assert
      expect(State.getAll).toHaveBeenCalledWith({ CountryId: undefined });
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ stateList: [] });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('getCity', () => {
    it('should return city list successfully for a given state', async () => {
      // Arrange
      const mockReqWithStateId = {
        params: { StateId: '1' },
        query: {},
      };

      const mockCityList = [
        { id: 1, name: 'Los Angeles', StateId: 1 },
        { id: 2, name: 'San Francisco', StateId: 1 },
        { id: 3, name: 'San Diego', StateId: 1 },
      ];

      City.getAll.mockResolvedValue(mockCityList);

      // Act
      await AddressController.getCity(mockReqWithStateId, mockRes, mockNext);

      // Assert
      expect(City.getAll).toHaveBeenCalledWith({ StateId: '1' });
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ cityList: mockCityList });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return empty city list when no cities exist for state', async () => {
      // Arrange
      const mockReqWithStateId = {
        params: { StateId: '999' },
        query: {},
      };

      City.getAll.mockResolvedValue([]);

      // Act
      await AddressController.getCity(mockReqWithStateId, mockRes, mockNext);

      // Assert
      expect(City.getAll).toHaveBeenCalledWith({ StateId: '999' });
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ cityList: [] });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle errors and call next with error', async () => {
      // Arrange
      const mockReqWithStateId = {
        params: { StateId: '1' },
        query: {},
      };

      const error = new Error('Database error');
      City.getAll.mockRejectedValue(error);

      // Act
      await AddressController.getCity(mockReqWithStateId, mockRes, mockNext);

      // Assert
      expect(City.getAll).toHaveBeenCalledWith({ StateId: '1' });
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle missing StateId parameter', async () => {
      // Arrange
      const mockReqWithoutStateId = {
        params: {},
        query: {},
      };

      City.getAll.mockResolvedValue([]);

      // Act
      await AddressController.getCity(mockReqWithoutStateId, mockRes, mockNext);

      // Assert
      expect(City.getAll).toHaveBeenCalledWith({ StateId: undefined });
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ cityList: [] });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    it('should handle null response from models', async () => {
      // Arrange
      Country.getAll.mockResolvedValue(null);

      // Act
      await AddressController.getCountry(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({ countryList: null });
    });

    it('should handle undefined response from models', async () => {
      // Arrange
      State.getAll.mockResolvedValue(undefined);

      const mockReqWithCountryId = {
        params: { CountryId: '1' },
        query: {},
      };

      // Act
      await AddressController.getState(mockReqWithCountryId, mockRes, mockNext);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({ stateList: undefined });
    });

    it('should handle string parameters correctly', async () => {
      // Arrange
      const mockReqWithStringParams = {
        params: { CountryId: '123', StateId: '456' },
        query: {},
      };

      const mockStateList = [{ id: 1, name: 'Test State' }];
      State.getAll.mockResolvedValue(mockStateList);

      // Act
      await AddressController.getState(mockReqWithStringParams, mockRes, mockNext);

      // Assert
      expect(State.getAll).toHaveBeenCalledWith({ CountryId: '123' });
      expect(mockRes.json).toHaveBeenCalledWith({ stateList: mockStateList });
    });
  });
});
