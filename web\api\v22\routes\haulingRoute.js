const { Router } = require('express');
const { validate } = require('express-validation');
const HaulingLogController = require('../controllers/haulingLogController');
const passportConfig = require('../config/passport');
const checkAdmin = require('../middlewares/checkAdmin');

const HaulingLogRoute = {
    get router() {
        const router = Router();
        router.post(
            '/add_haulinglog',
            // validate(equipmentValidation.addEquipment, { keyByField: true }, { abortEarly: false }),
            passportConfig.isAuthenticated,
            // checkAdmin.isProjectAdmin,
            HaulingLogController.addHaulingLog,
        );
        router.get(
            '/haulinglog_list/:pageSize/:pageNo',
            // validate(equipmentValidation.equipmentDetail, { keyByField: true }, { abortEarly: false }),
            passportConfig.isAuthenticated,
            HaulingLogController.listHaulingLog,
        );
        return router;
    },
};
module.exports = HaulingLogRoute;