const { Router } = require('express');
const { validate } = require('express-validation');
const { equipmentValidation } = require('../middlewares/validations');
const WasteLogController = require('../controllers/wasteLogController');
const passportConfig = require('../config/passport');
const checkAdmin = require('../middlewares/checkAdmin');

const wasteLogRoute = {
    get router() {
        const router = Router();
        router.post(
            '/add_wastelog',
            // validate(equipmentValidation.addEquipment, { keyByField: true }, { abortEarly: false }),
            passportConfig.isAuthenticated,
            // checkAdmin.isProjectAdmin,
            WasteLogController.addWasteLog,
        );
        router.get(
            '/wastelog_list/:pageSize/:pageNo',
            // validate(equipmentValidation.equipmentDetail, { keyByField: true }, { abortEarly: false }),
            passportConfig.isAuthenticated,
            WasteLogController.listWasteLog,
        );
        return router;
    },
};
module.exports = wasteLogRoute;