const moment = require('moment');
const _ = require('lodash');
const httpStatus = require('http-status');
const { Sequelize, sequelize, Enterprise } = require('../models');
const { Deliver<PERSON><PERSON><PERSON>, VoidList, SchedulerReport, CraneRequest, ConcreteRequest, InspectionRequest } = require('../models');
let { DeliveryRequest, Member, DeliverGate, DeliverCompany, User } = require('../models');
const exportService = require('./exportService');
const pdfDeliveryReportService = require('./pdfDeliveryReportService');
const csvDeliveryReportService = require('./csvDeliveryReportService');
const ApiError = require('../helpers/apiError');
const helper = require('../helpers/domainHelper');
const { Op } = Sequelize;

let publicUser;
let publicMember;

const deliveryReportService = {
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    const ParentCompanyId = inputData.body.ParentCompanyId || inputData.params.ParentCompanyId;
    domainName = await this.getDomainName(domainName, ParentCompanyId, inputData.user.email);
    const modelObj = await helper.getDynamicModel(domainName);
    DeliveryRequest = modelObj.DeliveryRequest;
    Member = modelObj.Member;
    DeliverGate = modelObj.DeliverGate;
    DeliverCompany = modelObj.DeliverCompany;
    User = modelObj.User;
    return null;
  },

  async getDomainName(domainName, ParentCompanyId, email) {
    if (domainName) {
      const domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) domainName = '';
    } else if (ParentCompanyId) {
      domainName = await this.findDomainNameByCompanyIdOrEmail(ParentCompanyId, email);
    }
    return domainName;
  },

  async findDomainNameByCompanyIdOrEmail(ParentCompanyId, email) {
    const userData = email ? await publicUser.findOne({ where: { email } }) : null;
    const memberData = userData ? await this.getPublicMemberData(userData.id) : null;
    return this.findDomainNameByMemberData(memberData, ParentCompanyId);
  },

  async getPublicMemberData(userId) {
    return publicMember.findOne({
      where: { UserId: userId, RoleId: { [Op.ne]: 4 }, isDeleted: false },
    });
  },

  async findDomainNameByMemberData(memberData, ParentCompanyId) {
    const enterpriseValue = memberData?.isAccount
      ? await this.getEnterpriseById(memberData.EnterpriseId)
      : await this.getEnterpriseDomainName(ParentCompanyId);
    return enterpriseValue ? enterpriseValue.name.toLowerCase() : '';
  },

  async getEnterpriseById(EnterpriseId) {
    return Enterprise.findOne({
      where: { id: EnterpriseId, status: 'completed' },
    });
  },

  async getEnterpriseDomainName(ParentCompanyId) {
    const enterpriseValue = await this.getEnterpriseByParentCompanyId(ParentCompanyId);
    return enterpriseValue ? enterpriseValue.name.toLowerCase() : '';
  },

  async getEnterpriseByParentCompanyId(ParentCompanyId) {
    return Enterprise.findOne({
      where: { ParentCompanyId, status: 'completed' },
    });
  },

  async listDeliveryRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      if (this.isInvalidVoidParam(inputData.params.void)) {
        return done({ message: 'Please enter void as 1 or 0' }, null);
      }

      const memberDetails = await this.getMemberDetails(inputData.user.id, inputData.params.ProjectId);
      if (!memberDetails) {
        return done({ message: 'Project Id/Member does not exist' }, null);
      }

      const searchCondition = this.buildSearchCondition(inputData.body);
      const deliveryList = await this.fetchDeliveryRequests(inputData, memberDetails, searchCondition);

      this.processDeliveryList(deliveryList, inputData, done);
    } catch (e) {
      done({ message: 'Something went wrong' }, e);
    }
  },

  isInvalidVoidParam(voidParam) {
    return voidParam !== '0' && voidParam !== '1';
  },

  async getMemberDetails(userId, ProjectId) {
    return Member.findOne({
      where: Sequelize.and({
        UserId: userId,
        ProjectId,
        isDeleted: false,
        isActive: true,
      }),
    });
  },

  buildSearchCondition(incomeData) {
    if (!incomeData.search) return {};
    const search = { [Op.iLike]: `%${incomeData.search}%` };
    return {
      [Op.or]: [
        { '$approverDetails.User.firstName$': search },
        { '$equipmentDetails.Equipment.equipmentName$': search },
        { description: search },
        { cranePickUpLocation: search },
        { craneDropOffLocation: search },
      ],
    };
  },

  async fetchDeliveryRequests(inputData, memberDetails, searchCondition) {
    const { pageNo, pageSize, void: voidParam, ProjectId } = inputData.params;
    const offset = this.calculateOffset(pageNo, pageSize);
    const condition = this.buildCondition(inputData.body, ProjectId, voidParam);
    return DeliveryRequest.getAll({
      attr: condition,
      roleId: memberDetails.RoleId,
      memberId: memberDetails.id,
      limit: +pageSize,
      offset,
      searchCondition,
    });
  },

  calculateOffset(pageNo, pageSize) {
    return (+pageNo - 1) * +pageSize;
  },

  processDeliveryList(deliveryList, inputData, done) {
    this.getLimitData(
      deliveryList, 0, +inputData.params.pageSize, [],
      inputData.body, inputData.headers.timezoneoffset,
      (response, err) => {
        if (err) return done(null, err);
        done({ count: deliveryList.length, rows: response }, null);
      }
    );
  },

  buildCondition(incomeData, ProjectId, voidParam) {
    const condition = { ProjectId: +ProjectId, isQueued: false };
    const voidDelivery = voidParam === '1' ? [] : undefined;

    const mapFilterConditions = (filters) => (
      Object.entries(filters).reduce((acc, [key, value]) => {
        if (value) acc[key] = { [Op.iLike]: `%${value}%` };
        return acc;
      }, {})
    );

    return { ...condition, ...mapFilterConditions(incomeData), ...voidDelivery };
  },

  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },

  async getLimitData(result, index, limit, finalResult, incomeData, timezoneoffset, done) {
    if (index < limit) {
      finalResult.push(result);
      return this.getLimitData(result, index + 1, limit, finalResult, incomeData, timezoneoffset, done);
    }
    done(finalResult, false);
  },
};

module.exports = deliveryReportService;