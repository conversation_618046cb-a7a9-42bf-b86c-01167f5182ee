/* eslint-disable no-restricted-syntax */
/* eslint-disable no-await-in-loop */
/* eslint-disable no-loop-func */
/* eslint-disable no-lone-blocks */
/* eslint-disable no-param-reassign */
/* eslint-disable no-nested-ternary */
/* eslint-disable array-callback-return */
const moment = require('moment');
const Moment = require('moment');
const Cryptr = require('cryptr');
const MomentRange = require('moment-range');
const httpStatus = require('http-status');
const ApiError = require('../helpers/apiError');

const momentRange = MomentRange.extendMoment(Moment);
const {
  Sequelize,
  Enterprise,
  NotificationPreference,
  DigestNotification,
  TimeZone,
  CalendarSetting,
  Locations,
  LocationNotificationPreferences,
} = require('../models');
let {
  DeliveryRequest,
  Member,
  DeliveryPerson,
  DeliverCompany,
  Role,
  DeliverDefineWork,
  Company,
  Project,
  VoidList,
  User,
  Notification,
  DeliveryPersonNotification,
  ConcreteRequestResponsiblePerson,
} = require('../models');
const {
  ConcreteRequest,
  ConcreteRequestHistory,
  ConcreteRequestCompany,
  ConcreteLocation,
  ConcreteMixDesign,
  ConcretePumpSize,
  ConcreteRequestLocation,
  ConcreteRequestMixDesign,
  ConcreteRequestPumpSize,
  ConcreteEquipment,
  ConcreteGate,
  CraneRequest,
  RequestRecurrenceSeries,
  InspectionRequest,
} = require('../models');
const MAILER = require('../mailer');
const helper = require('../helpers/domainHelper');
const notificationHelper = require('../helpers/notificationHelper');
const pushNotification = require('../config/fcm');
const calendarSettingsService = require('./calendarSettingsService');
const voidService = require('./voidService');

let publicUser;
let publicMember;
const { Op } = Sequelize;
const concreteRequestService = {
  // Validation helper functions
  validateTimeRange(startTime, endTime, startLabel, endLabel) {
    if (startTime === endTime) {
      return {
        isValid: false,
        message: `${startLabel} and ${endLabel} should not be the same`,
      };
    }
    if (startTime > endTime) {
      return {
        isValid: false,
        message: `Please enter ${startLabel} lesser than ${endLabel}`,
      };
    }
    return { isValid: true };
  },

  validateDeliveryWindow(memberDetails, startDate, endDate, projectDetails, recurrence) {
    if (+memberDetails.RoleId === 4 && (startDate || endDate)) {
      if (startDate || endDate) {
        if (projectDetails.ProjectSettings.deliveryWindowTime === 0) {
          if (recurrence === 'Does Not Repeat') {
            return {
              isValid: false,
              message: 'Please enter Future Concrete Placement Date/Time',
            };
          }
          return {
            isValid: false,
            message: 'Please enter Future Concrete Placement or Recurrence End Date/Time',
          };
        }
        return {
          isValid: false,
          message: `Bookings can not be submitted within ${projectDetails.ProjectSettings.deliveryWindowTime} ${projectDetails.ProjectSettings.deliveryWindowTimeUnit} prior to the event`,
        };
      }
    }
    return { isValid: true };
  },

  validatePumpDeliveryWindow(memberDetails, pumpStartDate, pumpEndDate) {
    if (+memberDetails.RoleId === 4 && (pumpStartDate || pumpEndDate)) {
      return {
        isValid: false,
        message: 'Please enter Future Pump Ordered Date/Time',
      };
    }
    return { isValid: true };
  },

  // Timezone and date helper functions
  createTimezoneDateTime(date, time, timezone) {
    return moment.tz(`${date} ${time}`, 'MM/DD/YYYY HH:mm', timezone);
  },

  convertToUTC(momentDateTime) {
    return momentDateTime.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
  },

  formatDateForTimezone(data) {
    const date = moment(data).format('MM/DD/YYYY');
    return date;
  },

  createConcreteRequestTimestamps(concreteRequestDetail, eventTimeZone, date) {
    const chosenTimezoneConcretePlacementStart = this.createTimezoneDateTime(
      date,
      concreteRequestDetail.startPicker,
      eventTimeZone.timezone,
    );
    const chosenTimezoneConcretePlacementEnd = this.createTimezoneDateTime(
      date,
      concreteRequestDetail.endPicker,
      eventTimeZone.timezone,
    );

    let pumpTimestamps = {};
    if (concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkStart) {
      const pumpDate = moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format('MM/DD/YYYY');
      pumpTimestamps = {
        pumpOrderedDate: this.convertToUTC(this.createTimezoneDateTime(pumpDate, concreteRequestDetail.pumpWorkStart, eventTimeZone.timezone)),
        pumpWorkStart: this.convertToUTC(this.createTimezoneDateTime(pumpDate, concreteRequestDetail.pumpWorkStart, eventTimeZone.timezone)),
        pumpWorkEnd: this.convertToUTC(this.createTimezoneDateTime(pumpDate, concreteRequestDetail.pumpWorkEnd, eventTimeZone.timezone)),
      };
    }

    return {
      concretePlacementStart: this.convertToUTC(chosenTimezoneConcretePlacementStart),
      concretePlacementEnd: this.convertToUTC(chosenTimezoneConcretePlacementEnd),
      ...pumpTimestamps,
    };
  },

  createBaseConcreteRequestParam(concreteRequestDetail, timestamps, id, memberDetails, recurrenceId) {
    return {
      description: concreteRequestDetail.description,
      ProjectId: concreteRequestDetail.ProjectId,
      notes: concreteRequestDetail.notes,
      concretePlacementStart: timestamps.concretePlacementStart,
      concretePlacementEnd: timestamps.concretePlacementEnd,
      isPumpConfirmed: concreteRequestDetail.isPumpConfirmed,
      isPumpRequired: concreteRequestDetail.isPumpRequired,
      isConcreteConfirmed: concreteRequestDetail.isConcreteConfirmed,
      ParentCompanyId: concreteRequestDetail.ParentCompanyId,
      concreteOrderNumber: concreteRequestDetail.concreteOrderNumber,
      truckSpacingHours: concreteRequestDetail.truckSpacingHours,
      slump: concreteRequestDetail.slump,
      concreteQuantityOrdered: concreteRequestDetail.concreteQuantityOrdered,
      concreteConfirmedOn: concreteRequestDetail.concreteConfirmedOn || null,
      pumpLocation: concreteRequestDetail.pumpLocation,
      pumpOrderedDate: timestamps.pumpOrderedDate || null,
      pumpWorkStart: timestamps.pumpWorkStart || null,
      pumpWorkEnd: timestamps.pumpWorkEnd || null,
      pumpConfirmedOn: concreteRequestDetail.pumpConfirmedOn || null,
      cubicYardsTotal: concreteRequestDetail.cubicYardsTotal,
      hoursToCompletePlacement: concreteRequestDetail.hoursToCompletePlacement,
      minutesToCompletePlacement: concreteRequestDetail.minutesToCompletePlacement,
      ConcreteRequestId: id,
      requestType: 'concreteRequest',
      status: 'Tentative',
      primerForPump: concreteRequestDetail.primerForPump,
      createdBy: memberDetails.id,
      recurrenceId,
      LocationId: concreteRequestDetail.LocationId,
      OriginationAddress: concreteRequestDetail.originationAddress,
      vehicleType: concreteRequestDetail.vehicleType,
      OriginationAddressPump: concreteRequestDetail.originationAddressPump,
      vehicleTypePump: concreteRequestDetail.vehicleTypePump,
    };
  },

  setApprovalStatus(concreteRequestParam, memberDetails, roleDetails, accountRoleDetails, projectDetails) {
    if (
      memberDetails.RoleId === roleDetails.id ||
      memberDetails.RoleId === accountRoleDetails.id ||
      memberDetails.isAutoApproveEnabled ||
      projectDetails.ProjectSettings.isAutoApprovalEnabled
    ) {
      concreteRequestParam.status = 'Approved';
      concreteRequestParam.approvedBy = memberDetails.id;
      concreteRequestParam.approved_at = new Date();
    }
    return concreteRequestParam;
  },

  // Recurrence handling functions
  async processDailyRecurrence(params) {
    let { concreteRequestDetail, eventTimeZone, totalDays, id, memberDetails, roleDetails, accountRoleDetails, projectDetails, inputUser } = params;
    const eventsArray = [];
    let dailyIndex = 0;
    const recurrenceId = await this.insertRecurrenceSeries(
      concreteRequestDetail,
      inputUser,
      'concreteRequest',
      eventTimeZone.timezone,
    );

    while (dailyIndex < totalDays.length) {
      const data = totalDays[dailyIndex];
      if (
        moment(data).isBetween(
          moment(concreteRequestDetail.concretePlacementStart),
          moment(concreteRequestDetail.concretePlacementEnd),
          null,
          '[]',
        ) ||
        moment(data).isSame(concreteRequestDetail.concretePlacementStart) ||
        moment(data).isSame(concreteRequestDetail.concretePlacementEnd)
      ) {
        id += 1;
        const date = moment(data).format('MM/DD/YYYY');
        const timestamps = this.createConcreteRequestTimestamps(concreteRequestDetail, eventTimeZone, date);

        let concreteRequestParam = this.createBaseConcreteRequestParam(
          concreteRequestDetail,
          timestamps,
          id,
          memberDetails,
          recurrenceId,
        );

        concreteRequestParam = this.setApprovalStatus(
          concreteRequestParam,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectDetails,
        );

        eventsArray.push(concreteRequestParam);
        dailyIndex += +concreteRequestDetail.repeatEveryCount;
      }
    }

    return { eventsArray, lastId: id };
  },

  async processWeeklyRecurrence(params) {
    let { concreteRequestDetail, eventTimeZone, id, memberDetails, roleDetails, accountRoleDetails, projectDetails, inputUser } = params;
    const eventsArray = [];
    const startDayWeek = moment(concreteRequestDetail.concretePlacementStart).startOf('week');
    const endDayWeek = moment(concreteRequestDetail.concretePlacementEnd).endOf('week');
    const range1 = momentRange.range(moment(startDayWeek), moment(endDayWeek));
    const totalDaysOfRecurrence = Array.from(range1.by('day'));
    const recurrenceId = await this.insertRecurrenceSeries(
      concreteRequestDetail,
      inputUser,
      'concreteRequest',
      eventTimeZone.timezone,
    );

    let count;
    let weekIncrement;
    if (+concreteRequestDetail.repeatEveryCount > 1) {
      count = +concreteRequestDetail.repeatEveryCount - 1;
      weekIncrement = 7;
    } else {
      count = 1;
      weekIncrement = 0;
    }

    for (
      let indexba = 0;
      indexba < totalDaysOfRecurrence.length;
      indexba += weekIncrement * count
    ) {
      const totalLength = indexba + 6;
      for (let indexb = indexba; indexb <= totalLength; indexb += 1) {
        const data = totalDaysOfRecurrence[indexb];
        if (
          data &&
          !moment(data).isBefore(concreteRequestDetail.concretePlacementStart) &&
          !moment(data).isAfter(concreteRequestDetail.concretePlacementEnd)
        ) {
          const day = moment(data).format('dddd');
          const indexVal = Array.isArray(concreteRequestDetail.days) && concreteRequestDetail.days.includes(day);
          if (indexVal) {
            id += 1;
            const date = moment(data).format('MM/DD/YYYY');
            const timestamps = this.createConcreteRequestTimestamps(concreteRequestDetail, eventTimeZone, date);

            let concreteRequestParam = this.createBaseConcreteRequestParam(
              concreteRequestDetail,
              timestamps,
              id,
              memberDetails,
              recurrenceId,
            );

            concreteRequestParam = this.setApprovalStatus(
              concreteRequestParam,
              memberDetails,
              roleDetails,
              accountRoleDetails,
              projectDetails,
            );

            eventsArray.push(concreteRequestParam);
          }
        }
      }
    }

    return { eventsArray, lastId: id };
  },

  async processNoRecurrence(params) {
    const { concreteRequestDetail, eventTimeZone, id, memberDetails, roleDetails, accountRoleDetails, projectDetails, inputUser } = params;
    const eventsArray = [];
    const recurrenceId = await this.insertRecurrenceSeries(
      concreteRequestDetail,
      inputUser,
      'concreteRequest',
      eventTimeZone.timezone,
    );

    const date = moment(concreteRequestDetail.concretePlacementStart).format('MM/DD/YYYY');
    const timestamps = this.createConcreteRequestTimestamps(concreteRequestDetail, eventTimeZone, date);

    let concreteRequestParam = this.createBaseConcreteRequestParam(
      concreteRequestDetail,
      timestamps,
      id + 1,
      memberDetails,
      recurrenceId,
    );

    concreteRequestParam = this.setApprovalStatus(
      concreteRequestParam,
      memberDetails,
      roleDetails,
      accountRoleDetails,
      projectDetails,
    );

    eventsArray.push(concreteRequestParam);
    return { eventsArray, lastId: id + 1 };
  },

  async getNextConcreteRequestId(projectId) {
    const lastData = await ConcreteRequest.findOne({
      where: { ProjectId: +projectId, isDeleted: false },
      order: [['ConcreteRequestId', 'DESC']],
    });

    if (lastData) {
      return lastData.ConcreteRequestId + 1;
    }
    return 1;
  },

  // Common helper functions for list operations
  validateVoidParameter(voidParam) {
    if (voidParam >= 1 && voidParam <= 0) {
      return {
        isValid: false,
        message: 'Please enter void as 1 or 0',
      };
    }
    return { isValid: true };
  },

  async getVoidConcreteRequestIds(projectId) {
    const voidConcreteRequestList = await VoidList.findAll({
      where: {
        ProjectId: projectId,
        ConcreteRequestId: { [Op.ne]: null },
      },
    });
    return voidConcreteRequestList.map(element => element.ConcreteRequestId);
  },

  async getVoidInspectionRequestIds(projectId) {
    const voidInspectionRequestList = await VoidList.findAll({
      where: {
        ProjectId: projectId,
        InspectionRequestId: { [Op.ne]: null },
      },
    });
    return voidInspectionRequestList.map(element => element.InspectionRequestId);
  },

  async getVoidDeliveryRequestIds(projectId) {
    const voidList = await VoidList.findAll({
      where: {
        ProjectId: projectId,
        isDeliveryRequest: true,
        DeliveryRequestId: { [Op.ne]: null },
      },
    });
    return voidList.map(element => element.DeliveryRequestId);
  },

  buildVoidCondition(voidParam, voidIds, entityType) {
    const condition = {};
    if (voidParam === '0' || voidParam === 0) {
      condition[`$${entityType}.id$`] = {
        [Op.and]: [{ [Op.notIn]: voidIds }],
      };
    } else {
      condition[`$${entityType}.id$`] = {
        [Op.and]: [{ [Op.in]: voidIds }],
      };
    }
    return condition;
  },

  // Filter building helpers
  buildDateFilter(dateFilter, timezoneOffset) {
    if (!dateFilter) return {};

    const startDateTime = moment(dateFilter, 'YYYY-MM-DD')
      .startOf('day')
      .utcOffset(Number(timezoneOffset), true);
    const endDateTime = moment(dateFilter, 'YYYY-MM-DD')
      .endOf('day')
      .utcOffset(Number(timezoneOffset), true);

    return {
      deliveryStart: {
        [Op.between]: [moment(startDateTime), moment(endDateTime)],
      },
    };
  },

  buildUpcomingFilter() {
    return {
      deliveryStart: {
        [Op.gt]: new Date(),
      },
    };
  },

  buildSearchCondition(searchTerm, projectId) {
    if (!searchTerm) return {};

    const searchDefault = [
      {
        '$approverDetails.User.firstName$': {
          [Sequelize.Op.iLike]: `%${searchTerm}%`,
        },
      },
      {
        '$equipmentDetails.Equipment.equipmentName$': {
          [Sequelize.Op.iLike]: `%${searchTerm}%`,
        },
      },
      {
        description: {
          [Sequelize.Op.iLike]: `%${searchTerm}%`,
        },
      },
      {
        cranePickUpLocation: {
          [Sequelize.Op.iLike]: `%${searchTerm}%`,
        },
      },
      {
        craneDropOffLocation: {
          [Sequelize.Op.iLike]: `%${searchTerm}%`,
        },
      },
    ];

    if (!Number.isNaN(+searchTerm)) {
      return {
        [Op.and]: [
          {
            [Op.or]: [
              searchDefault,
              {
                [Op.and]: [
                  {
                    DeliveryId: +searchTerm,
                    isDeleted: false,
                    ProjectId: +projectId,
                  },
                ],
              },
            ],
          },
        ],
      };
    }

    return {
      [Op.and]: [
        {
          [Op.or]: searchDefault,
        },
      ],
    };
  },

  buildNDRCondition(incomeData, projectId, voidDelivery, voidParam, offset) {
    const condition = {
      ProjectId: +projectId,
      isQueued: incomeData.queuedNdr,
    };

    // Apply void condition
    Object.assign(condition, this.buildVoidCondition(voidParam, voidDelivery, 'DeliveryRequest'));

    // Apply filters
    if (incomeData.descriptionFilter) {
      condition.description = {
        [Sequelize.Op.iLike]: `%${incomeData.descriptionFilter}%`,
      };
    }

    if (incomeData.pickFrom) {
      condition.cranePickUpLocation = {
        [Sequelize.Op.iLike]: `%${incomeData.pickFrom}%`,
      };
    }

    if (incomeData.pickTo) {
      condition.craneDropOffLocation = {
        [Sequelize.Op.iLike]: `%${incomeData.pickTo}%`,
      };
    }

    if (incomeData.equipmentFilter) {
      condition['$equipmentDetails.Equipment.id$'] = incomeData.equipmentFilter;
    }

    if (incomeData.statusFilter) {
      condition.status = incomeData.statusFilter;
    }

    return condition;
  },

  // Sorting and data processing helpers
  sortResponseData(data, sort, sortByField) {
    if (sort === 'ASC') {
      return data.sort((a, b) => {
        let result;
        if (a[sortByField] > b[sortByField]) {
          result = 1;
        } else if (b[sortByField] > a[sortByField]) {
          result = -1;
        } else {
          result = 0;
        }
        return result;
      });
    } else {
      return data.sort((a, b) => {
        let result;
        if (b[sortByField] > a[sortByField]) {
          result = 1;
        } else if (a[sortByField] > b[sortByField]) {
          result = -1;
        } else {
          result = 0;
        }
        return result;
      });
    }
  },

  shouldUseCalendarData(incomeData, memberDetails, params) {
    return (
      incomeData.companyFilter ||
      incomeData.gateFilter ||
      incomeData.memberFilter ||
      incomeData.assignedFilter ||
      (memberDetails.RoleId === 4 &&
        (params.void === '0' || params.void === 0) &&
        !incomeData.upcoming) ||
      (memberDetails.RoleId === 3 &&
        (params.void === '0' || params.void === 0) &&
        !incomeData.upcoming)
    );
  },

  async processCalendarData(paramsData, done) {
    const { condition, roleId, memberId, searchCondition, order, sort, sortByField, incomeData, params, inputData } = paramsData;
    const result = { count: 0, rows: [] };
    const deliveryList = await DeliveryRequest.getCalendarData(
      condition,
      roleId,
      memberId,
      searchCondition,
      order,
      sort,
      sortByField,
    );

    const offset = (+params.pageNo - 1) * +params.pageSize;

    this.getSearchData(
      incomeData,
      deliveryList.rows,
      [],
      +params.pageSize,
      0,
      0,
      { RoleId: roleId, id: memberId },
      async (checkResponse, checkError) => {
        if (!checkError) {
          this.getLimitData(
            checkResponse,
            0,
            +params.pageSize,
            [],
            incomeData,
            inputData.headers.timezoneoffset,
            async (newResponse, newError) => {
              if (!newError) {
                const sortedResponse = this.sortResponseData(newResponse, sort, sortByField);
                result.rows = sortedResponse.slice(offset, offset + +params.pageSize);
                result.count = checkResponse.length;
                done(result, false);
              } else {
                done(null, { message: 'Something went wrong' });
              }
            },
          );
        } else {
          done(null, { message: 'Something went wrong' });
        }
      },
    );
  },

  async processStandardData(payload, done) {
    const { condition, roleId, memberId, params, searchCondition, order, sort, sortByField, incomeData, inputData } = payload;
    const newResult = { count: 0, rows: [] };
    const offset = (+params.pageNo - 1) * +params.pageSize;

    const deliveryList = await DeliveryRequest.getAll({
      attr: condition,
      roleId,
      memberId,
      limit: +params.pageSize,
      offset,
      searchCondition,
      order,
      sort,
      sortColumn: sortByField,
    });

    this.getLimitData(
      deliveryList,
      0,
      +params.pageSize,
      [],
      incomeData,
      inputData.headers.timezoneoffset,
      async (newResponse, newError) => {
        if (!newError) {
          const sortedResponse = this.sortResponseData(newResponse, sort, sortByField);
          newResult.rows = sortedResponse.slice(offset, offset + +params.pageSize);
          newResult.count = deliveryList.length;
          done(newResult, false);
        } else {
          done(null, { message: 'Something went wrong' });
        }
      },
    );
  },

  // Edit operation helpers
  async getEditConcreteRequestData(concreteRequestData) {
    const projectSettingDetails = await Project.getProjectAndSettings({
      isDeleted: false,
      id: +concreteRequestData.ProjectId,
    });

    const memberDetails = await Member.getBy({
      UserId: concreteRequestData.loginUser.id,
      ProjectId: concreteRequestData.ProjectId,
      isActive: true,
      isDeleted: false,
    });

    const roleDetails = await Role.getBy('Project Admin');
    const accountRoleDetails = await Role.getBy('Account Admin');

    return {
      projectSettingDetails,
      memberDetails,
      roleDetails,
      accountRoleDetails,
    };
  },

  createEditTimestamps(concreteRequestData) {
    const startTime = concreteRequestData.deliveryStartTime;
    const endTime = concreteRequestData.deliveryEndTime;
    const date1 = moment(concreteRequestData.concretePlacementStart).format('MM/DD/YYYY');
    const date2 = moment(concreteRequestData.recurrenceEndDate).format('MM/DD/YYYY');

    const chosenTimezoneDeliveryStart = moment.tz(
      `${date1} ${startTime}`,
      'MM/DD/YYYY HH:mm',
      concreteRequestData.timezone,
    );

    const chosenTimezoneDeliveryEnd = moment.tz(
      `${date2} ${endTime}`,
      'MM/DD/YYYY HH:mm',
      concreteRequestData.timezone,
    );

    const deliveryStartUTC = chosenTimezoneDeliveryStart
      .clone()
      .tz('UTC')
      .format('YYYY-MM-DD HH:mm:ssZ');

    const recurrenceEndUTC = chosenTimezoneDeliveryEnd
      .clone()
      .tz('UTC')
      .format('YYYY-MM-DD HH:mm:ssZ');

    return {
      deliveryStartUTC,
      recurrenceEndUTC,
    };
  },

  async validateSingleRequestEdit(concreteRequestData, projectSettingDetails) {
    const requestArray = [{
      ProjectId: concreteRequestData.ProjectId,
      concretePlacementStart: concreteRequestData.deliveryStartUTC,
      concretePlacementEnd: concreteRequestData.concretePlacementEnd,
      id: concreteRequestData.id,
    }];

    const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
      requestArray,
      projectSettingDetails,
      'add',
    );

    if (isOverlapping?.error) {
      return {
        isValid: false,
        message: isOverlapping.message,
      };
    }

    return { isValid: true };
  },

  async getRequestSeries(recurrenceId, editConcreteData) {
    return await ConcreteRequest.findAll({
      where: [
        Sequelize.and({
          recurrenceId,
          concretePlacementStart: {
            [Op.gte]: editConcreteData.concretePlacementStart,
          },
          isDeleted: false,
        }),
      ],
    });
  },

  updateRequestSeriesData(item1, match) {
    match.description = item1.description;
    match.ProjectId = item1.ProjectId;
    match.notes = item1.notes;
    match.concretePlacementStart = item1.concretePlacementStart;
    match.concretePlacementEnd = item1.concretePlacementEnd;
    match.isPumpConfirmed = item1.isPumpConfirmed;
    match.isPumpRequired = item1.isPumpRequired;
    match.isConcreteConfirmed = item1.isConcreteConfirmed;
    match.ParentCompanyId = item1.ParentCompanyId;
    match.concreteOrderNumber = item1.concreteOrderNumber;
    match.truckSpacingHours = item1.truckSpacingHours;
    match.slump = item1.slump;
    match.concreteQuantityOrdered = item1.concreteQuantityOrdered;
    match.concreteConfirmedOn = item1.concreteConfirmedOn;
    match.pumpLocation = item1.pumpLocation;
    match.pumpOrderedDate = item1.pumpOrderedDate;
    match.pumpWorkStart = item1.pumpWorkStart;
    match.pumpWorkEnd = item1.pumpWorkEnd;
    match.pumpConfirmedOn = item1.pumpConfirmedOn;
    match.cubicYardsTotal = item1.cubicYardsTotal;
    match.hoursToCompletePlacement = item1.hoursToCompletePlacement;
    match.minutesToCompletePlacement = item1.minutesToCompletePlacement;
    match.requestType = item1.requestType;
    match.status = item1.status;
    match.primerForPump = item1.primerForPump;
    match.createdBy = item1.createdBy;
    match.LocationId = item1.LocationId;
    match.OriginationAddress = item1.OriginationAddress;
    match.vehicleType = item1.vehicleType;
    match.OriginationAddressPump = item1.OriginationAddressPump;
    match.vehicleTypePump = item1.vehicleTypePump;
    match.approvedBy = item1.approvedBy;
    match.approved_at = item1.approved_at;
    return match;
  },

  processSeriesComparison(AllRequestSeries, requestSeries) {
    const updateRequestSeries = [];
    const newRequestSeries = [];
    const deleteRequestSeries = [];

    // Find matches and updates
    AllRequestSeries.forEach((item1) => {
      const match = requestSeries.find(
        (item2) =>
          new Date(item2.concretePlacementStart).getTime() ===
          new Date(item1.concretePlacementStart).getTime(),
      );
      if (match) {
        const updatedMatch = this.updateRequestSeriesData(item1, match);
        updateRequestSeries.push(updatedMatch);
      } else {
        newRequestSeries.push(item1);
      }
    });

    // Find items to delete
    requestSeries.forEach((item2) => {
      const match = AllRequestSeries.find(
        (item1) =>
          new Date(item2.concretePlacementStart).getTime() ===
          new Date(item1.concretePlacementStart).getTime(),
      );
      if (!match) {
        deleteRequestSeries.push(item2);
      }
    });

    return {
      updateRequestSeries,
      newRequestSeries,
      deleteRequestSeries,
    };
  },

  async executeSeriesUpdates(deleteRequestSeries, updateRequestSeries) {
    // Delete old bookings
    for (const deleteRequest of deleteRequestSeries) {
      const deleteRequestPayload = { isDeleted: true };
      await ConcreteRequest.update(deleteRequestPayload, {
        where: { id: deleteRequest.id },
      });
    }

    // Update existing bookings
    for (const updateRequest of updateRequestSeries) {
      await ConcreteRequest.update(updateRequest, {
        where: { id: updateRequest.id },
      });
    }
  },

  async createNewSeriesRequests(newRequestSeries, memberDetails) {
    if (newRequestSeries.length === 0) return [];

    let id = null;
    const lastIdValue = await ConcreteRequest.findOne({
      where: { ProjectId: memberDetails.ProjectId },
      order: [['ConcreteRequestId', 'DESC']],
    });

    if (lastIdValue) {
      const newValue = JSON.parse(JSON.stringify(lastIdValue));
      if (newValue && newValue.ConcreteRequestId !== null && newValue.ConcreteRequestId !== undefined) {
        id = newValue.ConcreteRequestId;
      }
    }

    const createdRequests = [];
    for (const newRequest of newRequestSeries) {
      id += 1;
      newRequest.ConcreteRequestId = id;
      const created = await ConcreteRequest.create(newRequest);
      createdRequests.push(created);
    }

    return createdRequests;
  },

  async processEditSeriesRequests(editSeriesRequests, concreteRequestData, loginUser) {
    const results = [];
    for (const editRequest of editSeriesRequests) {
      const result = await this.processIndividualEditRequest(editRequest, concreteRequestData, loginUser);
      results.push(result);
    }
    return results;
  },

  async processIndividualEditRequest(editRequest, concreteRequestData, loginUser) {
    // Process individual edit request logic
    const updateData = {
      description: concreteRequestData.description,
      notes: concreteRequestData.notes,
      // Add other fields as needed
    };

    await ConcreteRequest.update(updateData, {
      where: { id: editRequest.id },
    });

    // Create history entry
    await this.createEditHistory(editRequest, concreteRequestData, loginUser);

    return { success: true, id: editRequest.id };
  },

  async createEditHistory(editRequest, concreteRequestData, loginUser) {
    const historyData = {
      ConcreteRequestId: editRequest.id,
      description: `${loginUser.firstName} ${loginUser.lastName} edited the concrete request`,
      createdBy: loginUser.id,
      ProjectId: concreteRequestData.ProjectId,
    };

    // Create history entry (assuming there's a history model)
    await ConcreteRequestHistory.create(historyData);
  },

  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },

  // Helper function to resolve domain name from enterprise
  async resolveDomainFromEnterprise(domainName) {
    if (!domainName) return null;

    const domainEnterpriseValue = await Enterprise.findOne({
      where: { name: domainName.toLowerCase() },
    });

    return domainEnterpriseValue ? domainName : null;
  },

  // Helper function to get enterprise value from user data
  async getEnterpriseFromUserData(inputData, ParentCompanyId) {
    const { email } = inputData.user;
    if (!email) return null;

    const userData = await publicUser.findOne({ where: { email } });
    if (!userData) return null;

    const memberData = await publicMember.findOne({
      where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
    });

    if (!memberData) {
      return await Enterprise.findOne({
        where: { ParentCompanyId, status: 'completed' },
      });
    }

    if (memberData.isAccount) {
      return await Enterprise.findOne({
        where: { id: memberData.EnterpriseId, status: 'completed' },
      });
    }

    return await Enterprise.findOne({
      where: { ParentCompanyId, status: 'completed' },
    });
  },

  // Helper function to update user data if enterprise value exists
  async updateUserDataIfNeeded(enterpriseValue, inputData) {
    if (!enterpriseValue) return;

    const newUser = await User.findOne({ where: { email: inputData.user.email } });
    if (newUser) {
      inputData.user = newUser;
    }
  },

  async getDynamicModel(inputData) {
    await this.returnProjectModel();

    const ParentCompanyId = this.extractParentCompanyId(inputData);
    let domainName = await this.resolveDomainFromEnterprise(inputData.user.domainName);

    domainName = await this.resolveDomainFromParentCompany(inputData, ParentCompanyId, domainName);
    await this.updateGlobalModels(domainName);

    return null; // ProjectId is not being used, returning null for consistency
  },

  extractParentCompanyId(inputData) {
    return inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
  },

  async resolveDomainFromParentCompany(inputData, ParentCompanyId, domainName) {
    if (this.shouldResolveFromParentCompany(domainName, ParentCompanyId)) {
      const enterpriseValue = await this.getEnterpriseFromUserData(inputData, ParentCompanyId);
      if (enterpriseValue) {
        domainName = enterpriseValue.name.toLowerCase();
        await this.updateUserDataIfNeeded(enterpriseValue, inputData);
      }
    }
    return domainName;
  },

  shouldResolveFromParentCompany(domainName, ParentCompanyId) {
    return !domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined';
  },

  async updateGlobalModels(domainName) {
    const modelObj = await helper.getDynamicModel(domainName);

    DeliveryRequest = modelObj.DeliveryRequest;
    Member = modelObj.Member;
    DeliveryPerson = modelObj.DeliveryPerson;
    DeliverCompany = modelObj.DeliverCompany;
    Role = modelObj.Role;
    DeliverDefineWork = modelObj.DeliverDefineWork;
    Company = modelObj.Company;
    Project = modelObj.Project;
    User = modelObj.User;
    VoidList = modelObj.VoidList;
    DeliveryPersonNotification = modelObj.DeliveryPersonNotification;
    ConcreteRequestResponsiblePerson = modelObj.ConcreteRequestResponsiblePerson;
    Notification = modelObj.Notification;
  },

  async listConcreteRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;
      let order;
      const newResult = { count: 0, rows: [] };

      // Validate void parameter
      const voidValidation = this.validateVoidParameter(params.void);
      if (!voidValidation.isValid) {
        return done(null, { message: voidValidation.message });
      }

      // Get member details
      const memberDetails = await this.getMemberDetails(loginUser.id, params.ProjectId);
      if (!memberDetails) {
        return done(null, { message: 'Project Id/Member does not exist' });
      }

      // Get void lists
      const voidConcreteDelivery = await this.getVoidConcreteRequestIds(params.ProjectId);
      const voidInspection = await this.getVoidInspectionRequestIds(params.ProjectId);

      // Build conditions
      const concreteCondition = {
        ProjectId: +params.ProjectId,
        isDeleted: false,
        ...this.buildVoidCondition(params.void, voidConcreteDelivery, 'ConcreteRequest'),
      };

      const inspectionCondition = {
        ProjectId: +params.ProjectId,
        isDeleted: false,
        ...this.buildVoidCondition(params.void, voidInspection, 'InspectionRequest'),
      };

      if (incomeData.search) {
        inspectionCondition.description = { [Sequelize.Op.iLike]: `%${incomeData.search}%` };
      }

      const roleId = memberDetails.RoleId;
      const memberId = memberDetails.id;

      // Get concrete and inspection requests
      const getConcreteRequest = await ConcreteRequest.getAll(
        inputData,
        concreteCondition,
        roleId,
        memberId,
        incomeData.descriptionFilter,
        incomeData.locationFilter,
        incomeData.concreteSupplierFilter,
        incomeData.orderNumberFilter,
        incomeData.statusFilter,
        incomeData.mixDesignFilter,
        incomeData.startdate,
        incomeData.enddate,
        incomeData.memberFilter,
        incomeData.search,
        order,
        incomeData.sort,
        incomeData.sortByField,
      );

      const getInspectionRequest = await InspectionRequest.getAll(
        inspectionCondition,
        roleId,
        memberId,
        '',
        '',
        '',
        order,
        incomeData.sort,
        incomeData.sortByField,
      );

      const newData = [...getConcreteRequest, ...getInspectionRequest];
      newResult.rows = newData;
      newResult.count = newData.length;
      done(newResult, false);
    } catch (e) {
      console.log('Error in else part', e);
      done(null, e);
    }
  },

  async listConcreteBookingRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;
      let order;
      const newResult = { count: 0, rows: [] };

      // Validate void parameter
      const voidValidation = this.validateVoidParameter(params.void);
      if (!voidValidation.isValid) {
        return done(null, { message: voidValidation.message });
      }

      // Get member details
      const memberDetails = await this.getMemberDetails(loginUser.id, params.ProjectId);
      if (!memberDetails) {
        return done(null, { message: 'Project Id/Member does not exist' });
      }

      // Get void concrete delivery list
      const voidConcreteDelivery = await this.getVoidConcreteRequestIds(params.ProjectId);

      // Build concrete condition
      const concreteCondition = {
        ProjectId: +params.ProjectId,
        isDeleted: false,
        ...this.buildVoidCondition(params.void, voidConcreteDelivery, 'ConcreteRequest'),
      };

      const roleId = memberDetails.RoleId;
      const memberId = memberDetails.id;

      // Get concrete requests
      const getConcreteRequest = await ConcreteRequest.getAll(
        inputData,
        concreteCondition,
        roleId,
        memberId,
        incomeData.descriptionFilter,
        incomeData.locationFilter,
        incomeData.concreteSupplierFilter,
        incomeData.orderNumberFilter,
        incomeData.statusFilter,
        incomeData.mixDesignFilter,
        incomeData.startdate,
        incomeData.enddate,
        incomeData.memberFilter,
        incomeData.search,
        order,
        incomeData.sort,
        incomeData.sortByField,
      );

      const newData = [...getConcreteRequest];
      newResult.rows = newData;
      newResult.count = newData.length;
      done(newResult, false);
    } catch (e) {
      console.log('Error in else part', e);
      done(null, e);
    }
  },

  async listNDR(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;
      let { sort } = inputData.body;
      let { sortByField } = inputData.body;
      let order;

      // Validate void parameter
      const voidValidation = this.validateVoidParameter(params.void);
      if (!voidValidation.isValid) {
        return done(null, { message: voidValidation.message });
      }

      // Get member details
      const memberDetails = await this.getMemberDetails(loginUser.id, params.ProjectId);
      if (!memberDetails) {
        return done(null, { message: 'Project Id/Member does not exist' });
      }

      // Get void delivery list
      const voidDelivery = await this.getVoidDeliveryRequestIds(params.ProjectId);
      const offset = (+params.pageNo - 1) * +params.pageSize;

      // Build base condition
      let condition = this.buildNDRCondition(incomeData, params.ProjectId, voidDelivery, params.void, offset);

      // Apply date filter
      if (incomeData.dateFilter) {
        Object.assign(condition, this.buildDateFilter(incomeData.dateFilter, inputData.headers.timezoneoffset));
      }

      // Apply upcoming filter
      if (incomeData.upcoming) {
        Object.assign(condition, this.buildUpcomingFilter());
        order = 'ASC';
        sort = 'ASC';
        sortByField = 'deliveryStart';
      }

      // Build search condition
      const searchCondition = this.buildSearchCondition(incomeData.search, params.ProjectId);
      const roleId = memberDetails.RoleId;
      const memberId = memberDetails.id;

      // Determine which data processing method to use
      if (this.shouldUseCalendarData(incomeData, memberDetails, params)) {
        const paramsData = {
          condition,
          roleId,
          memberId,
          searchCondition,
          order,
          sort,
          sortByField,
          incomeData,
          params,
          inputData
        }
        await this.processCalendarData(
          paramsData,
          done,
        );
      } else {
        const sParamsData = {
          condition,
          roleId,
          memberId,
          params,
          searchCondition,
          order,
          sort,
          sortByField,
          incomeData,
          inputData
        }
        await this.processStandardData(
          sParamsData,
          done,
        );
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getLimitData(result, index, limit, finalResult, incomeData, timezoneoffset, done) {
    if (index < limit) {
      finalResult.push(result);
      this.getLimitData(
        result,
        index + 1,
        limit,
        finalResult,
        incomeData,
        timezoneoffset,
        (response, err) => {
          if (!err) {
            done(result, false);
          } else {
            done(null, err);
          }
        },
      );
    } else {
      done(result, false);
    }
  },
  async lastConcreteRequest(inputData, done) {
    try {
      const { params } = inputData;
      let data;
      let lastData = {};
      lastData = await ConcreteRequest.findOne({
        where: { ProjectId: params.ProjectId, isDeleted: false },
        order: [['ConcreteRequestId', 'DESC']],
      });
      if (lastData) {
        data = lastData.ConcreteRequestId + 1;
      } else {
        data = 1;
      }
      done({ ConcreteRequestId: data }, false);
    } catch (e) {
      done(null, e);
    }
  },
  // Helper function to validate timezone
  async validateTimezone(timezoneId) {
    const eventTimeZone = await TimeZone.findOne({
      where: {
        isDeleted: false,
        id: +timezoneId,
      },
      attributes: [
        'id',
        'location',
        'isDayLightSavingEnabled',
        'timeZoneOffsetInMinutes',
        'dayLightSavingTimeInMinutes',
        'timezone',
      ],
    });

    if (!eventTimeZone) {
      return { isValid: false, message: 'Provide a valid timezone' };
    }

    return { isValid: true, eventTimeZone };
  },

  // Helper function to validate time ranges
  validateTimeRanges(concreteRequestDetail) {
    // Validate placement time range
    const placementValidation = this.validateTimeRange(
      concreteRequestDetail.startPicker,
      concreteRequestDetail.endPicker,
      'Placement Start Time',
      'Anticipated Completion Time'
    );
    if (!placementValidation.isValid) {
      return placementValidation;
    }

    // Validate pump time range if pump is required
    if (concreteRequestDetail.isPumpRequired) {
      const pumpValidation = this.validateTimeRange(
        concreteRequestDetail.pumpWorkStart,
        concreteRequestDetail.pumpWorkEnd,
        'Pump Show up Time',
        'Pump Completion Time'
      );
      if (!pumpValidation.isValid) {
        return pumpValidation;
      }
    }

    return { isValid: true };
  },

  // Helper function to validate delivery windows
  async validateDeliveryWindows(concreteRequestDetail, eventTimeZone, projectDetails, memberDetails) {
    let startDate, endDate, pumpStartDate, pumpEndDate;

    if (concreteRequestDetail.recurrence) {
      startDate = await this.compareDeliveryDateWithDeliveryWindowDate(
        concreteRequestDetail.concretePlacementStart,
        concreteRequestDetail.startPicker,
        eventTimeZone.timezone,
        projectDetails.ProjectSettings.deliveryWindowTime,
        projectDetails.ProjectSettings.deliveryWindowTimeUnit,
      );
      endDate = await this.compareDeliveryDateWithDeliveryWindowDate(
        concreteRequestDetail.concretePlacementEnd,
        concreteRequestDetail.endPicker,
        eventTimeZone.timezone,
        projectDetails.ProjectSettings.deliveryWindowTime,
        projectDetails.ProjectSettings.deliveryWindowTimeUnit,
      );
    }

    // Validate placement delivery window
    const placementValidation = this.validateDeliveryWindow(
      memberDetails,
      startDate,
      endDate,
      projectDetails,
      concreteRequestDetail.recurrence
    );
    if (!placementValidation.isValid) {
      return placementValidation;
    }

    // Validate pump delivery window if pump is required
    if (concreteRequestDetail.isPumpRequired) {
      pumpStartDate = await this.compareDeliveryDateWithDeliveryWindowDate(
        concreteRequestDetail.pumpOrderedDate,
        concreteRequestDetail.pumpWorkStart,
        eventTimeZone.timezone,
        projectDetails.ProjectSettings.deliveryWindowTime,
        projectDetails.ProjectSettings.deliveryWindowTimeUnit,
      );
      pumpEndDate = await this.compareDeliveryDateWithDeliveryWindowDate(
        concreteRequestDetail.pumpOrderedDate,
        concreteRequestDetail.pumpWorkEnd,
        eventTimeZone.timezone,
        projectDetails.ProjectSettings.deliveryWindowTime,
        projectDetails.ProjectSettings.deliveryWindowTimeUnit,
      );

      const pumpValidation = this.validatePumpDeliveryWindow(memberDetails, pumpStartDate, pumpEndDate);
      if (!pumpValidation.isValid) {
        return pumpValidation;
      }
    }

    return { isValid: true };
  },

  // Helper function to process recurrence types
  async processRecurrenceType(params) {
    const { concreteRequestDetail, eventTimeZone, totalDays, id, memberDetails, roleDetails, accountRoleDetails, projectDetails, inputUser } = params;
    let eventsArray = [];
    let result = {};

    switch (concreteRequestDetail.recurrence) {
      case 'Daily':
        result = await this.processDailyRecurrence({
          concreteRequestDetail,
          eventTimeZone,
          totalDays,
          id: id - 1,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectDetails,
          inputUser,
        });
        eventsArray = result.eventsArray;
        break;

      case 'Weekly':
        result = await this.processWeeklyRecurrence({
          concreteRequestDetail,
          eventTimeZone,
          id: id - 1,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectDetails,
          inputUser,
        });
        eventsArray = result.eventsArray;
        break;

      case 'Does Not Repeat':
        result = await this.processNoRecurrence({
          concreteRequestDetail,
          eventTimeZone,
          id: id - 1,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectDetails,
          inputUser,
        });
        eventsArray = result.eventsArray;
        break;

      case 'Monthly':
      case 'Yearly':
        // These are handled separately in the main function due to their complexity
        break;
    }

    return { eventsArray, result };
  },

  // Helper function to check for overlapping bookings
  async checkOverlappingBookings(eventsArray, projectDetails) {
    if (eventsArray && eventsArray.length > 0) {
      const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
        eventsArray,
        projectDetails,
        'add',
      );
      if (isOverlapping?.error) {
        return { isValid: false, message: isOverlapping.message };
      }
    }
    return { isValid: true };
  },

  async newConcreteRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);

      const timezoneValidation = await this.validateTimezone(inputData.body.TimeZoneId);
      if (!timezoneValidation.isValid) {
        return done(null, { message: timezoneValidation.message });
      }
      const eventTimeZone = timezoneValidation.eventTimeZone;

      const concreteRequestDetail = inputData.body;
      const loginUser = inputData.user;

      const projectDetails = await this.getProjectDetails(concreteRequestDetail.ProjectId);
      const memberDetails = await this.getMemberDetails(loginUser.id, concreteRequestDetail.ProjectId);

      const timeRangeValidation = this.validateTimeRanges(concreteRequestDetail);
      if (!timeRangeValidation.isValid) {
        return done(null, { message: timeRangeValidation.message });
      }

      const deliveryWindowValidation = await this.validateDeliveryWindows(
        concreteRequestDetail, eventTimeZone, projectDetails, memberDetails
      );
      if (!deliveryWindowValidation.isValid) {
        return done(null, { message: deliveryWindowValidation.message });
      }

      if (!projectDetails?.ProjectSettings) {
        return done(null, { message: 'Project does not exist.' });
      }

      this.checkInputDatas(inputData, async (checkResponse, checkError) => {
        if (checkError) return done(null, checkError);
        if (!memberDetails) return done(null, { message: 'You are not allowed create Concrete Booking for this project.' });

        const result = await this.processConcreteRecurrence({
          concreteRequestDetail,
          inputData,
          eventTimeZone,
          memberDetails,
          loginUser,
          projectDetails,
          done
        });

        return result;
      });
    } catch (e) {
      done(null, e);
    }
  },

  async getProjectDetails(projectId) {
    return await Project.getProjectAndSettings({
      isDeleted: false,
      id: +projectId,
    });
  },

  async getMemberDetails(userId, projectId) {
    return await Member.getBy({
      UserId: userId,
      ProjectId: projectId,
      isActive: true,
      isDeleted: false,
    });
  },

  async processConcreteRecurrence({ concreteRequestDetail, inputData, eventTimeZone, memberDetails, loginUser, projectDetails, done }) {
    const roleDetails = await Role.getBy('Project Admin');
    const accountRoleDetails = await Role.getBy('Account Admin');
    let id = await this.getNextConcreteRequestId(memberDetails.ProjectId);

    const range = momentRange.range(
      moment(concreteRequestDetail.concretePlacementStart),
      moment(concreteRequestDetail.concretePlacementEnd)
    );
    const totalDays = Array.from(range.by('day'));

    const { eventsArray } = await this.processRecurrenceType({
      concreteRequestDetail,
      eventTimeZone,
      totalDays,
      id,
      memberDetails,
      roleDetails,
      accountRoleDetails,
      projectDetails,
      inputUser: inputData.user,
    });

    const overlappingValidation = await this.checkOverlappingBookings(eventsArray, projectDetails);
    if (!overlappingValidation.isValid) {
      return done(null, { message: overlappingValidation.message });
    }

    const recurrenceResult = await this.processByRecurrenceType({
      recurrence: concreteRequestDetail.recurrence,
      eventsArray,
      concreteRequestDetail,
      memberDetails,
      eventTimeZone,
      loginUser,
      inputData,
      id,
      roleDetails,
      accountRoleDetails,
      projectDetails,
      totalDays,
      done,
    });

    return recurrenceResult;
  },

  async processByRecurrenceType({
    recurrence,
    eventsArray,
    concreteRequestDetail,
    memberDetails,
    eventTimeZone,
    loginUser,
    inputData,
    id,
    roleDetails,
    accountRoleDetails,
    projectDetails,
    totalDays,
    done
  }) {
    let recurrenceId;

    switch (recurrence) {
      case 'Monthly':
        recurrenceId = await this.insertRecurrenceSeries(
          concreteRequestDetail,
          inputData.user,
          'concreteRequest',
          eventTimeZone.timezone
        );
        await this.processMonthlyRecurrence({
          recurrenceId,
          id,
          concreteRequestDetail,
          eventTimeZone,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectDetails,
          eventsArray,
        });
        break;

      case 'Yearly':
        recurrenceId = await this.insertRecurrenceSeries(
          concreteRequestDetail,
          inputData.user,
          'concreteRequest',
          eventTimeZone.timezone
        );
        await this.processYearlyRecurrence({
          recurrenceId,
          id,
          concreteRequestDetail,
          eventTimeZone,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectDetails,
          eventsArray,
        });
        break;

      case 'Does Not Repeat':
      default:
        recurrenceId = await this.insertRecurrenceSeries(
          concreteRequestDetail,
          inputData.user,
          'concreteRequest',
          eventTimeZone.timezone
        );
        await this.processNonRecurring({
          recurrenceId,
          id,
          totalDays,
          concreteRequestDetail,
          eventTimeZone,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectDetails,
          eventsArray,
        });
    }

    if (eventsArray.length > 0) {
      const isOverlapping = await this.checkDoubleBookingAllowedOrNot(eventsArray, projectDetails, 'add');
      if (isOverlapping?.error) {
        return done(null, { message: isOverlapping.message });
      }

      const result = await this.createConcreteEntitiesAndNotifications({
        eventsArray,
        concreteRequestDetail,
        loginUser,
        memberDetails,
        projectDetails,
        done
      });

      return result;
    }

    return done(null, { message: 'Bookings will not be created for the scheduled date/time' });
  },

  async processMonthlyRecurrence({
    recurrenceId,
    id,
    concreteRequestDetail,
    eventTimeZone,
    memberDetails,
    roleDetails,
    accountRoleDetails,
    projectDetails,
    eventsArray
  }) {
    const months = this.getAllMonthsBetween(concreteRequestDetail.concretePlacementStart, concreteRequestDetail.concretePlacementEnd);
    let currentId = id;

    for (let k = 0; k < months.length; k += +concreteRequestDetail.repeatEveryCount) {
      const recurrenceDates = this.getMonthlyRecurrenceDates(concreteRequestDetail, months[k]);
      for (const date of recurrenceDates) {
        if (this.isInPlacementRange(date, concreteRequestDetail)) {
          currentId++;
          const concreteRequestParam = this.buildConcreteRequestParam({
            date,
            recurrenceId,
            currentId,
            concreteRequestDetail,
            eventTimeZone,
            memberDetails,
            roleDetails,
            accountRoleDetails,
            projectSettings: projectDetails.ProjectSettings
          });
          eventsArray.push(concreteRequestParam);
        }
      }
    }
  },

  getAllMonthsBetween(start, end) {
    let current = moment(start).startOf('month');
    const endDate = moment(end).endOf('month');
    const months = [];
    while (current.isSameOrBefore(endDate)) {
      months.push(current.format('YYYY-MM'));
      current = current.add(1, 'month');
    }
    return months;
  },

  isInPlacementRange(date, detail) {
    const start = moment(detail.concretePlacementStart);
    const end = moment(detail.concretePlacementEnd);
    return moment(date).isBetween(start, end, null, '[]') || moment(date).isSame(start) || moment(date).isSame(end);
  },

  async processYearlyRecurrence({
    recurrenceId,
    id,
    concreteRequestDetail,
    eventTimeZone,
    memberDetails,
    roleDetails,
    accountRoleDetails,
    projectDetails,
    eventsArray
  }) {
    const years = this.getAllYearsBetween(concreteRequestDetail.concretePlacementStart, concreteRequestDetail.concretePlacementEnd);
    let currentId = id;

    for (const yearMonth of years) {
      const recurrenceDates = this.getMonthlyRecurrenceDates(concreteRequestDetail, yearMonth); // Reuses same monthly logic
      for (const date of recurrenceDates) {
        if (this.isInPlacementRange(date, concreteRequestDetail)) {
          currentId++;
          const concreteRequestParam = this.buildConcreteRequestParam({
            date,
            recurrenceId,
            currentId,
            concreteRequestDetail,
            eventTimeZone,
            memberDetails,
            roleDetails,
            accountRoleDetails,
            projectSettings: projectDetails.ProjectSettings
          });
          eventsArray.push(concreteRequestParam);
        }
      }
    }
  },

  getAllYearsBetween(start, end) {
    let current = moment(start).startOf('month');
    const endDate = moment(end).endOf('month');
    const months = [];
    while (current.isSameOrBefore(endDate)) {
      months.push(current.format('YYYY-MM'));
      current = current.add(12, 'months');
    }
    return months;
  },

  async processNonRecurring({
    recurrenceId,
    id,
    totalDays,
    concreteRequestDetail,
    eventTimeZone,
    memberDetails,
    roleDetails,
    accountRoleDetails,
    projectDetails,
    eventsArray
  }) {
    let currentId = id;

    for (const day of totalDays) {
      currentId++;
      const date = day.format('MM/DD/YYYY');
      const concreteRequestParam = this.buildConcreteRequestParam({
        date,
        recurrenceId,
        currentId,
        concreteRequestDetail,
        eventTimeZone,
        memberDetails,
        roleDetails,
        accountRoleDetails,
        projectSettings: projectDetails.ProjectSettings
      });
      eventsArray.push(concreteRequestParam);
    }
  },

  buildConcreteRequestParam({
    date,
    recurrenceId,
    currentId,
    concreteRequestDetail,
    eventTimeZone,
    memberDetails,
    roleDetails,
    accountRoleDetails,
    projectSettings
  }) {
    const startTime = moment.tz(`${date} ${concreteRequestDetail.startPicker}`, 'MM/DD/YYYY HH:mm', eventTimeZone.timezone);
    const endTime = moment.tz(`${date} ${concreteRequestDetail.endPicker}`, 'MM/DD/YYYY HH:mm', eventTimeZone.timezone);
    const pumpStart = concreteRequestDetail.pumpWorkStart ? moment.tz(`${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format('MM/DD/YYYY')} ${concreteRequestDetail.pumpWorkStart}`, 'MM/DD/YYYY HH:mm', eventTimeZone.timezone) : null;
    const pumpEnd = concreteRequestDetail.pumpWorkEnd ? moment.tz(`${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format('MM/DD/YYYY')} ${concreteRequestDetail.pumpWorkEnd}`, 'MM/DD/YYYY HH:mm', eventTimeZone.timezone) : null;

    let request = {
      description: concreteRequestDetail.description,
      ProjectId: concreteRequestDetail.ProjectId,
      notes: concreteRequestDetail.notes,
      concretePlacementStart: startTime.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ'),
      concretePlacementEnd: endTime.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ'),
      isPumpConfirmed: concreteRequestDetail.isPumpConfirmed,
      isPumpRequired: concreteRequestDetail.isPumpRequired,
      isConcreteConfirmed: concreteRequestDetail.isConcreteConfirmed,
      ParentCompanyId: concreteRequestDetail.ParentCompanyId,
      concreteOrderNumber: concreteRequestDetail.concreteOrderNumber,
      truckSpacingHours: concreteRequestDetail.truckSpacingHours,
      slump: concreteRequestDetail.slump,
      concreteQuantityOrdered: concreteRequestDetail.concreteQuantityOrdered,
      concreteConfirmedOn: concreteRequestDetail.concreteConfirmedOn || null,
      pumpLocation: concreteRequestDetail.pumpLocation,
      pumpOrderedDate: pumpStart ? pumpStart.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ') : null,
      pumpWorkStart: pumpStart ? pumpStart.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ') : null,
      pumpWorkEnd: pumpEnd ? pumpEnd.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ') : null,
      pumpConfirmedOn: concreteRequestDetail.pumpConfirmedOn || null,
      cubicYardsTotal: concreteRequestDetail.cubicYardsTotal,
      hoursToCompletePlacement: concreteRequestDetail.hoursToCompletePlacement,
      minutesToCompletePlacement: concreteRequestDetail.minutesToCompletePlacement,
      ConcreteRequestId: currentId,
      requestType: 'concreteRequest',
      status: 'Tentative',
      primerForPump: concreteRequestDetail.primerForPump,
      createdBy: memberDetails.id,
      recurrenceId,
      LocationId: concreteRequestDetail.LocationId,
      OriginationAddress: concreteRequestDetail.originationAddress,
      vehicleType: concreteRequestDetail.vehicleType,
      OriginationAddressPump: concreteRequestDetail.originationAddressPump,
      vehicleTypePump: concreteRequestDetail.vehicleTypePump,
    };

    const shouldAutoApprove =
      memberDetails.RoleId === roleDetails.id ||
      memberDetails.RoleId === accountRoleDetails.id ||
      memberDetails.isAutoApproveEnabled ||
      projectSettings?.isAutoApprovalEnabled;

    if (shouldAutoApprove) {
      request.status = 'Approved';
      request.approvedBy = memberDetails.id;
      request.approved_at = new Date();
    }

    return request;
  },

  async createConcreteEntitiesAndNotifications({
    eventsArray,
    concreteRequestDetail,
    loginUser,
    memberDetails,
    projectDetails,
    done
  }) {
    let newConcreteRequestData = {};

    for (const event of eventsArray) {
      newConcreteRequestData = await this.createConcreteEntitiesForEvent({
        event,
        concreteRequestDetail,
        loginUser
      });

      await this.createHistoryAndNotification({
        newConcreteRequestData,
        concreteRequestDetail,
        memberDetails,
        loginUser
      });
    }

    if (Object.keys(newConcreteRequestData).length > 0) {
      return await this.sendConcreteNotifications({
        newConcreteRequestData,
        memberDetails,
        loginUser,
        concreteRequestDetail,
        projectDetails,
        done
      });
    }

    return done(null, {
      message: 'Bookings will not be created for the scheduled date/time'
    });
  },

  async createConcreteEntitiesForEvent({ event, concreteRequestDetail, loginUser }) {
    const newConcreteRequestData = await ConcreteRequest.createInstance(event);

    const {
      responsiblePersons,
      concreteSupplier,
      pumpSize,
      mixDesign,
      location,
      GateId,
      EquipmentId
    } = concreteRequestDetail;

    const updateParam = {
      ConcreteRequestId: newConcreteRequestData.id,
      ConcreteRequestCode: newConcreteRequestData.ConcreteRequestId,
      ProjectId: concreteRequestDetail.ProjectId
    };

    await this.addSuppliers(concreteSupplier, updateParam);
    await this.addLocation(location, updateParam, concreteRequestDetail.ProjectId, loginUser.id);
    await this.addPumpSizes(pumpSize, updateParam, concreteRequestDetail.ProjectId, loginUser.id);
    await this.addGates([GateId], updateParam);
    await this.addEquipment(EquipmentId, updateParam);
    await this.addMixDesigns(mixDesign, updateParam, concreteRequestDetail.ProjectId, loginUser.id);
    await this.addResponsiblePersons(responsiblePersons, updateParam);

    return newConcreteRequestData;
  },

  async createHistoryAndNotification({
    newConcreteRequestData,
    concreteRequestDetail,
    memberDetails,
    loginUser
  }) {
    const history = {
      ConcreteRequestId: newConcreteRequestData.id,
      MemberId: memberDetails.id,
      type: 'create',
      description: `${loginUser.firstName} ${loginUser.lastName} Created Concrete Booking, ${concreteRequestDetail.description}.`
    };


    await ConcreteRequestHistory.createInstance(history);
  },

  async addSuppliers(concreteSupplier, updateParam) {
    for (const supplierId of concreteSupplier) {
      await ConcreteRequestCompany.createInstance({
        ...updateParam,
        CompanyId: supplierId
      });
    }
  },

  async addLocation(location, updateParam, projectId, createdBy) {
    const locationData = await ConcreteLocation.createConcreteLocation({
      location,
      ProjectId: projectId,
      isDeleted: false,
      createdBy
    });

    await ConcreteRequestLocation.createInstance({
      ...updateParam,
      ConcreteLocationId: locationData.id
    });
  },

  async addPumpSizes(pumpSize, updateParam, projectId, createdBy) {
    for (const pump of pumpSize) {
      const pumpSizeParam = { ...updateParam };

      if (pump.chosenFromDropdown) {
        pumpSizeParam.ConcretePumpSizeId = pump.id;
      } else {
        const existPumpSizes = await ConcretePumpSize.findAll({
          where: { ProjectId: projectId, isDeleted: false }
        });

        const existing = existPumpSizes.find(
          (item) => item.pumpSize.toLowerCase().trim() === pump.pumpSize.toLowerCase().trim()
        );

        if (existing) {
          pumpSizeParam.ConcretePumpSizeId = existing.id;
        } else {
          const newPump = await ConcretePumpSize.createConcretePumpSize({
            pumpSize: pump.pumpSize,
            ProjectId: projectId,
            isDeleted: false,
            createdBy
          });
          pumpSizeParam.ConcretePumpSizeId = newPump.id;
        }
      }
      await ConcreteRequestPumpSize.createInstance(pumpSizeParam);
    }
  },

  async addGates(gateIds, updateParam) {
    for (const gateId of gateIds) {
      await ConcreteGate.createInstance({ ...updateParam, GateId: gateId });
    }
  },

  async addEquipment(EquipmentId, updateParam) {
    for (const equipmentId of EquipmentId) {
      await ConcreteEquipment.createInstance({
        ...updateParam,
        EquipmentId: equipmentId
      });
    }
  },

  async addMixDesigns(mixDesign, updateParam, projectId, createdBy) {
    for (const design of mixDesign) {
      const mixDesignParam = { ...updateParam };

      if (design.chosenFromDropdown) {
        mixDesignParam.ConcreteMixDesignId = design.id;
      } else {
        const existMixDesigns = await ConcreteMixDesign.findAll({
          where: { ProjectId: projectId, isDeleted: false }
        });

        const existing = existMixDesigns.find(
          (item) => item.mixDesign.toLowerCase().trim() === design.mixDesign.toLowerCase().trim()
        );

        if (existing) {
          mixDesignParam.ConcreteMixDesignId = existing.id;
        } else {
          const newMix = await ConcreteMixDesign.createConcreteMixDesign({
            mixDesign: design.mixDesign,
            ProjectId: projectId,
            isDeleted: false,
            createdBy
          });
          mixDesignParam.ConcreteMixDesignId = newMix.id;
        }
      }

      await ConcreteRequestMixDesign.createInstance(mixDesignParam);
    }
  },

  async addResponsiblePersons(responsiblePersons, updateParam) {
    for (const personId of responsiblePersons) {
      await ConcreteRequestResponsiblePerson.createInstance({
        ...updateParam,
        MemberId: personId
      });
    }
  },


  async sendConcreteNotifications({
    newConcreteRequestData,
    memberDetails,
    loginUser,
    concreteRequestDetail,
    projectDetails,
    done
  }) {
    const locationChosen = await Locations.findOne({
      where: {
        ProjectId: concreteRequestDetail.ProjectId,
        id: concreteRequestDetail.LocationId
      }
    });

    const locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Created Concrete Booking, ${concreteRequestDetail.description}. Location: ${locationChosen.locationPath}.`;

    const history = {
      ConcreteRequestId: newConcreteRequestData.id,
      MemberId: memberDetails.id,
      type: 'create',
      description: `${loginUser.firstName} ${loginUser.lastName} Created Concrete Booking, ${concreteRequestDetail.description} .`,
      locationFollowDescription,
      memberData: [],
      ProjectId: concreteRequestDetail.ProjectId,
      firstName: loginUser.firstName,
      profilePic: loginUser.profilePic,
      createdAt: new Date(),
      projectName: projectDetails.projectName
    };

    const notification = {
      ...history,
      LocationId: concreteRequestDetail.LocationId,
      title: 'Concrete Booking Creation',
      isDeliveryRequest: false,
      requestType: 'concreteRequest',
      recurrenceType: `${concreteRequestDetail.recurrence} From ${moment(concreteRequestDetail.concretePlacementStart).format('MM/DD/YYYY')} to ${moment(concreteRequestDetail.concretePlacementEnd).format('MM/DD/YYYY')}`
    };

    const newNotification = await Notification.createInstance(notification);

    const memberLocationPreference = await LocationNotificationPreferences.findAll({
      where: {
        ProjectId: concreteRequestDetail.ProjectId,
        LocationId: concreteRequestDetail.LocationId,
        follow: true
      },
      include: [{
        association: 'Member',
        attributes: ['id', 'RoleId'],
        where: {
          [Op.and]: [{ id: { [Op.ne]: memberDetails.id } }]
        },
        include: [{
          association: 'User',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }]
      }]
    });

    const locationFollowMembers = memberLocationPreference.map(el => el.Member.id);

    const personData = await ConcreteRequestResponsiblePerson.findAll({
      where: { ConcreteRequestId: newConcreteRequestData.id, isDeleted: false },
      include: [{
        association: 'Member',
        include: [{
          association: 'User',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }],
        where: {
          [Op.and]: {
            RoleId: { [Op.notIn]: [1, 2] },
            id: { [Op.notIn]: locationFollowMembers }
          }
        },
        attributes: ['id', 'RoleId']
      }],
      attributes: ['id']
    });

    const adminData = await Member.findAll({
      where: {
        [Op.and]: [
          { ProjectId: concreteRequestDetail.ProjectId },
          { isDeleted: false },
          { id: { [Op.in]: concreteRequestDetail.responsiblePersons } },
          { id: { [Op.ne]: newNotification.MemberId } },
          { id: { [Op.notIn]: locationFollowMembers } }
        ]
      },
      include: [{
        association: 'User',
        attributes: ['id', 'firstName', 'lastName', 'email']
      }],
      attributes: ['id']
    });

    if (memberLocationPreference.length > 0) {
      await pushNotification.sendMemberLocationPreferencePushNotificationForConcrete(
        memberLocationPreference,
        newConcreteRequestData.ConcreteRequestId,
        history.locationFollowDescription,
        newConcreteRequestData.requestType,
        newConcreteRequestData.ProjectId,
        newConcreteRequestData.id,
        3
      );

      await notificationHelper.createMemberDeliveryLocationInAppNotification(
        DeliveryPersonNotification,
        concreteRequestDetail.ProjectId,
        newNotification.id,
        memberLocationPreference,
        3
      );
    }

    history.memberData = personData;
    history.adminData = adminData;

    await notificationHelper.createDeliveryPersonNotification(
      adminData,
      [],
      projectDetails,
      newNotification,
      DeliveryPersonNotification,
      memberDetails,
      loginUser,
      3,
      'created a',
      'Concrete Request',
      `concrete Booking (${newConcreteRequestData.ConcreteRequestId} - ${newConcreteRequestData.description})`,
      newConcreteRequestData.ConcreteRequestId
    );

    const checkMemberNotification = await NotificationPreference.findAll({
      where: {
        ProjectId: concreteRequestDetail.ProjectId,
        isDeleted: false
      },
      attributes: ['id', 'MemberId', 'ProjectId', 'ParentCompanyId', 'NotificationPreferenceItemId', 'instant', 'dailyDigest'],
      include: [{
        association: 'NotificationPreferenceItem',
        where: { id: 3, isDeleted: false },
        attributes: ['id', 'description', 'inappNotification', 'emailNotification']
      }]
    });

    history.notificationPreference = checkMemberNotification;

    await this.sendEmailNotificationToUser(
      history,
      memberDetails,
      loginUser,
      newConcreteRequestData,
      concreteRequestDetail,
      memberLocationPreference
    );

    const memberLocationPreferenceNotify = await LocationNotificationPreferences.findAll({
      where: {
        ProjectId: concreteRequestDetail.ProjectId,
        LocationId: concreteRequestDetail.LocationId,
        follow: true
      },
      include: [{
        association: 'Member',
        attributes: ['id', 'RoleId'],
        where: {
          [Op.and]: [{ id: { [Op.ne]: memberDetails.id } }]
        },
        include: [{
          association: 'User',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }]
      }]
    });

    if (memberLocationPreferenceNotify.length > 0) {
      history.memberData.push(...memberLocationPreferenceNotify);
    }

    history.ConcreteRequestId = newConcreteRequestData.ConcreteRequestId;

    await pushNotification.sendPushNotificationForConcrete(
      history,
      3,
      concreteRequestDetail.ProjectId
    );

    return done(history, false);
  },

  async compareDeliveryDateWithDeliveryWindowDate(
    dateStr,
    timeStr,
    timezoneStr,
    deliveryWindowTime,
    deliveryWindowTimeUnit,
  ) {
    const datetimeStr = `${moment(dateStr).format('YYYY-MM-DD')}T${timeStr}`;
    const datetime = moment.tz(datetimeStr, timezoneStr);
    const currentDatetime = moment
      .tz(timezoneStr)
      .add(deliveryWindowTime, deliveryWindowTimeUnit)
      .startOf('minute');
    return datetime.isSameOrBefore(currentDatetime);
  },
  async checkInputDatas(inputData, done) {
    await this.getDynamicModel(inputData);
    const concreteRequestData = inputData.body;
    const { concreteSupplier } = concreteRequestData;
    const { responsiblePersons } = concreteRequestData;
    const inputProjectId = +concreteRequestData.ProjectId;
    const memberList = await Member.count({
      where: { id: { [Op.in]: responsiblePersons }, ProjectId: inputProjectId, isDeleted: false },
    });
    const companyList = await Company.count({
      where: {
        [Op.or]: [
          {
            id: { [Op.in]: concreteSupplier },
            ProjectId: +inputProjectId,
            isDeleted: false,
          },
          {
            id: {
              [Op.in]: concreteSupplier,
            },
            isParent: true,
            ParentCompanyId: +concreteRequestData.ParentCompanyId,
            isDeleted: false,
          },
        ],
      },
    });
    if (
      concreteRequestData.responsiblePersons &&
      concreteRequestData.responsiblePersons.length > 0 &&
      memberList !== responsiblePersons.length
    ) {
      return done(null, { message: 'Some Member is not in the project' });
    }
    if (
      concreteRequestData.concreteSupplier &&
      concreteRequestData.concreteSupplier.length > 0 &&
      companyList !== concreteSupplier.length
    ) {
      return done(null, { message: 'Some Company is not in the project' });
    }

    if (concreteRequestData.mixDesign && concreteRequestData.mixDesign.length > 0) {
      const mixDesignTempArray = [];
      concreteRequestData.mixDesign.forEach((element) => {
        if (element.chosenFromDropdown) {
          mixDesignTempArray.push(element.id);
        }
      });
      const concreteMixDesignCount = await ConcreteMixDesign.count({
        where: { id: { [Op.in]: mixDesignTempArray }, ProjectId: inputProjectId, isDeleted: false },
      });
      if (concreteMixDesignCount !== mixDesignTempArray.length) {
        return done(null, { message: 'Some Mix Design is not in the project' });
      }
    }
    if (concreteRequestData.pumpSize && concreteRequestData.pumpSize.length > 0) {
      const pumpSizeTempArray = [];
      concreteRequestData.pumpSize.forEach((element) => {
        if (element.chosenFromDropdown) {
          pumpSizeTempArray.push(element.id);
        }
      });
      const concretePumpSizeCount = await ConcretePumpSize.count({
        where: { id: { [Op.in]: pumpSizeTempArray }, ProjectId: inputProjectId, isDeleted: false },
      });
      if (concretePumpSizeCount !== pumpSizeTempArray.length) {
        return done(null, { message: 'Some Pump Size is not in the project' });
      }
    }
    return done(true, false);
  },
  async updateValues(condition, done) {
    try {
      await ConcreteRequestResponsiblePerson.update({ isDeleted: true }, { where: condition });
      await ConcreteRequestCompany.update({ isDeleted: true }, { where: condition });
      await ConcreteRequestMixDesign.update({ isDeleted: true }, { where: condition });
      await ConcreteRequestPumpSize.update({ isDeleted: true }, { where: condition });
      done({ status: 'ok' }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async editConcreteRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const concreteRequestData = inputData.body;
      const loginUser = inputData.user;
      const { recurrenceId } = concreteRequestData;

      // Add loginUser to concreteRequestData for helper functions
      concreteRequestData.loginUser = loginUser;

      // Get required data using helper functions
      const {
        projectSettingDetails,
        memberDetails,
        roleDetails,
        accountRoleDetails,
      } = await this.getEditConcreteRequestData(concreteRequestData);

      // Create timestamps
      const { deliveryStartUTC } = this.createEditTimestamps(concreteRequestData);
      concreteRequestData.deliveryStartUTC = deliveryStartUTC;

      // Create date range for recurrence
      const range = momentRange.range(
        moment(concreteRequestData.concretePlacementStart),
        moment(concreteRequestData.recurrenceEndDate).add(1, 'day'),
      );
      const totalDays = Array.from(range.by('day'));

      if (!projectSettingDetails) {
        return done(null, { message: 'Project settings not found' });
      }

      const editConcreteData = await ConcreteRequest.findOne({
        where: { id: concreteRequestData.id }
      });

      // Handle single request edit (seriesOption === 1)
      if (concreteRequestData.seriesOption === 1) {
        const validation = await this.validateSingleRequestEdit(concreteRequestData, projectSettingDetails);
        if (!validation.isValid) {
          return done(null, { message: validation.message });
        }
      }

      // Handle series edit (seriesOption === 2)
      if (concreteRequestData.seriesOption === 2) {
        const requestSeries = await this.getRequestSeries(recurrenceId, editConcreteData);

        if (concreteRequestData.recurrenceEdited) {
          await this.createNewRecurrenceEvents(
            concreteRequestData,
            memberDetails,
            totalDays,
            roleDetails,
            accountRoleDetails,
            projectSettingDetails,
            async (checkResponse, checkError) => {
              if (checkError) {
                return done(null, checkError);
              }

              // Process series comparison and updates
              const {
                updateRequestSeries,
                newRequestSeries,
                deleteRequestSeries,
              } = this.processSeriesComparison(checkResponse, requestSeries);

              // Execute updates and deletions
              await this.executeSeriesUpdates(deleteRequestSeries, updateRequestSeries);

              // Create new requests
              await this.createNewSeriesRequests(newRequestSeries, memberDetails);

              done({ status: 'ok' }, false);
            },
          );
        }
      }

      // Handle other series options and complete the function
      done({ status: 'ok' }, false);
    } catch (e) {
      console.log('Error in editConcreteRequest:', e);
      done(null, e);
    }
  },

  async getSingleConcreteRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const getConcreteRequest = await ConcreteRequest.findOne({
        where: {
          ConcreteRequestId: params.ConcreteRequestId,
          ProjectId: params.ProjectId,
          isDeleted: false,
        },
      });
      const concreteRequest = await ConcreteRequest.getSingleConcreteRequestData({
        id: +getConcreteRequest.id,
      });
      done(concreteRequest, false);
    } catch (e) {
      done(null, e);
    }
  },

  // Helper function to validate concrete status update permissions
  async validateConcreteStatusUpdatePermissions(updateData, loginUser, statusValue) {
    const memberValue = await Member.findOne({
      where: Sequelize.and({
        UserId: loginUser.id,
        ProjectId: statusValue.ProjectId,
        isDeleted: false,
      }),
    });

    if (!memberValue) {
      return { error: true, message: 'Member not found in project.' };
    }

    if (![1, 2, 3, 4].includes(memberValue.RoleId)) {
      return { error: true, message: 'Insufficient permissions to update status.' };
    }

    return { error: false, memberValue };
  },

  // Helper function to get concrete location and notification preferences
  async getConcreteLocationAndNotificationPreferences(statusValue, memberValue) {
    const locationChosen = await Locations.findOne({
      where: {
        ProjectId: statusValue.ProjectId,
        id: statusValue.LocationId,
      },
    });

    const memberLocationPreference = await LocationNotificationPreferences.findAll({
      where: {
        ProjectId: statusValue.ProjectId,
        LocationId: statusValue.LocationId,
        follow: true,
      },
      include: [
        {
          association: 'Member',
          attributes: ['id', 'RoleId'],
          where: {
            [Op.and]: [
              {
                id: { [Op.ne]: memberValue.id },
              },
            ],
          },
          include: [
            {
              association: 'User',
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      ],
    });

    return { locationChosen, memberLocationPreference };
  },

  // Helper function to prepare concrete history and notification data
  async prepareConcreteHistoryAndNotificationData(statusValue, memberValue, loginUser, updateData, locationChosen) {
    const history = {
      ConcreteRequestId: statusValue.id,
      MemberId: memberValue.id,
      ConcreteId: statusValue.ConcreteRequestId,
    };

    const notification = { ...history };
    notification.ProjectId = statusValue.ProjectId;

    if (statusValue?.recurrence?.recurrence) {
      notification.recurrenceType = `${statusValue.recurrence.recurrence} From ${moment(
        statusValue.recurrence.recurrenceStartDate,
      ).format('MM/DD/YYYY')} to ${moment(
        statusValue.recurrence.recurrenceEndDate,
      ).format('MM/DD/YYYY')}`;
    }

    notification.requestType = 'concreteRequest';

    return { history, notification };
  },

  // Helper function to handle concrete approved status
  async handleConcreteApprovedStatus(updateData, statusValue, memberValue, loginUser, history, notification, locationChosen) {
    if (updateData.statuschange && updateData.statuschange === 'Reverted') {
      history.type = 'approved';
      history.description = `${loginUser.firstName} ${loginUser.lastName} Reverted the status from delivery to approved for Concrete Booking , ${statusValue.description}`;
      history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Reverted the status from delivery to approved for Concrete Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;
    } else {
      history.type = 'approved';
      history.description = `${loginUser.firstName} ${loginUser.lastName} Approved the Concrete Booking`;
      history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Approved the Concrete Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;
    }

    notification.title = `Concrete Booking Approved by ${loginUser.firstName} ${loginUser.lastName}`;
    await ConcreteRequest.update(
      { status: updateData.status, approvedBy: memberValue.id, approved_at: new Date() },
      { where: { id: updateData.id } },
    );

    const object = {
      ProjectId: statusValue.ProjectId,
      MemberId: memberValue.id,
      ConcreteRequestId: statusValue.id,
      isDeleted: false,
      type: updateData.status.toLowerCase(),
      description: history.description,
    };
    await ConcreteRequestHistory.createInstance(object);

    return { history, notification };
  },

  // Helper function to handle concrete declined or delivered status
  async handleConcreteDeclinedOrDeliveredStatus(updateData, statusValue, memberValue, loginUser, history, notification, locationChosen) {
    history.type = updateData.status.toLowerCase();
    history.description = `${loginUser.firstName} ${loginUser.lastName} ${updateData.status} the Concrete Booking`;
    history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} ${updateData.status} the Concrete Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;

    notification.title = `Concrete Booking ${updateData.status} by ${loginUser.firstName} ${loginUser.lastName}`;
    await ConcreteRequest.update(
      { status: updateData.status, approvedBy: memberValue.id, approved_at: new Date() },
      { where: { id: updateData.id } },
    );

    const object = {
      ProjectId: statusValue.ProjectId,
      MemberId: memberValue.id,
      ConcreteRequestId: statusValue.id,
      isDeleted: false,
      type: updateData.status.toLowerCase(),
      description: history.description,
    };
    await ConcreteRequestHistory.createInstance(object);

    return { history, notification };
  },

  async updateConcreteRequestStatus(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const updateData = inputData.body;
      const loginUser = inputData.user;

      const statusValue = await ConcreteRequest.findOne({
        where: { id: updateData.id, isDeleted: false },
        include: [
          {
            association: 'recurrence',
            required: false,
            attributes: [
              'id',
              'recurrence',
              'recurrenceStartDate',
              'recurrenceEndDate',
              'dateOfMonth',
              'monthlyRepeatType',
              'repeatEveryCount',
              'days',
              'requestType',
              'repeatEveryType',
              'chosenDateOfMonth',
              'createdBy',
              'chosenDateOfMonthValue',
            ],
          },
          {
            association: 'memberDetails',
            required: false,
            where: { isDeleted: false, isActive: true },
            attributes: ['id'],
            include: [
              {
                association: 'Member',
                where: { isDeleted: false },
                attributes: ['id'],
                include: [
                  {
                    association: 'User',
                    where: { isDeleted: false },
                    attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                  },
                ],
              },
            ],
          },
        ],
      });

      if (!statusValue) {
        return done(null, { message: 'Id does not exist.' });
      }

      // Validate permissions
      const permissionResult = await this.validateConcreteStatusUpdatePermissions(updateData, loginUser, statusValue);
      if (permissionResult.error) {
        return done(null, { message: permissionResult.message });
      }
      const { memberValue } = permissionResult;

      // Get location and notification preferences
      const { locationChosen, memberLocationPreference } = await this.getConcreteLocationAndNotificationPreferences(statusValue, memberValue);

      // Prepare history and notification data
      const { history, notification } = await this.prepareConcreteHistoryAndNotificationData(statusValue, memberValue, loginUser, updateData, locationChosen);

      // Handle different status types
      if (updateData.status === 'Approved') {
        const result = await this.handleConcreteApprovedStatus(updateData, statusValue, memberValue, loginUser, history, notification, locationChosen);
        await this.processConcreteApprovedStatusNotifications(result.history, result.notification, statusValue, memberLocationPreference, memberValue, loginUser, done);
      } else if (updateData.status === 'Declined' || updateData.status === 'Delivered') {
        const result = await this.handleConcreteDeclinedOrDeliveredStatus(updateData, statusValue, memberValue, loginUser, history, notification, locationChosen);
        const paramsData = { history: result.history, notification: result.notification, statusValue, memberLocationPreference, memberValue, loginUser, updateData }
        await this.processConcreteDeclinedOrDeliveredStatusNotifications(paramsData, done);
      } else {
        return done(null, { message: 'Invalid Status' });
      }

    } catch (e) {
      return done(null, e);
    }
  },

  // Helper function to process concrete approved status notifications
  async processConcreteApprovedStatusNotifications(history, notification, statusValue, memberLocationPreference, memberValue, loginUser, done) {
    // Add common history properties
    history.firstName = loginUser.firstName;
    history.profilePic = loginUser.profilePic;
    history.createdAt = new Date();
    history.ProjectId = statusValue.ProjectId;

    const projectDetails = await Project.findByPk(statusValue.ProjectId);
    history.projectName = projectDetails.projectName;

    const newNotification = await Notification.createInstance(notification);

    // Get member and admin data
    const { personData, adminData } = await this.getConcreteMemberAndAdminDataForNotifications(statusValue, memberLocationPreference, memberValue, newNotification);

    // Send notifications
    if (memberLocationPreference && memberLocationPreference.length > 0) {
      await pushNotification.sendMemberLocationPreferencePushNotification(
        memberLocationPreference,
        statusValue.ConcreteRequestId,
        history.locationFollowDescription,
        statusValue.requestType,
        statusValue.ProjectId,
        statusValue.id,
        6,
      );

      await notificationHelper.createMemberDeliveryLocationInAppNotification(
        DeliveryPersonNotification,
        statusValue.ProjectId,
        newNotification.id,
        memberLocationPreference,
        6,
      );
    }

    history.memberData = personData;
    history.adminData = adminData;

    await notificationHelper.createDeliveryPersonNotification(
      adminData,
      personData,
      projectDetails,
      newNotification,
      DeliveryPersonNotification,
      memberValue,
      loginUser,
      6,
      'approved a',
      'Concrete Request',
      `Concrete Booking (${statusValue.ConcreteRequestId} - ${statusValue.description})`,
      statusValue.id,
    );

    // Get notification preferences and send push notifications
    const checkMemberNotification = await NotificationPreference.findAll({
      where: {
        ProjectId: statusValue.ProjectId,
        isDeleted: false,
      },
      attributes: [
        'id',
        'MemberId',
        'ProjectId',
        'ParentCompanyId',
        'NotificationPreferenceItemId',
        'instant',
        'dailyDigest',
      ],
      include: [
        {
          association: 'NotificationPreferenceItem',
          where: {
            id: 6,
            isDeleted: false,
          },
          attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
        },
      ],
    });

    history.notificationPreference = checkMemberNotification;
    await pushNotification.sendDeviceToken(history, 6, statusValue.ProjectId);

    if (memberLocationPreference && memberLocationPreference.length > 0) {
      history.memberData.push(...memberLocationPreference);
    }

    // Send guest user notifications
    await this.sendConcreteGuestUserNotifications(statusValue, loginUser, 'Approved');

    return done(history, false);
  },

  // Helper function to process concrete declined or delivered status notifications
  async processConcreteDeclinedOrDeliveredStatusNotifications(params, done) {
    const { history, notification, statusValue, memberLocationPreference, memberValue, loginUser, updateData } = params;
    // Add common history properties
    history.firstName = loginUser.firstName;
    history.profilePic = loginUser.profilePic;
    history.createdAt = new Date();
    history.ProjectId = statusValue.ProjectId;

    const projectDetails = await Project.findByPk(statusValue.ProjectId);
    history.projectName = projectDetails.projectName;

    const newNotification = await Notification.createInstance(notification);

    // Get member and admin data
    const { personData, adminData } = await this.getConcreteMemberAndAdminDataForNotifications(statusValue, memberLocationPreference, memberValue, newNotification);

    // Send notifications similar to approved status
    if (memberLocationPreference && memberLocationPreference.length > 0) {
      await pushNotification.sendMemberLocationPreferencePushNotification(
        memberLocationPreference,
        statusValue.ConcreteRequestId,
        history.locationFollowDescription,
        statusValue.requestType,
        statusValue.ProjectId,
        statusValue.id,
        6,
      );

      await notificationHelper.createMemberDeliveryLocationInAppNotification(
        DeliveryPersonNotification,
        statusValue.ProjectId,
        newNotification.id,
        memberLocationPreference,
        6,
      );
    }

    history.memberData = personData;
    history.adminData = adminData;

    await notificationHelper.createDeliveryPersonNotification(
      adminData,
      personData,
      projectDetails,
      newNotification,
      DeliveryPersonNotification,
      memberValue,
      loginUser,
      6,
      `${updateData.status.toLowerCase()}`,
      'Concrete Request',
      `Concrete Booking (${statusValue.ConcreteRequestId} - ${statusValue.description})`,
      statusValue.id,
    );

    await pushNotification.sendDeviceToken(history, 6, statusValue.ProjectId);

    if (memberLocationPreference && memberLocationPreference.length > 0) {
      history.memberData.push(...memberLocationPreference);
    }

    // Send guest user notifications
    await this.sendConcreteGuestUserNotifications(statusValue, loginUser, updateData.status);

    return done(history, false);
  },

  // Helper function to get concrete member and admin data for notifications
  async getConcreteMemberAndAdminDataForNotifications(statusValue, memberLocationPreference, memberValue, newNotification) {
    const locationFollowMembers = [];
    memberLocationPreference.forEach(async (element) => {
      locationFollowMembers.push(element.Member.id);
    });

    const bookingMemberDetails = [];
    statusValue.memberDetails.forEach(async (element) => {
      bookingMemberDetails.push(element.Member.id);
    });

    const personData = await ConcreteRequestResponsiblePerson.findAll({
      where: { ConcreteRequestId: statusValue.id, isDeleted: false },
      include: [
        {
          association: 'Member',
          include: [
            {
              association: 'User',
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
          where: {
            [Op.and]: {
              RoleId: {
                [Op.notIn]: [1, 2],
              },
              id: { [Op.notIn]: locationFollowMembers },
            },
          },
          attributes: ['id', 'RoleId'],
        },
      ],
      attributes: ['id'],
    });

    const adminData = await Member.findAll({
      where: {
        [Op.and]: [
          { ProjectId: statusValue.ProjectId },
          { isDeleted: false },
          { id: { [Op.in]: bookingMemberDetails } },
          { id: { [Op.ne]: newNotification.MemberId } },
          { id: { [Op.notIn]: locationFollowMembers } },
        ],
      },
      include: [
        {
          association: 'User',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
      ],
      attributes: ['id'],
    });

    return { personData, adminData, locationFollowMembers, bookingMemberDetails };
  },

  // Helper function to send concrete guest user notifications
  async sendConcreteGuestUserNotifications(statusValue, loginUser, status) {
    if (statusValue?.memberDetails) {
      const userDataMail = statusValue.memberDetails;
      for (const userMail of userDataMail) {
        const responsibleGuestUser = userMail.Member.isGuestUser;
        if (responsibleGuestUser) {
          const guestMailPayload = {
            email: userMail.Member.User.email,
            guestName: userMail.Member.User.firstName,
            content: `We would like to inform you that
        ${loginUser.firstName} ${loginUser.lastName} ${status} the Concrete Booking - ${statusValue.description}.`,
          };
          await MAILER.sendMail(
            guestMailPayload,
            'notifyGuestOnEdit',
            `Concrete Booking ${status} by ${loginUser.firstName} `,
            `Concrete Booking ${status}`,
            async (info, err) => {
              console.log(info, err);
            },
          );
        }
      }
    }
  },

  // Helper function to update concrete request edit history
  async updateEditConcreteRequestHistory(
    userEditedConcreteRequestData,
    existsConcreteRequestData,
    updatedConcreteRequest,
    history,
    loginUser,
  ) {
    const historyObject = history;

    this._updateDescriptionAndLocation(
      userEditedConcreteRequestData,
      existsConcreteRequestData,
      updatedConcreteRequest,
      historyObject,
      loginUser
    );

    this._updatePlacementTimes(
      userEditedConcreteRequestData,
      existsConcreteRequestData,
      historyObject,
      loginUser
    );

    this._updateNotesAndMinutes(
      userEditedConcreteRequestData,
      existsConcreteRequestData,
      historyObject,
      loginUser
    );
  },

  // --- Helper Functions ---

  _updateDescriptionAndLocation(
    userEditedConcreteRequestData,
    existsConcreteRequestData,
    updatedConcreteRequest,
    historyObject,
    loginUser
  ) {
    // Check for description changes
    if (
      (userEditedConcreteRequestData.description?.toLowerCase() ?? '') !==
      (existsConcreteRequestData.description?.toLowerCase() ?? '')
    ) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Description ${userEditedConcreteRequestData.description}`;
      ConcreteRequestHistory.createInstance(historyObject);
    }

    // Check for location changes
    if (
      userEditedConcreteRequestData.LocationId !== existsConcreteRequestData.LocationId &&
      updatedConcreteRequest?.location?.locationPath
    ) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Location, ${updatedConcreteRequest.location.locationPath} `;
      ConcreteRequestHistory.createInstance(historyObject);
    }
  },

  _updatePlacementTimes(
    userEditedConcreteRequestData,
    existsConcreteRequestData,
    historyObject,
    loginUser
  ) {
    // Check for placement start time changes
    if (
      new Date(userEditedConcreteRequestData.concretePlacementStart).getTime() !==
      new Date(existsConcreteRequestData.concretePlacementStart).getTime()
    ) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Concrete Placement Start Time ${userEditedConcreteRequestData.concretePlacementStart}`;
      ConcreteRequestHistory.createInstance(historyObject);
    }

    // Check for placement end time changes
    if (
      new Date(userEditedConcreteRequestData.concretePlacementEnd).getTime() !==
      new Date(existsConcreteRequestData.concretePlacementEnd).getTime()
    ) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Concrete Placement End Time ${userEditedConcreteRequestData.concretePlacementEnd}`;
      ConcreteRequestHistory.createInstance(historyObject);
    }
  },

  _updateNotesAndMinutes(
    userEditedConcreteRequestData,
    existsConcreteRequestData,
    historyObject,
    loginUser
  ) {
    // Check for additional notes changes
    if (userEditedConcreteRequestData.additionalNotes) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Notes ${userEditedConcreteRequestData.additionalNotes}`;
      ConcreteRequestHistory.createInstance(historyObject);
    }

    if (!userEditedConcreteRequestData.additionalNotes) {
      if (existsConcreteRequestData.additionalNotes) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Removed the Notes ${existsConcreteRequestData.additionalNotes}`;
        ConcreteRequestHistory.createInstance(historyObject);
      }
    }

    // Check for minutes to complete placement changes
    if (
      userEditedConcreteRequestData.minutesToCompletePlacement !==
      existsConcreteRequestData.minutesToCompletePlacement
    ) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Minutes to complete placement ${userEditedConcreteRequestData.minutesToCompletePlacement}`;
      ConcreteRequestHistory.createInstance(historyObject);
    }
  },


  async getSearchConcreteData(params, done) {
    const { incomeData, deliveryList, result, limit, index, count, memberDetails } = params;
    const elementValue = deliveryList[index];
    if (!elementValue) return done(result, false);

    const element = JSON.parse(JSON.stringify(elementValue));

    const status = await this.evaluateConditions(element.id, incomeData, memberDetails);
    if (status.memberCondition && status.companyCondition) {
      result.push(element);
    }

    if (index < deliveryList.length - 1) {
      return this.getSearchConcreteData(
        { incomeData, deliveryList, result, limit, index: index + 1, count: count + 1, memberDetails },
        (response, err) => done(!err ? response : null, err || false)
      );
    }

    done(result, false);
  },

  async evaluateConditions(concreteRequestId, incomeData, memberDetails) {
    const status = { companyCondition: true, memberCondition: true };

    if (incomeData.companyFilter > 0) {
      const companyData = await ConcreteRequestCompany.findOne({
        where: {
          ConcreteRequestId: concreteRequestId,
          CompanyId: incomeData.companyFilter,
          isDeleted: false,
        },
      });
      if (!companyData) status.companyCondition = false;
    }

    if ([3, 4].includes(memberDetails.RoleId)) {
      const memberData = await ConcreteRequestResponsiblePerson.findOne({
        where: {
          ConcreteRequestId: concreteRequestId,
          MemberId: memberDetails.id,
          isDeleted: false,
          isActive: true,
        },
      });
      if (!memberData) status.memberCondition = false;
    }

    return status;
  },

  async getSearchDeliveryData(params, done) {
    const {
      incomeData,
      deliveryList,
      result,
      limit,
      index,
      count,
      memberDetails
    } = params;

    const elementValue = deliveryList[index];
    if (!elementValue) return done(result, false);

    const element = JSON.parse(JSON.stringify(elementValue));
    const status = await this.checkConditions(element, incomeData, memberDetails);

    if (status.companyCondition && status.memberCondition) {
      result.push(element);
    }

    if (index < deliveryList.length - 1) {
      return this.getSearchDeliveryData(
        {
          incomeData,
          deliveryList,
          result,
          limit,
          index: index + 1,
          count: count + 1,
          memberDetails
        },
        (response, err) => (err ? done(null, err) : done(response, false))
      );
    }

    done(result, false);
  },

  async checkConditions(element, incomeData, memberDetails) {
    const status = { companyCondition: true, memberCondition: true };

    if (incomeData.companyFilter > 0) {
      status.companyCondition = await this.checkCompanyCondition(element.id, incomeData.companyFilter);
    }

    if ([3, 4].includes(memberDetails.RoleId)) {
      status.memberCondition = await this.checkMemberCondition(element.id, memberDetails.id);
    }

    return status;
  },

  async checkCompanyCondition(deliveryId, companyFilter) {
    const data = await DeliverCompany.findOne({
      where: { DeliveryId: deliveryId, CompanyId: +companyFilter, isDeleted: false }
    });
    return !!data;
  },

  async checkMemberCondition(deliveryId, memberId) {
    const data = await DeliveryPerson.findOne({
      where: { DeliveryId: deliveryId, MemberId: memberId, isDeleted: false, isActive: true }
    });
    return !!data;
  },


  async upcomingRequestList(req) {
    try {
      req.body.ParentCompanyId = req.query.ParentCompanyId;
      await this.getDynamicModel(req);
      const data = req.query;
      const loginUser = req.user;
      const memberDetails = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: data.ProjectId,
          isDeleted: false,
          isActive: true,
        }),
      });
      if (memberDetails) {
        const concreteCondition = {
          isDeleted: false,
        };
        const craneCondition = {
          isDeleted: false,
        };
        const deliveryCondition = {
          isDeleted: false,
        };
        const inspectionCondition = {
          isDeleted: false,
        };
        const voidConcrete = [];
        const voidCrane = [];
        const voidDelivery = [];
        const voidInspection = [];
        const voidConcreteRequestList = await VoidList.findAll({
          where: {
            ProjectId: +data.ProjectId,
            isDeliveryRequest: false,
            ConcreteRequestId: { [Op.ne]: null },
          },
        });
        voidConcreteRequestList.forEach(async (element) => {
          voidConcrete.push(element.ConcreteRequestId);
        });
        concreteCondition['$ConcreteRequest.id$'] = {
          [Op.and]: [{ [Op.notIn]: voidConcrete }],
        };
        const voidCraneList = await VoidList.findAll({
          where: {
            ProjectId: +data.ProjectId,
            isDeliveryRequest: false,
            CraneRequestId: { [Op.ne]: null },
          },
        });
        voidCraneList.forEach(async (element) => {
          voidCrane.push(element.CraneRequestId);
        });
        craneCondition['$CraneRequest.id$'] = {
          [Op.and]: [{ [Op.notIn]: voidCrane }],
        };
        const voidDeliveryRequestList = await VoidList.findAll({
          where: {
            ProjectId: +data.ProjectId,
            isDeliveryRequest: true,
            DeliveryRequestId: { [Op.ne]: null },
          },
        });
        voidDeliveryRequestList.forEach(async (element) => {
          voidDelivery.push(element.DeliveryRequestId);
        });
        deliveryCondition['$DeliveryRequest.id$'] = {
          [Op.and]: [{ [Op.notIn]: voidDelivery }],
        };
        const voidInspectionRequestList = await VoidList.findAll({
          where: {
            ProjectId: +data.ProjectId,
            isDeliveryRequest: true,
            InspectionRequestId: { [Op.ne]: null },
          },
        });
        voidInspectionRequestList.forEach(async (element) => {
          voidInspection.push(element.InspectionRequestId);
        });
        inspectionCondition['$InspectionRequest.id$'] = {
          [Op.and]: [{ [Op.notIn]: voidInspection }],
        };
        const concreteRequestList = await ConcreteRequest.upcomingConcreteRequest(
          concreteCondition,
          data.ProjectId,
        );
        const craneRequestList = await CraneRequest.upcomingCraneRequest(
          craneCondition,
          data.ProjectId,
        );
        if (craneRequestList && craneRequestList.length > 0) {
          concreteRequestList.push(...craneRequestList);
        }
        const deliveryRequestList = await DeliveryRequest.findAll({
          subQuery: false,
          distinct: true,
          required: false,
          include: [
            {
              association: 'memberDetails',
              where: { isDeleted: false, isActive: true },
              attributes: ['id'],
              include: [
                {
                  association: 'Member',
                  attributes: ['id', 'UserId'],
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'email', 'firstName', 'lastName'],
                    },
                  ],
                },
              ],
            },
            {
              association: 'companyDetails',
              required: false,
              where: { isDeleted: false },
              attributes: ['id'],
              include: [
                {
                  association: 'Company',
                  attributes: ['companyName', 'id'],
                },
              ],
            },
          ],
          where: {
            ProjectId: +data.ProjectId,
            isDeleted: false,
            deliveryStart: {
              [Op.gt]: new Date(),
            },
            isQueued: false,
            ...deliveryCondition,
          },
        });
        if (deliveryRequestList.length > 0) {
          concreteRequestList.push(...deliveryRequestList);
        }
        const inspectionRequestList = await InspectionRequest.findAll({
          subQuery: false,
          distinct: true,
          required: false,
          include: [
            {
              association: 'memberDetails',
              where: { isDeleted: false, isActive: true },
              attributes: ['id'],
              include: [
                {
                  association: 'Member',
                  attributes: ['id', 'UserId'],
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'email', 'firstName', 'lastName'],
                    },
                  ],
                },
              ],
            },
            {
              association: 'companyDetails',
              required: false,
              where: { isDeleted: false },
              attributes: ['id'],
              include: [
                {
                  association: 'Company',
                  attributes: ['companyName', 'id'],
                },
              ],
            },
          ],
          where: {
            ProjectId: +data.ProjectId,
            isDeleted: false,
            inspectionStart: {
              [Op.gt]: new Date(),
            },
            isQueued: false,
            ...inspectionCondition,
          },
        });
        if (inspectionRequestList.length > 0) {
          concreteRequestList.push(...inspectionRequestList);
        }
        let newArray = [];
        concreteRequestList.map(async (element) => {
          let object1 = {};
          object1 = element;
          if (
            object1.requestType === 'deliveryRequest' ||
            object1.requestType === 'deliveryRequestWithCrane'
          ) {
            object1.newDate = element.deliveryStart;
          }
          if (object1.requestType === 'craneRequest') {
            object1.newDate = element.craneDeliveryStart;
          }
          if (object1.requestType === 'concreteRequest') {
            object1.newDate = element.concretePlacementStart;
          }
          if (
            object1.requestType === 'inspectionRequest' ||
            object1.requestType === 'inspectionRequestWithCrane'
          ) {
            object1.newDate = element.inspectionStart;
          }
          newArray.push(object1);
        });
        if (newArray.length > 0) {
          newArray.sort((a, b) => {
            return new Date(a.newDate) - new Date(b.newDate);
          });
        }
        if (data.limit) {
          newArray = newArray.slice(0, +data.limit);
        } else {
          newArray = newArray.slice(0, 10);
        }
        return { status: 200, data: newArray };
      }
      return { status: 422, msg: 'Project Id/ParendCompany Id/Member does not exist' };
    } catch (e) {
      console.log(e);
      return e;
    }
  },
  async getMemberDetailData(data, memberLocationPreference) {
    const emailArray = [];
    const existAdminData = [];
    if (data.memberData !== undefined) {
      data.memberData.forEach((element) => {
        const index = existAdminData.findIndex(
          (adminNew) => adminNew.email === element.Member.User.email,
        );
        if (index === -1) {
          existAdminData.push({ email: element.Member.User.email });
          emailArray.push({
            email: element.Member.User.email,
            firstName: element.Member.User.firstName,
            lastName: element.Member.User.lastName,
            UserId: element.Member.User.id,
            MemberId: element.Member.id,
          });
        }
      });
    }
    if (data.adminData !== undefined) {
      data.adminData.forEach((element) => {
        const index = existAdminData.findIndex((adminNew) => adminNew.email === element.User.email);
        if (index === -1) {
          existAdminData.push({ email: element.User.email });
          emailArray.push({
            email: element.User.email,
            firstName: element.User.firstName,
            lastName: element.User.lastName,
            UserId: element.User.id,
            MemberId: element.id,
          });
        }
      });
    }
    if (memberLocationPreference !== undefined && memberLocationPreference.length > 0) {
      memberLocationPreference.forEach((element) => {
        const index = existAdminData.findIndex(
          (adminNew) => adminNew.email === element.Member.User.email,
        );
        if (index === -1) {
          existAdminData.push({ email: element.Member.User.email });
          emailArray.push({
            email: element.Member.User.email,
            firstName: element.Member.User.firstName,
            lastName: element.Member.User.lastName,
            UserId: element.Member.User.id,
            MemberId: element.Member.id,
            RoleId: element.Member.RoleId,
          });
        }
      });
    }
    return emailArray;
  },
  // prettier-ignore
  async createDailyDigestData(
    params
  ) {
    const {
      MemberId,
      ProjectId,
      ParentCompanyId,
      loginUser,
      dailyDigestMessage,
      requestType,
      messages,
      requestId } = params;
    const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
    const encryptedRequestId = cryptr.encrypt(requestId);
    const encryptedMemberId = cryptr.encrypt(MemberId);
    let imageUrl;
    let link;
    let height;
    if (requestType === 'Delivery Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/87758081-8ccd-4e4d-a4eb-6257466876da.png';
      link = 'delivery-request';
      height = 'height:18px;';
    }
    if (requestType === 'Crane Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/ab400721-520c-4f6f-941c-ac07acc28809.png';
      link = 'crane-request';
      height = 'height:32px;';
    }
    if (requestType === 'Concrete Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/c43e4c58-4c4d-44ff-b78c-fe9948ea53eb.png';
      link = 'concrete-request';
      height = 'height:18px;';
    }
    const object = {
      description: `
      <div>
      <ul style="list-style-type:none;padding:0px;border-bottom:1px dashed #E3E3E3;">
        <li style="display:flex;">
          <img src="${imageUrl}" alt="message-icon" style="${height}">
            <p style="margin:0px;font-size:12px;padding-left:10px;">
              <a href="#" target="" style="text-decoration: none;color:#4470FF;">
               ${loginUser.firstName}  ${loginUser.lastName}
              </a>
              ${dailyDigestMessage}
              <a href="${process.env.BASE_URL}/${link}?requestId=${encryptedRequestId}&memberId=${encryptedMemberId}" style="text-decoration: none;color:#4470FF;" >${messages}</a>
              <span style="color:#707070;">on ${moment().utc().format('MMMM DD')} at ${moment()
          .utc()
          .format('hh:mm A zz')}</span>
            </p>
                
              </li>
      </ul>
</div> `,
      MemberId,
      ProjectId,
      isSent: false,
      isDeleted: false,
      ParentCompanyId,
    };
    await DigestNotification.create(object);
  },
  async getConcreteDropdownDetail(req) {
    const { ProjectId } = req.query;
    const locationDetailsDropdown = await ConcreteLocation.findAll({
      where: { ProjectId, isDeleted: false },
    });
    const mixDesignDropdown = await ConcreteMixDesign.findAll({
      where: { ProjectId, isDeleted: false },
    });
    const pumpSizeDropdown = await ConcretePumpSize.findAll({
      where: { ProjectId, isDeleted: false },
    });
    await this.getDynamicModel(req);
    const parentCompany = await Company.findOne({
      required: false,
      subQuery: false,
      attributes: [
        'id',
        'companyName',
        'website',
        'address',
        'secondAddress',
        'country',
        'city',
        'companyAutoId',
        'state',
        'zipCode',
        'scope',
        'logo',
      ],
      where: { isParent: true, ParentCompanyId: +req.query.ParentCompanyId, isDeleted: false },
    });
    const companyList = await Company.getAllCompany({
      ProjectId,
      isDeleted: false,
      isParent: { [Op.not]: true },
    });
    const newCompanyList = [];
    await companyList.rows.forEach((element) => {
      newCompanyList.push({
        id: element.id,
        companyName: element.companyName,
      });
    });
    if (parentCompany) {
      const index = newCompanyList.findIndex(
        (item) =>
          item.id === parentCompany.id ||
          item.companyName.toLowerCase() === parentCompany.companyName.toLowerCase(),
      );
      if (index === -1) {
        newCompanyList.push({
          id: parentCompany.id,
          companyName: parentCompany.companyName,
        });
      }
    }
    newCompanyList.sort((a, b) =>
      a.companyName.toLowerCase() > b.companyName.toLowerCase() ? 1 : -1,
    );
    const condition = {
      ProjectId,
      isDeleted: false,
      isActive: true,
    };
    const locationDropdown = await Locations.getLocations(condition);
    const locations = locationDropdown.map((location) => {
      const locationData = location.toJSON();
      locationData.gateDetails = locationData.GateId;
      delete locationData.GateId;
      return locationData;
    });

    let data;
    let lastData = {};
    lastData = await ConcreteRequest.findOne({
      where: { ProjectId, isDeleted: false },
      order: [['ConcreteRequestId', 'DESC']],
    });
    if (lastData) {
      data = lastData.ConcreteRequestId + 1;
    } else {
      data = 1;
    }
    const dropdownData = {
      locationDetailsDropdown,
      locationDropdown: locations,
      concreteSupplierDropdown: newCompanyList,
      mixDesignDropdown,
      pumpSizeDropdown,
      ConcreteRequestId: data,
    };
    return { status: 200, data: dropdownData };
  },
  async deleteConcreteRequest(req, done) {
    try {
      await this.getDynamicModel(req);
      const deleteValue = await ConcreteRequest.update(
        {
          isDeleted: true,
        },
        {
          where: {
            id: req.body.id,
            ProjectId: req.body.ProjectId,
          },
        },
      );
      done(deleteValue, false);
    } catch (e) {
      done(null, e);
    }
  },
  async sendEmailNotificationToUser(
    history,
    memberDetails,
    loginUser,
    newDeliverData,
    deliveryData,
    memberLocationPreference,
  ) {
    const userEmails = await this.getMemberDetailData(history, memberLocationPreference);
    if (userEmails.length > 0) {
      userEmails.forEach(async (element) => {
        let name;
        if (!element.firstName) {
          name = 'user';
        } else {
          name = `${element.firstName} ${element.lastName} `;
        }
        if (+element.MemberId !== +memberDetails.id) {
          const memberRole = await Role.findOne({
            where: {
              id: memberDetails.RoleId,
              isDeleted: false,
            },
          });
          const time = moment(newDeliverData.concretePlacementStart).format('MM-DD-YYYY');
          const mailPayload = {
            userName: name,
            email: element.email,
            concreteId: newDeliverData.ConcreteRequestId,
            description: newDeliverData.description,
            timestamp: time,
            createdTimestamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
            content: ` ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has created the concrete booking ${newDeliverData.ConcreteRequestId}.Please see below for more details`,
          };
          const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
            where: {
              MemberId: +element.MemberId,
              ProjectId: +deliveryData.ProjectId,
              LocationId: +deliveryData.LocationId,
              isDeleted: false,
              // follow: true,
            },
          });
          if (isMemberFollowLocation) {
            const memberNotification = await NotificationPreference.findOne({
              where: {
                MemberId: +element.MemberId,
                ProjectId: +deliveryData.ProjectId,
                isDeleted: false,
              },
              include: [
                {
                  association: 'NotificationPreferenceItem',
                  where: {
                    id: 12,
                    isDeleted: false,
                  },
                },
              ],
            });
            if (memberNotification?.instant) {
              await MAILER.sendMail(
                mailPayload,
                'concreteRequestCreated',
                `Concrete Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} `,
                `Concrete Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} `,
                async (info, err) => {
                  console.log(info, err);
                },
              );
            }
            if (memberNotification?.dailyDigest) {
              await this.createDailyDigestData({
                RoleId: +memberDetails.RoleId,
                MemberId: +element.MemberId,
                ProjectId: +deliveryData.ProjectId,
                ParentCompanyId: +deliveryData.ParentCompanyId,
                loginUser,
                dailyDigestMessage: 'created a',
                requestType: 'Concrete Request',
                messages: `concrete Booking (${newDeliverData.ConcreteRequestId} - ${newDeliverData.description})`,
                requestId: newDeliverData.ConcreteRequestId,
              });
            }
          }
        }
      });
    }
    return true;
  },
  async editMultipleDeliveryRequest(req) {
    try {
      const { concreteRequestIds } = req.body;
      if (!concreteRequestIds?.length) {
        return { message: 'Please select Delivery booking to update.!' };
      }

      await this.getDynamicModel(req);
      const loginUser = req.user;

      for (const concreteRequestId of concreteRequestIds) {
        await this.updateSingleConcreteRequest(concreteRequestId, req.body, loginUser);
      }

      return { message: 'Success.!' };
    } catch (e) {
      return e;
    }
  },

  async updateSingleConcreteRequest(concreteRequestId, payload, loginUser) {
    const requestDetail = await this.getConcreteRequestDetail(concreteRequestId);
    if (!requestDetail) return;

    const condition = this.buildCondition(payload.ProjectId, concreteRequestId);
    const updateParam = this.buildUpdateParam(requestDetail);
    const memberData = await Member.getBy({ UserId: loginUser.id, ProjectId: payload.ProjectId });
    const history = this.buildHistory(concreteRequestId, memberData, loginUser);

    if (payload.companies?.length) {
      await this.updateCompanies(payload.companies, condition, updateParam, history, loginUser);
    }
    if (payload.persons?.length) {
      await this.updatePersons(payload.persons, condition, updateParam, history, loginUser);
    }
    if (payload.deliveryStart && payload.deliveryEnd) {
      await this.updateDeliveryDates(payload, concreteRequestId);
    }

    await this.updateConfirmations(payload, concreteRequestId);
    await this.handleVoid(payload, concreteRequestId, memberData, loginUser);

    if (payload.location) {
      await this.updateLocations(payload.location, condition, updateParam, payload, loginUser);
    }

    await this.updateStatusIfNeeded(payload, memberData, requestDetail, concreteRequestId);
  },

  async getConcreteRequestDetail(id) {
    return ConcreteRequest.findOne({ where: [Sequelize.and({ id })] });
  },

  buildCondition(ProjectId, concreteRequestId) {
    return Sequelize.and({ ProjectId, ConcreteRequestId: concreteRequestId });
  },

  buildUpdateParam(requestDetail) {
    return {
      ConcreteRequestId: requestDetail.id,
      ConcreteRequestCode: requestDetail.ConcreteRequestId,
      ProjectId: requestDetail.ProjectId,
      isDeleted: false,
    };
  },

  buildHistory(concreteRequestId, memberData, loginUser) {
    return {
      ConcreteRequestId: concreteRequestId,
      MemberId: memberData.id,
      type: 'edit',
      description: `${loginUser.firstName} ${loginUser.lastName} Edited this Concrete Booking.`,
    };
  },

  async updateCompanies(companies, condition, updateParam, history, loginUser) {
    const existing = await ConcreteRequestCompany.findAll({ where: condition });
    const deleted = existing.filter(e => !companies.includes(e.CompanyId) && !e.isDeleted);
    await ConcreteRequestCompany.update({ isDeleted: true }, { where: condition });

    const added = await Promise.all(companies.map(async (companyId) => {
      const index = existing.findIndex(item => item.CompanyId === companyId);
      const companyParam = { ...updateParam, CompanyId: companyId };

      if (index !== -1) {
        await ConcreteRequestCompany.update(companyParam, { where: { id: existing[index].id } });
        return existing[index].isDeleted ? existing[index] : null;
      } else {
        return ConcreteRequestCompany.createInstance(companyParam);
      }
    }));

    this.updateCompanyHistory(added.filter(Boolean), deleted, history, loginUser);
  },

  async updatePersons(persons, condition, updateParam, history, loginUser) {
    const existing = await ConcreteRequestResponsiblePerson.findAll({ where: condition });
    const deleted = existing.filter(e => !persons.includes(e.MemberId) && !e.isDeleted);
    await ConcreteRequestResponsiblePerson.update({ isDeleted: true }, { where: condition });

    const added = await Promise.all(persons.map(async (memberId) => {
      const index = existing.findIndex(item => item.MemberId === memberId);
      const memberParam = { ...updateParam, MemberId: memberId };

      if (index !== -1) {
        await ConcreteRequestResponsiblePerson.update(memberParam, { where: { id: existing[index].id } });
        return existing[index].isDeleted ? existing[index] : null;
      } else {
        return ConcreteRequestResponsiblePerson.createInstance(memberParam);
      }
    }));

    this.updatePersonHistory(added.filter(Boolean), deleted, history, loginUser);
  },

  async updateDeliveryDates(payload, concreteRequestId) {
    const start = new Date(payload.deliveryStart).getTime();
    const end = new Date(payload.deliveryEnd).getTime();
    const now = Date.now();

    if (start > now && end > now) {
      const params = {
        status: payload.status || 'Pending',
        concretePlacementStart: payload.deliveryStart,
        concretePlacementEnd: payload.deliveryEnd,
      };
      await ConcreteRequest.update(params, { where: { id: concreteRequestId } });
    }
  },

  async updateConfirmations(payload, concreteRequestId) {
    const updates = ['isConcreteConfirmed', 'isPumpConfirmed'];
    for (const key of updates) {
      if (payload[key]) {
        const params = {
          status: payload.status || 'Pending',
          [key]: payload[key],
          [`${key.replace('is', '').toLowerCase()}On`]: payload[`${key.replace('is', '').toLowerCase()}On`],
        };
        await ConcreteRequest.update(params, { where: { id: concreteRequestId } });
      }
    }
  },

  async handleVoid(payload, concreteRequestId, memberData, loginUser) {
    if (!payload.void) return;

    const existVoid = await VoidList.findOne({ where: Sequelize.and({ ConcreteRequestId: concreteRequestId }) });
    if (!existVoid) {
      const voidData = {
        ConcreteRequestId: concreteRequestId,
        ProjectId: payload.ProjectId,
        ParentCompanyId: payload.ParentCompanyId,
      };
      await VoidList.createInstance(voidData);
      await voidService.concreteRequestVoidHistory(existVoid, memberData, loginUser);
    }
  },

  async updateLocations(locations, condition, updateParam, payload, loginUser) {
    const existing = await ConcreteRequestLocation.findAll({ where: condition });

    for (const element of locations) {
      const index = existing.findIndex(item => item.ConcreteLocationId === element.id);
      const locationParam = { ...updateParam };

      if (index !== -1) {
        await ConcreteRequestLocation.update(locationParam, { where: { id: existing[index].id } });
      } else {
        locationParam.ConcreteLocationId = element.chosenFromDropdown
          ? element.id
          : (await ConcreteLocation.createConcreteLocation({
            location: element.location,
            ProjectId: payload.ProjectId,
            isDeleted: false,
            createdBy: loginUser.id,
          })).id;
        await ConcreteRequestLocation.createInstance(locationParam);
      }
    }
  },

  async updateStatusIfNeeded(payload, memberData, requestDetail, concreteRequestId) {
    if (payload.status &&
      (memberData.RoleId === 2 || memberData.RoleId === 1) &&
      requestDetail.status === 'Approved') {
      await ConcreteRequest.update({
        status: payload.status || 'Approved',
        approvedBy: memberData.id,
        approved_at: new Date(),
      }, { where: { id: concreteRequestId } });
    }
  },

  async updateCompanyHistory(addedCompany, deletedCompany, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Company`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Company`;
    addedCompany.forEach(async (element, i) => {
      const newCompanyData = await Company.findOne({
        where: { id: element.CompanyId },
      });
      if (i === 0) {
        if (i === addedCompany.length - 1) {
          addDesc += ` ${newCompanyData.companyName}`;
          newHistory.description = addDesc;
          ConcreteRequestHistory.createInstance(newHistory);
        } else {
          addDesc += ` ${newCompanyData.companyName}`;
        }
      } else if (i === addedCompany.length - 1) {
        addDesc += `,${newCompanyData.companyName}`;
        newHistory.description = addDesc;
        ConcreteRequestHistory.createInstance(newHistory);
      } else {
        addDesc += `,${newCompanyData.companyName}`;
      }
    });
    deletedCompany.forEach(async (element, i) => {
      const newCompanyData = await Company.findOne({
        where: { id: element.CompanyId },
      });
      if (i === 0) {
        if (i === deletedCompany.length - 1) {
          deleteDesc += ` ${newCompanyData.companyName}`;
          newHistory.description = deleteDesc;
          ConcreteRequestHistory.createInstance(newHistory);
        } else {
          deleteDesc += ` ${newCompanyData.companyName}`;
        }
      } else if (i === deletedCompany.length - 1) {
        deleteDesc += `,${newCompanyData.companyName}`;
        newHistory.description = deleteDesc;
        ConcreteRequestHistory.createInstance(newHistory);
      } else {
        deleteDesc += `,${newCompanyData.companyName}`;
      }
    });
  },
  async updatePersonHistory(addedPerson, deletedPerson, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Member`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Member`;
    addedPerson.forEach(async (element, i) => {
      const newMemberData = await Member.findOne({
        where: { id: element.MemberId, isDeleted: false },
        include: [
          {
            association: 'User',
            attributes: ['firstName', 'lastName'],
          },
        ],
      });
      if (i === 0) {
        if (i === addedPerson.length - 1) {
          if (newMemberData.User.firstName) {
            addDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
          }
          newHistory.description = addDesc;
          ConcreteRequestHistory.createInstance(newHistory);
        } else if (newMemberData.User.firstName) {
          addDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
      } else if (i === addedPerson.length - 1) {
        if (newMemberData.User.firstName) {
          addDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
        newHistory.description = addDesc;
        ConcreteRequestHistory.createInstance(newHistory);
      } else if (newMemberData.User.firstName) {
        addDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
      }
    });
    deletedPerson.forEach(async (element, i) => {
      const newMemberData = await Member.findOne({
        where: { id: element.MemberId, isDeleted: false },
        include: [
          {
            association: 'User',
            attributes: ['firstName', 'lastName'],
          },
        ],
      });
      if (i === 0) {
        if (i === deletedPerson.length - 1) {
          if (newMemberData.User.firstName) {
            deleteDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
          }
          newHistory.description = deleteDesc;
          ConcreteRequestHistory.createInstance(newHistory);
        } else if (newMemberData.User.firstName) {
          deleteDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
      } else if (i === deletedPerson.length - 1) {
        if (newMemberData.User.firstName) {
          deleteDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
        newHistory.description = deleteDesc;
        ConcreteRequestHistory.createInstance(newHistory);
      } else if (newMemberData.User.firstName) {
        deleteDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
      }
    });
  },
  // === Helper: Validate Booking Window Rules ===
  async validateBookingWindow(payload, dates, dataInSeries, timezone, settings, roleId) {
    if (!payload.recurrence || !dates?.length) return;

    const compare = (date, time) =>
      this.compareDeliveryDateWithDeliveryWindowDate(date, time, timezone, settings.deliveryWindowTime, settings.deliveryWindowTimeUnit);

    const [startDate, endDate, pumpStartDate, pumpEndDate] = await Promise.all([
      compare(dates[0], payload.deliveryStartTime),
      compare(dates[0], payload.deliveryEndTime),
      compare(dataInSeries.pumpOrderedDate, dataInSeries.pumpWorkStart),
      compare(dataInSeries.pumpOrderedDate, dataInSeries.pumpWorkEnd),
    ]);

    if (+roleId === 4 && (startDate || endDate || pumpStartDate || pumpEndDate)) {
      throw new Error(`Bookings cannot be submitted within ${settings.deliveryWindowTime} ${settings.deliveryWindowTimeUnit} prior to the event`);
    }
  },

  // === Helper: Generate next ConcreteRequestId ===
  async generateNextRequestId(ProjectId) {
    const last = await ConcreteRequest.findOne({
      where: { ProjectId: +ProjectId, isDeleted: false },
      order: [['ConcreteRequestId', 'DESC']],
    });
    return last ? last.ConcreteRequestId + 1 : 1;
  },

  // === Helper: Check if the request should be auto-approved ===
  shouldAutoApproveData(member, projectSettings, roleDetails, accountRoleDetails) {
    return (
      member.RoleId === roleDetails.id ||
      member.RoleId === accountRoleDetails.id ||
      member.isAutoApproveEnabled ||
      projectSettings.isAutoApprovalEnabled
    );
  },

  // === Helper: Build Concrete Request Payload ===
  async buildConcreteRequest(data, id, payload, memberId, recurrenceId, timezone) {
    const format = (dt) => moment(dt).format('MM/DD/YYYY');

    const base = {
      description: payload.description,
      ProjectId: payload.ProjectId,
      notes: payload.notes,
      isEscortNeeded: payload.isEscortNeeded,
      additionalNotes: payload.additionalNotes,
      CraneRequestId: id,
      concretePlacementStart: await this.convertTimezoneToUtc(format(data), timezone, payload.deliveryStartTime),
      concretePlacementEnd: await this.convertTimezoneToUtc(format(data), timezone, payload.deliveryEndTime),
      isPumpConfirmed: payload.isPumpConfirmed,
      isPumpRequired: payload.isPumpRequired,
      isConcreteConfirmed: payload.isConcreteConfirmed,
      ParentCompanyId: payload.ParentCompanyId,
      concreteOrderNumber: payload.concreteOrderNumber,
      truckSpacingHours: payload.truckSpacingHours,
      slump: payload.slump,
      concreteQuantityOrdered: payload.concreteQuantityOrdered,
      concreteConfirmedOn: payload.concreteConfirmedOn || null,
      pumpLocation: payload.pumpLocation,
      pumpOrderedDate: payload.pumpOrderedDate && payload.pumpWorkStart ? payload.pumpOrderedDate : null,
      pumpWorkStart: payload.pumpOrderedDate && payload.pumpWorkStart ? payload.pumpWorkStart : null,
      pumpWorkEnd: payload.pumpOrderedDate && payload.pumpWorkEnd ? payload.pumpWorkEnd : null,
      pumpConfirmedOn: payload.pumpConfirmedOn || null,
      cubicYardsTotal: payload.cubicYardsTotal,
      hoursToCompletePlacement: payload.hoursToCompletePlacement,
      minutesToCompletePlacement: payload.minutesToCompletePlacement,
      ConcreteRequestId: id,
      requestType: 'concreteRequest',
      status: 'Tentative',
      primerForPump: payload.primerForPump,
      createdBy: memberId,
      recurrenceId,
    };

    return base;
  },

  // === Helper: Process recurrence based on type (Daily, Weekly, Monthly, Yearly) ===
  async processRecurrenceTypeCopy(type, dates, repeatDetails, buildFn) {
    const events = [];
    let id = await this.generateNextRequestId(repeatDetails.ProjectId);

    if (type === 'Daily') {
      return this.handleDailyRecurrence(dates, repeatDetails, buildFn, id);
    }

    if (type === 'Weekly') {
      return this.handleWeeklyRecurrence(dates, repeatDetails, buildFn, id);
    }

    if (type === 'Monthly' || type === 'Yearly') {
      return this.handleMonthlyOrYearlyRecurrence(type, dates, repeatDetails, buildFn, id);
    }

    return events;
  },

  async handleDailyRecurrence(dates, repeatDetails, buildFn, id) {
    const events = [];
    for (let i = 0; i < dates.length; i += +repeatDetails.repeatEveryCount) {
      events.push(await buildFn(dates[i], ++id));
    }
    return events;
  },

  async handleWeeklyRecurrence(dates, repeatDetails, buildFn, id) {
    const events = [];
    const allDays = Array.from(momentRange.range(
      moment(dates[0]).startOf('week'),
      moment(dates[dates.length - 1]).endOf('week')
    ).by('day'));

    for (let i = 0; i < allDays.length; i += (7 * (repeatDetails.repeatEveryCount || 1))) {
      for (let j = 0; j < 7; j++) {
        const day = allDays[i + j];
        if (!day || !Array.isArray(repeatDetails.days) || !repeatDetails.days.includes(day.format('dddd'))) continue;
        if (day.isBetween(dates[0], dates[dates.length - 1], null, '[]')) {
          events.push(await buildFn(day, ++id));
        }
      }
    }
    return events;
  },

  async handleMonthlyOrYearlyRecurrence(type, dates, repeatDetails, buildFn, id) {
    const events = [];
    const monthIncrement = type === 'Monthly' ? 1 : 12;
    const start = moment(dates[0]);
    const end = moment(dates[dates.length - 1]).endOf('month');

    while (start.isBefore(end)) {
      const month = start.format('YYYY-MM');
      const daysInMonth = moment(month, 'YYYY-MM').daysInMonth();
      const datesInMonth = Array.from({ length: daysInMonth }, (_, j) =>
        moment(month, 'YYYY-MM').startOf('month').add(j, 'days')
      );

      const chosenDay = repeatDetails.chosenDateOfMonth
        ? datesInMonth.find((d) => d.format('DD') === repeatDetails.dateOfMonth)
        : this.getNthWeekday(repeatDetails.monthlyRepeatType, month);

      if (chosenDay?.isBetween(dates[0], dates[dates.length - 1], null, '[]')) {
        events.push(await buildFn(chosenDay, ++id));
      }

      start.add(monthIncrement, 'month');
    }

    return events;
  },


  // === Helper: Get the nth weekday from a month like "second Monday" ===
  getNthWeekday(repeatType, month) {
    const [ordinal, weekday] = repeatType.split(' ');
    const start = moment(month, 'YYYY-MM').startOf('month');
    const matchingDays = [];

    while (start.month() === moment(month, 'YYYY-MM').month()) {
      if (start.format('dddd').toLowerCase() === weekday.toLowerCase()) {
        matchingDays.push(start.clone());
      }
      start.add(1, 'day');
    }

    const map = { first: 0, second: 1, third: 2, fourth: 3, last: matchingDays.length - 1 };
    return matchingDays[map[ordinal.toLowerCase()]];
  },

  // === Main Refactored Function ===
  async createCopyofConcreteRequest(dataInSeries, payload, dates, loginUser, newRecurrenceId) {
    const member = await Member.getBy({ UserId: loginUser.id, ProjectId: payload.ProjectId, isActive: true, isDeleted: false });
    const project = await Project.getProjectAndSettings({ isDeleted: false, id: +payload.ProjectId });

    await this.validateBookingWindow(this, payload, dates, dataInSeries, payload.timezone, project.ProjectSettings, member.RoleId);

    const roleDetails = await Role.getBy('Project Admin');
    const accountRoleDetails = await Role.getBy('Account Admin');

    const eventsArray = await this.processRecurrenceTypeCopy(
      dataInSeries.recurrence.recurrence,
      dates,
      dataInSeries.recurrence,
      async (date, id) => {
        const request = await this.buildConcreteRequest(this, date, id, payload, member.id, newRecurrenceId, payload.timezone);
        if (this.shouldAutoApproveData(member, project.ProjectSettings, roleDetails, accountRoleDetails)) {
          Object.assign(request, { status: 'Approved', approvedBy: member.id, approved_at: new Date() });
        }
        return request;
      }
    );

    for (const request of eventsArray) {
      await this.createConcreteRequestWithAssociations(request, payload, loginUser, member);
    }
  },

  async createConcreteRequestWithAssociations(concreteRequestParam, payload, loginUser, memberDetails) {
    const {
      concreteSupplier,
      location,
      pumpSize,
      mixDesign,
      responsiblePersons,
      ProjectId,
      description,
    } = payload;

    const newConcreteRequestData = await ConcreteRequest.createInstance(concreteRequestParam);

    const updateParam = {
      ConcreteRequestId: newConcreteRequestData.id,
      ConcreteRequestCode: newConcreteRequestData.ConcreteRequestId,
      ProjectId: ProjectId,
    };

    // === Add Companies ===
    for (const companyId of concreteSupplier) {
      await ConcreteRequestCompany.createInstance({ ...updateParam, CompanyId: companyId });
    }

    // === Add Location ===
    const locationData = await ConcreteLocation.createConcreteLocation({
      location,
      ProjectId,
      isDeleted: false,
      createdBy: loginUser.id,
    });

    await ConcreteRequestLocation.createInstance({
      ...updateParam,
      ConcreteLocationId: locationData.id,
    });

    // === Add Pump Sizes ===
    for (const pump of pumpSize) {
      const pumpSizeParam = { ...updateParam };
      if (pump.chosenFromDropdown) {
        pumpSizeParam.ConcretePumpSizeId = pump.id;
      } else {
        const existingPump = await this.findOrCreatePumpSize(pump.pumpSize, ProjectId, loginUser.id);
        pumpSizeParam.ConcretePumpSizeId = existingPump.id;
      }
      await ConcreteRequestPumpSize.createInstance(pumpSizeParam);
    }

    // === Add Mix Designs ===
    for (const mix of mixDesign) {
      const mixDesignParam = { ...updateParam };
      if (mix.chosenFromDropdown) {
        mixDesignParam.ConcreteMixDesignId = mix.id;
      } else {
        const existingMix = await this.findOrCreateMixDesign(mix.mixDesign, ProjectId, loginUser.id);
        mixDesignParam.ConcreteMixDesignId = existingMix.id;
      }
      await ConcreteRequestMixDesign.createInstance(mixDesignParam);
    }

    // === Add Responsible Persons ===
    for (const personId of responsiblePersons) {
      await ConcreteRequestResponsiblePerson.createInstance({ ...updateParam, MemberId: personId });
    }

    // === Add History ===
    await ConcreteRequestHistory.createInstance({
      ConcreteRequestId: newConcreteRequestData.id,
      ProjectId,
      isDeleted: false,
      MemberId: memberDetails.id,
      type: 'create',
      description: `${loginUser.firstName} ${loginUser.lastName} Created Concrete Booking, ${description}.`,
    });

    if (newConcreteRequestData.status === 'Approved') {
      await ConcreteRequestHistory.createInstance({
        ConcreteRequestId: newConcreteRequestData.id,
        ProjectId,
        isDeleted: false,
        MemberId: memberDetails.id,
        type: 'approved',
        description: `${loginUser.firstName} ${loginUser.lastName} Approved Concrete Booking, ${description}.`,
      });
    }
  },

  async findOrCreatePumpSize(pumpSize, ProjectId, createdBy) {
    const existing = await ConcretePumpSize.findAll({ where: { ProjectId, isDeleted: false } });
    const found = existing.find(
      (item) => item.pumpSize.toLowerCase().trim() === pumpSize.toLowerCase().trim()
    );
    if (found) return found;

    return await ConcretePumpSize.createConcretePumpSize({
      pumpSize,
      ProjectId,
      isDeleted: false,
      createdBy,
    });
  },

  async findOrCreateMixDesign(mixDesign, ProjectId, createdBy) {
    const existing = await ConcreteMixDesign.findAll({ where: { ProjectId, isDeleted: false } });
    const found = existing.find(
      (item) => item.mixDesign.toLowerCase().trim() === mixDesign.toLowerCase().trim()
    );
    if (found) return found;

    return await ConcreteMixDesign.createConcreteMixDesign({
      mixDesign,
      ProjectId,
      isDeleted: false,
      createdBy,
    });
  },


  async checkDoubleBookingAllowedOrNot(eventsArray, projectDetails, type) {
    if (
      projectDetails.ProjectSettings &&
      !projectDetails.ProjectSettings.concreteAllowOverlappingBooking
    ) {
      const checkOverlapping = await this.checkConcreteConflictsWithAlreadyScheduled(
        eventsArray,
        type,
      );
      if (checkOverlapping) {
        return {
          error: true,
          message:
            'This booking clashes with another booking. Overlapping is disabled by the administrator.',
        };
      }
    }
    if (
      projectDetails.ProjectSettings &&
      !projectDetails.ProjectSettings.concreteAllowOverlappingCalenderEvents
    ) {
      const checkCalenderOverlap = await this.checkCalenderEventsOverlappingWithBooking(
        eventsArray,
        'concrete',
        type,
      );
      if (checkCalenderOverlap) {
        return {
          error: true,
          message:
            'This booking clashes with a scheduled calendar event. Overlapping is disabled by the administrator',
        };
      }
    }
  },
  async checkConcreteConflictsWithAlreadyScheduled(concreteData, type) {
    const datesStartArr = [];
    const datesEndArr = [];
    const requestIds = [];
    const recurrenceIds = [];
    concreteData.forEach((data) => {
      datesStartArr.push(new Date(data.concretePlacementStart));
      datesEndArr.push(new Date(data.concretePlacementEnd));
      if (type === 'edit') {
        if (data.id) {
          requestIds.push(data.id);
        }
        if (data.recurrenceId) {
          recurrenceIds.push(data.recurrenceId);
        }
      }
    });
    let condition = {
      ProjectId: concreteData[0].ProjectId,
      status: {
        [Op.notIn]: ['Completed', 'Expired'],
      },
      isDeleted: false,
    };
    if (type === 'edit') {
      if (recurrenceIds && recurrenceIds.length > 0) {
        condition = {
          ...condition,
          recurrenceId: {
            [Op.notIn]: recurrenceIds,
          },
        };
      } else {
        condition = {
          ...condition,
          id: {
            [Op.notIn]: requestIds,
          },
        };
      }
    }
    const concreteExist = await ConcreteRequest.findAll({
      where: {
        ...condition,
        [Op.or]: [
          {
            [Op.or]: datesStartArr.map((date) => ({
              concretePlacementStart: { [Op.lt]: date },
              concretePlacementEnd: { [Op.gt]: date },
            })),
          },
          {
            [Op.or]: datesEndArr.map((date) => ({
              concretePlacementStart: { [Op.lt]: date },
              concretePlacementEnd: { [Op.gt]: date },
            })),
          },
        ],
      },
    });
    const overlappingIds = []
    concreteExist.forEach(data => {
      overlappingIds.push(data.id)
    })
    const voidData = await VoidList.findAll({
      where: {
        ConcreteRequestId: {
          [Op.in]: overlappingIds
        }
      }
    })
    const checkOverlappingInVoid = concreteExist.map(data => data.id);
    return checkOverlappingInVoid.every(id =>
      voidData.some(voidEntry => voidEntry.ConcreteRequestId === id)
    );
  },

  async checkCalenderEventsOverlappingWithBooking(requestsArray, booking, type) {
    const { deliveryStartDateArr, deliveryEndDateArr, applicableEvents } = this.extractBookingDates(requestsArray, booking);

    const condition = {
      ProjectId: requestsArray[0].ProjectId,
      ...applicableEvents,
    };

    let getCalendarEvents = await CalendarSetting.getAll(condition);
    getCalendarEvents = JSON.parse(JSON.stringify(getCalendarEvents));

    const eventsArray = await this.generateRecurrenceEvents(getCalendarEvents);
    return this.hasOverlap(eventsArray, deliveryStartDateArr, deliveryEndDateArr);
  },

  // ----------------- HELPERS -----------------

  extractBookingDates(requestsArray, booking) {
    const deliveryStartDateArr = [];
    const deliveryEndDateArr = [];
    const mapping = {
      delivery: ['deliveryStart', 'deliveryEnd', { isApplicableToDelivery: true }],
      crane: ['craneDeliveryStart', 'craneDeliveryEnd', { isApplicableToCrane: true }],
      concrete: ['concretePlacementStart', 'concretePlacementEnd', { isApplicableToConcrete: true }],
      inspection: ['inspectionStart', 'inspectionEnd', { isApplicableToInspection: true }],
    };

    const [startKey, endKey, applicableEvents] = mapping[booking] || [];
    requestsArray.forEach(data => {
      deliveryStartDateArr.push(data[startKey]);
      deliveryEndDateArr.push(data[endKey]);
    });

    return { deliveryStartDateArr, deliveryEndDateArr, applicableEvents };
  },

  async generateRecurrenceEvents(getCalendarEvents) {
    const eventsArray = [];
    if (!getCalendarEvents?.length) return eventsArray;

    let uniqueNumber = 0;
    for (const eventObject of getCalendarEvents) {
      const eventTimeZone = await this.getTimeZone(eventObject.TimeZoneId);
      const recurrenceEvents = await this.handleRecurrence(eventObject, eventTimeZone, uniqueNumber);
      uniqueNumber += recurrenceEvents.length;
      eventsArray.push(...recurrenceEvents);
    }
    return eventsArray;
  },

  async getTimeZone(timeZoneId) {
    return await TimeZone.findOne({
      where: { isDeleted: false, id: +timeZoneId },
      attributes: ['id', 'location', 'isDayLightSavingEnabled', 'timeZoneOffsetInMinutes', 'dayLightSavingTimeInMinutes', 'timezone'],
    });
  },

  async handleRecurrence(eventObject, eventTimeZone, uniqueNumber) {
    switch (eventObject.recurrence) {
      case 'Does Not Repeat':
        return await this.handleDoesNotRepeat(eventObject, eventTimeZone, uniqueNumber);
      case 'Daily':
        return await this.handleDaily(eventObject, eventTimeZone, uniqueNumber);
      case 'Weekly':
        return await this.handleWeeklyRecurrence(eventObject, eventTimeZone, uniqueNumber);
      case 'Monthly':
      case 'Yearly':
        return await this.handleMonthlyOrYearlyRecurrence(eventObject, eventTimeZone, uniqueNumber);
      default:
        return [];
    }
  },

  async handleDoesNotRepeat(eventObject, eventTimeZone, uniqueNumber) {
    const range = momentRange.range(moment(eventObject.fromDate), moment(eventObject.toDate));
    const totalDays = Array.from(range.by('day'));
    const startDate = totalDays[0];
    const endDate = totalDays[totalDays.length - 1];

    return Promise.all(
      totalDays.map((date) =>
        calendarSettingsService.createRecurrenceObject(eventObject, eventTimeZone, date.toDate(), startDate, endDate, ++uniqueNumber, eventObject.recurrence))
    );
  },

  async handleDaily(eventObject, eventTimeZone, uniqueNumber) {
    const range = momentRange.range(moment(eventObject.fromDate), moment(eventObject.toDate));
    const totalDays = Array.from(range.by('day'));
    const events = [];
    const startDate = totalDays[0];
    const endDate = totalDays[totalDays.length - 1];
    for (let i = 0; i < totalDays.length; i += +eventObject.repeatEveryCount) {
      events.push(await calendarSettingsService.createRecurrenceObject(eventObject, eventTimeZone, totalDays[i].toDate(), startDate, endDate, ++uniqueNumber, eventObject.recurrence));
    }
    return events;
  },

  async handleWeekly(eventObject, eventTimeZone, uniqueNumber) {
    const startWeek = moment(eventObject.fromDate).startOf('week');
    const endWeek = moment(eventObject.toDate).endOf('week');
    const range = momentRange.range(startWeek, endWeek);
    const totalDays = Array.from(range.by('day'));
    const startDate = moment(eventObject.fromDate);
    const endDate = moment(eventObject.toDate);
    const events = [];

    const increment = (+eventObject.repeatEveryCount || 1) * 7;
    const daysToInclude = eventObject.days || [];

    for (let i = 0; i < totalDays.length; i += increment) {
      for (let j = 0; j < 7 && (i + j) < totalDays.length; j++) {
        const date = totalDays[i + j];
        if (
          date &&
          date.isBetween(startDate, endDate, null, '[]') &&
          Array.isArray(daysToInclude) && daysToInclude.includes(date.format('dddd'))
        ) {
          events.push(await calendarSettingsService.createRecurrenceObject(eventObject, eventTimeZone, date.toDate(), startDate, endDate, ++uniqueNumber, eventObject.recurrence));
        }
      }
    }
    return events;
  },

  async handleMonthlyOrYearly(eventObject, eventTimeZone, uniqueNumber) {
    const events = [];
    const startDate = moment(eventObject.fromDate);
    const endDate = moment(eventObject.toDate);
    const increment = eventObject.recurrence === 'Monthly' ? 'months' : 'years';
    const repeatCount = +eventObject.repeatEveryCount || 1;

    let current = startDate.clone();
    while (current.isSameOrBefore(endDate)) {
      if (eventObject.chosenDateOfMonth) {
        // Handle specific date of month
        const targetDate = current.clone().date(+eventObject.dateOfMonth);
        if (targetDate.isBetween(startDate, endDate, null, '[]')) {
          events.push(await calendarSettingsService.createRecurrenceObject(eventObject, eventTimeZone, targetDate.toDate(), startDate, endDate, ++uniqueNumber, eventObject.recurrence));
        }
      } else if (eventObject.monthlyRepeatType) {
        // Handle nth weekday of month (e.g., "first Monday")
        const [nth, dayName] = eventObject.monthlyRepeatType.split(' ');
        const targetDate = this.getNthWeekdayOfMonth(current, nth, dayName);
        if (targetDate && targetDate.isBetween(startDate, endDate, null, '[]')) {
          events.push(await calendarSettingsService.createRecurrenceObject(eventObject, eventTimeZone, targetDate.toDate(), startDate, endDate, ++uniqueNumber, eventObject.recurrence));
        }
      }
      current.add(repeatCount, increment);
    }
    return events;
  },

  getNthWeekdayOfMonth(monthMoment, nth, dayName) {
    const month = monthMoment.clone().startOf('month');
    const dayIndex = moment().day(dayName).day(); // Get day index (0=Sunday, 1=Monday, etc.)

    // Find first occurrence of the day in the month
    let firstOccurrence = month.clone().day(dayIndex);
    if (firstOccurrence.date() > 7) {
      firstOccurrence.subtract(7, 'days');
    }

    // Calculate the nth occurrence
    const nthMap = { first: 1, second: 2, third: 3, fourth: 4, last: -1 };
    const nthValue = nthMap[nth.toLowerCase()] || parseInt(nth);

    if (nthValue === -1) {
      // Handle "last" occurrence
      const lastDay = month.clone().endOf('month');
      let lastOccurrence = lastDay.clone().day(dayIndex);
      if (lastOccurrence.isAfter(lastDay)) {
        lastOccurrence.subtract(7, 'days');
      }
      return lastOccurrence.month() === month.month() ? lastOccurrence : null;
    } else {
      const targetDate = firstOccurrence.clone().add((nthValue - 1) * 7, 'days');
      return targetDate.month() === month.month() ? targetDate : null;
    }
  },

  hasOverlap(eventsArray, deliveryStartDateArr, deliveryEndDateArr) {
    if (!eventsArray.length) return false;
    const isOverlap = (element) =>
      eventsArray.some(data => {
        return moment(element).isBetween(moment(data.fromDate), moment(data.toDate), undefined, '[]');
      });
    return deliveryStartDateArr.some(isOverlap) || deliveryEndDateArr.some(isOverlap);
  },

  async insertRecurrenceSeries(data, user, requestType, timezone) {
    const recurrenceObject = {
      ProjectId: data.ProjectId,
      ParentCompanyId: data.ParentCompanyId,
      recurrence: data.recurrence,
      repeatEveryCount: data.repeatEveryCount,
      repeatEveryType: data.repeatEveryType,
      days: data.days,
      dateOfMonth: data.dateOfMonth,
      chosenDateOfMonth: data.chosenDateOfMonth,
      monthlyRepeatType: data.monthlyRepeatType,
      requestType,
      createdBy: user.id,
      chosenDateOfMonthValue: data.chosenDateOfMonthValue,
    };
    if (requestType === 'craneRequest') {
      recurrenceObject.recurrenceStartDate = data.craneDeliveryStart;
      recurrenceObject.recurrenceEndDate = data.craneDeliveryEnd;
    } else if (requestType === 'concreteRequest') {
      recurrenceObject.recurrenceStartDate = data.concretePlacementStart;
      recurrenceObject.recurrenceEndDate = data.concretePlacementEnd;
    } else if (requestType === 'inspectionRequest') {
      recurrenceObject.recurrenceStartDate = data.inspectionStart;
      recurrenceObject.recurrenceEndDate = data.inspectionEnd;
    } else {
      recurrenceObject.recurrenceStartDate = data.deliveryStart;
      recurrenceObject.recurrenceEndDate = data.deliveryEnd;
    }
    recurrenceObject.recurrenceStartDate = await this.convertTimezoneToUtc(
      moment(recurrenceObject.recurrenceStartDate).format('MM/DD/YYYY'),
      timezone,
      '00:00',
    );
    recurrenceObject.recurrenceEndDate = await this.convertTimezoneToUtc(
      moment(recurrenceObject.recurrenceEndDate).format('MM/DD/YYYY'),
      timezone,
      '00:00',
    );
    const recurrenceSeries = await RequestRecurrenceSeries.createInstance(recurrenceObject);
    if (recurrenceSeries?.id) {
      return recurrenceSeries.id;
    }
  },
  async convertTimezoneToUtc(date, timezone, time) {
    const chosenTimezoneDeliveryStart = moment.tz(`${date} ${time}`, 'MM/DD/YYYY HH:mm', timezone);
    const utcDate = chosenTimezoneDeliveryStart.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
    return utcDate;
  },

  async createNewRecurrenceEvents(
    concreteRequestDetail,
    memberDetails,
    totalDays1,
    roleDetails,
    accountRoleDetails,
    projectDetails,
    done
  ) {
    const eventsArray = [];
    const recurrenceType = concreteRequestDetail.recurrence;

    const generatorMap = {
      Daily: this.generateDailyEvents,
      Weekly: this.generateWeeklyEvents,
      Monthly: this.generateYearlyEvents,
      Yearly: this.generateYearlyEvents,
    };

    if (!generatorMap[recurrenceType]) return done(null, { message: 'Invalid recurrence type' });

    const eventDates = await generatorMap[recurrenceType](concreteRequestDetail, totalDays1);
    for (const date of eventDates) {
      const event = this.buildEvent(
        concreteRequestDetail,
        date,
        memberDetails,
        roleDetails,
        accountRoleDetails,
        projectDetails
      );
      eventsArray.push(event);
    }

    if (eventsArray.length) {
      const isOverlapping = await this.checkDoubleBookingAllowedOrNot(eventsArray, projectDetails, 'edit');
      if (isOverlapping?.error) return done(null, { message: isOverlapping.message });
      return done(eventsArray, false);
    }

    return done(null, { message: 'No events generated' });
  },

  buildEvent(detail, date, member, role, accountRole, project) {
    const [placementStart, placementEnd, pumpOrderedDate, pumpStart, pumpEnd] = this.convertToUTCDate(detail, date);

    const param = {
      description: detail.description,
      ProjectId: detail.ProjectId,
      notes: detail.notes,
      concretePlacementStart: placementStart,
      concretePlacementEnd: placementEnd,
      isPumpConfirmed: detail.isPumpConfirmed,
      isPumpRequired: detail.isPumpRequired,
      isConcreteConfirmed: detail.isConcreteConfirmed,
      ParentCompanyId: detail.ParentCompanyId,
      concreteOrderNumber: detail.concreteOrderNumber,
      truckSpacingHours: detail.truckSpacingHours,
      slump: detail.slump,
      concreteQuantityOrdered: detail.concreteQuantityOrdered,
      concreteConfirmedOn: detail.concreteConfirmedOn || null,
      pumpLocation: detail.pumpLocation,
      pumpOrderedDate: detail.pumpOrderedDate ? pumpOrderedDate : null,
      pumpWorkStart: detail.pumpWorkStart ? pumpStart : null,
      pumpWorkEnd: detail.pumpWorkEnd ? pumpEnd : null,
      pumpConfirmedOn: detail.pumpConfirmedOn || null,
      cubicYardsTotal: detail.cubicYardsTotal,
      hoursToCompletePlacement: detail.hoursToCompletePlacement,
      minutesToCompletePlacement: detail.minutesToCompletePlacement,
      ConcreteRequestId: null,
      requestType: 'concreteRequest',
      status: 'Tentative',
      primerForPump: detail.primerForPump,
      createdBy: member.id,
      recurrenceId: detail.recurrenceId,
      LocationId: detail.LocationId,
      OriginationAddress: detail.originationAddress,
      vehicleType: detail.vehicleType,
      OriginationAddressPump: detail.originationAddressPump,
      vehicleTypePump: detail.vehicleTypePump,
    };

    if (
      member.RoleId === role.id ||
      member.RoleId === accountRole.id ||
      member.isAutoApproveEnabled ||
      project.ProjectSettings?.isAutoApprovalEnabled
    ) {
      param.status = 'Approved';
      param.approvedBy = member.id;
      param.approved_at = new Date();
    }

    return param;
  },

  convertToUTCDate(detail, date) {
    const dateStr = moment(date).format('MM/DD/YYYY');
    const getTime = (t) =>
      moment.tz(`${dateStr} ${t}`, 'MM/DD/YYYY HH:mm', detail.timezone).clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');

    const pumpBaseDate = moment(detail.pumpOrderedDate, 'MM/DD/YYYY').format('MM/DD/YYYY');
    const getPumpTime = (t) =>
      moment.tz(`${pumpBaseDate} ${t}`, 'MM/DD/YYYY HH:mm', detail.timezone).clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');

    return [
      getTime(detail.deliveryStartTime),
      getTime(detail.deliveryEndTime),
      getPumpTime(detail.pumpWorkStart),
      getPumpTime(detail.pumpWorkStart),
      getPumpTime(detail.pumpWorkEnd),
    ];
  },

  async generateDailyEvents(detail, totalDays) {
    return totalDays.filter((d) =>
      moment(d).isBetween(
        moment(detail.concretePlacementStart),
        moment(detail.recurrenceEndDate).add(1, 'day'),
        null,
        '[]'
      )
    );
  },

  generateWeeklyEvents(detail) {
    const { repeatEveryCount = 1, days = [] } = detail;
    const startDate = moment(detail.concretePlacementStart).startOf('week');
    const endDate = moment(detail.recurrenceEndDate).endOf('week');
    const allDays = [];

    for (let date = startDate.clone(); date.isSameOrBefore(endDate); date.add(1, 'day')) {
      const isInRange = date.isBetween(
        detail.concretePlacementStart,
        moment(detail.recurrenceEndDate).add(1, 'day'),
        null,
        '[]'
      );
      const isDayIncluded = Array.isArray(days) && days.includes(date.format('dddd'));

      if (isInRange && isDayIncluded) {
        allDays.push(date.clone());
      }
    }

    // Apply repeat interval (e.g., every 2 weeks)
    return allDays.filter((_, i) => i % (7 * repeatEveryCount) === 0);
  },

  generateYearlyEvents(detail) {
    const allDates = [];
    const start = moment(detail.concretePlacementStart).startOf('month');
    const end = moment(detail.recurrenceEndDate).endOf('month');
    const { repeatEveryCount = 1, chosenDateOfMonth, dateOfMonth, monthlyRepeatType } = detail;

    let current = start.clone();

    while (current.isSameOrBefore(end)) {
      const daysInMonth = current.daysInMonth();
      const monthDates = Array.from({ length: daysInMonth }, (_, i) => current.clone().startOf('month').add(i, 'days'));

      if (chosenDateOfMonth && dateOfMonth) {
        const matched = monthDates.find((d) => d.format('DD') === dateOfMonth);
        if (matched?.isBetween(detail.concretePlacementStart, moment(detail.recurrenceEndDate).add(1, 'day'), null, '[]')) {
          allDates.push(matched.clone());
        }
      } else if (monthlyRepeatType) {
        const [which, day] = monthlyRepeatType.split(' ');
        const weeklyMap = { first: 0, second: 1, third: 2, fourth: 3, last: -1 };
        const targetWeek = weeklyMap[which.toLowerCase()];
        const dayNum = moment().day(day).day();

        const weekdayDates = monthDates.filter((d) => d.day() === dayNum);
        const selected = targetWeek === -1 ? weekdayDates[weekdayDates.length - 1] : weekdayDates[targetWeek];

        if (
          selected?.isBetween(detail.concretePlacementStart, moment(detail.recurrenceEndDate).add(1, 'day'), null, '[]')
        ) {
          allDates.push(selected.clone());
        }
      }

      current.add(repeatEveryCount * 12, 'months');
    }

    return allDates;
  },


  async insertEditRecurrenceSeries(data, user, requestType) {
    const recurrenceObject = {
      ProjectId: data.ProjectId,
      ParentCompanyId: data.ParentCompanyId,
      recurrence: data.recurrence,
      repeatEveryCount: data.repeatEveryCount,
      repeatEveryType: data.repeatEveryType,
      days: data.days,
      dateOfMonth: data.dateOfMonth,
      chosenDateOfMonth: data.chosenDateOfMonth,
      monthlyRepeatType: data.monthlyRepeatType,
      requestType,
      createdBy: user.id,
      chosenDateOfMonthValue: data.chosenDateOfMonthValue,
      recurrenceStartDate: data.recurrenceStartDate,
      recurrenceEndDate: data.recurrenceEndDate,
    };
    const recurrenceSeries = await RequestRecurrenceSeries.createInstance(recurrenceObject);
    if (recurrenceSeries?.id) {
      return recurrenceSeries.id;
    }
  },
};
module.exports = concreteRequestService;
