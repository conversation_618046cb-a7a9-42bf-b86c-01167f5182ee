const { Router } = require('express');
const { AccountCornController } = require('../controllers');
const passportConfig = require('../config/passport');
const checkAdmin = require('../middlewares/checkAdmin');

const accountRoute = {
  get router() {
    const router = Router();
    router
      .get(
        '/get_enterprise_accounts',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        AccountCornController.getEnterpriseAccounts,
      )
      .get(
        '/get_non_enterprise_accounts',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        AccountCornController.getNonEnterpriseAccounts,
      )
      .get(
        '/get_non_enterprise_account_projects/:id',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        AccountCornController.getNonEnterpriseAccountProjects,
      );
    return router;
  },
};
module.exports = accountRoute;
