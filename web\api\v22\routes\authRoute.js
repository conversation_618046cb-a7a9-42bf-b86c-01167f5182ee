const { Router } = require('express');
const { validate } = require('express-validation');
const { AuthController } = require('../controllers');
const { userValidation } = require('../middlewares/validations');
// const domainHelper = require('../helpers/domainHelper');

const authRoute = {
  get router() {
    const router = Router();
    router.post(
      '/register',
      validate(userValidation.register, { keyByField: true }, { abortEarly: false }),
      AuthController.register,
    );
    router.post(
      '/login',
      validate(userValidation.login, { keyByField: true }, { abortEarly: false }),
      AuthController.login,
    );
    router.post(
      '/exist_user',
      validate(userValidation.existEmail, { keyByField: true }, { abortEarly: false }),
      AuthController.existEmail,
    );
    router.post(
      '/remove_invited_member',
      validate(userValidation.email, { keyByField: true }, { abortEarly: false }),
      AuthController.removeFromInvitedProject,
    );
    router.post(
      '/request_invited_link',
      validate(userValidation.email, { keyByField: true }, { abortEarly: false }),
      AuthController.getResendInviteLink,
    );
    router.get(
      '/check_reset_token/:resetToken',
      validate(userValidation.checkResetToken, { keyByField: true }, { abortEarly: false }),
      AuthController.checkResetToken,
    );
    router.post(
      '/forgot_password',
      validate(userValidation.forgotPassword, { keyByField: true }, { abortEarly: false }),
      AuthController.forgotPassword,
    );
    router.post(
      '/reset_password_email/:reset_password_token',
      validate(userValidation.resetPasswordEmail, { keyByField: true }, { abortEarly: false }),
      AuthController.resetPasswordByEmail,
    );

    return router;
  },
};

module.exports = authRoute;
