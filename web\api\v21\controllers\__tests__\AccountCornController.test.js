const status = require('http-status');
const AccountCornController = require('../AccountCornController');

// Mock the services
jest.mock('../../services', () => ({
  accountCornService: {
    createSchemas: jest.fn(),
  },
  accountService: {
    getEnterpriseAccounts: jest.fn(),
    getNonEnterpriseAccountProjects: jest.fn(),
  },
}));

const { accountCornService, accountService } = require('../../services');

describe('AccountCornController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();

    // Setup mock request, response, and next function
    mockReq = {
      query: {},
      params: {},
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    mockNext = jest.fn();
  });

  describe('createSchemas', () => {
    it('should call accountCornService.createSchemas successfully', async () => {
      // Arrange
      accountCornService.createSchemas.mockResolvedValue();

      // Act
      await AccountCornController.createSchemas();

      // Assert
      expect(accountCornService.createSchemas).toHaveBeenCalledTimes(1);
    });

    it('should handle errors from accountCornService.createSchemas', async () => {
      // Arrange
      const error = new Error('Database connection failed');
      accountCornService.createSchemas.mockRejectedValue(error);

      // Act & Assert
      await expect(AccountCornController.createSchemas()).rejects.toThrow(
        'Database connection failed',
      );
      expect(accountCornService.createSchemas).toHaveBeenCalledTimes(1);
    });
  });

  describe('getEnterpriseAccounts', () => {
    it('should return enterprise accounts successfully when data exists', async () => {
      // Arrange
      const mockEnterpriseAccounts = {
        count: 2,
        rows: [
          { company: { id: 1, name: 'Company 1' }, projectCount: 3 },
          { company: { id: 2, name: 'Company 2' }, projectCount: 2 },
        ],
      };

      accountService.getEnterpriseAccounts.mockResolvedValue(mockEnterpriseAccounts);

      // Act
      await AccountCornController.getEnterpriseAccounts(mockReq, mockRes, mockNext);

      // Assert
      expect(accountService.getEnterpriseAccounts).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Enterprise Accounts Listed Successfully.',
        data: mockEnterpriseAccounts,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return empty array when no enterprise accounts exist', async () => {
      // Arrange
      accountService.getEnterpriseAccounts.mockResolvedValue(null);

      // Act
      await AccountCornController.getEnterpriseAccounts(mockReq, mockRes, mockNext);

      // Assert
      expect(accountService.getEnterpriseAccounts).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Enterprise Accounts Listed Successfully.',
        data: [],
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return empty array when enterprise accounts is falsy', async () => {
      // Arrange
      accountService.getEnterpriseAccounts.mockResolvedValue(false);

      // Act
      await AccountCornController.getEnterpriseAccounts(mockReq, mockRes, mockNext);

      // Assert
      expect(accountService.getEnterpriseAccounts).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Enterprise Accounts Listed Successfully.',
        data: [],
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle errors and call next with error', async () => {
      // Arrange
      const error = new Error('Service error');
      accountService.getEnterpriseAccounts.mockRejectedValue(error);

      // Act
      await AccountCornController.getEnterpriseAccounts(mockReq, mockRes, mockNext);

      // Assert
      expect(accountService.getEnterpriseAccounts).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getNonEnterpriseAccounts', () => {
    it('should return non-enterprise accounts successfully when data exists', async () => {
      // Arrange
      const mockNonEnterpriseAccounts = {
        count: 2,
        rows: [
          { company: { id: 1, name: 'Company 1' }, projectCount: 6 },
          { company: { id: 2, name: 'Company 2' }, projectCount: 8 },
        ],
      };

      accountService.getEnterpriseAccounts.mockResolvedValue(mockNonEnterpriseAccounts);

      // Act
      await AccountCornController.getNonEnterpriseAccounts(mockReq, mockRes, mockNext);

      // Assert
      expect(accountService.getEnterpriseAccounts).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Non Enterprise Accounts Listed Successfully.',
        data: mockNonEnterpriseAccounts,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return empty array when no non-enterprise accounts exist', async () => {
      // Arrange
      accountService.getEnterpriseAccounts.mockResolvedValue(null);

      // Act
      await AccountCornController.getNonEnterpriseAccounts(mockReq, mockRes, mockNext);

      // Assert
      expect(accountService.getEnterpriseAccounts).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Non Enterprise Accounts Listed Successfully.',
        data: [],
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return empty array when non-enterprise accounts is falsy', async () => {
      // Arrange
      accountService.getEnterpriseAccounts.mockResolvedValue(false);

      // Act
      await AccountCornController.getNonEnterpriseAccounts(mockReq, mockRes, mockNext);

      // Assert
      expect(accountService.getEnterpriseAccounts).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Non Enterprise Accounts Listed Successfully.',
        data: [],
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle errors and call next with error', async () => {
      // Arrange
      const error = new Error('Service error');
      accountService.getEnterpriseAccounts.mockRejectedValue(error);

      // Act
      await AccountCornController.getNonEnterpriseAccounts(mockReq, mockRes, mockNext);

      // Assert
      expect(accountService.getEnterpriseAccounts).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('getNonEnterpriseAccountProjects', () => {
    it('should return non-enterprise account projects successfully when data exists', async () => {
      // Arrange
      const mockNonEnterpriseAccountProjects = {
        count: 3,
        rows: [
          { id: 1, projectName: 'Project 1', ParentCompanyId: 1 },
          { id: 2, projectName: 'Project 2', ParentCompanyId: 1 },
          { id: 3, projectName: 'Project 3', ParentCompanyId: 1 },
        ],
      };

      accountService.getNonEnterpriseAccountProjects.mockResolvedValue(
        mockNonEnterpriseAccountProjects,
      );

      // Act
      await AccountCornController.getNonEnterpriseAccountProjects(mockReq, mockRes, mockNext);

      // Assert
      expect(accountService.getNonEnterpriseAccountProjects).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Non Enterprise Accounts Listed Successfully.',
        data: mockNonEnterpriseAccountProjects,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return empty array when no non-enterprise account projects exist', async () => {
      // Arrange
      accountService.getNonEnterpriseAccountProjects.mockResolvedValue(null);

      // Act
      await AccountCornController.getNonEnterpriseAccountProjects(mockReq, mockRes, mockNext);

      // Assert
      expect(accountService.getNonEnterpriseAccountProjects).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Non Enterprise Accounts Listed Successfully.',
        data: [],
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return empty array when non-enterprise account projects is falsy', async () => {
      // Arrange
      accountService.getNonEnterpriseAccountProjects.mockResolvedValue(false);

      // Act
      await AccountCornController.getNonEnterpriseAccountProjects(mockReq, mockRes, mockNext);

      // Assert
      expect(accountService.getNonEnterpriseAccountProjects).toHaveBeenCalledWith(mockReq);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Non Enterprise Accounts Listed Successfully.',
        data: [],
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle errors and call next with error', async () => {
      // Arrange
      const error = new Error('Service error');
      accountService.getNonEnterpriseAccountProjects.mockRejectedValue(error);

      // Act
      await AccountCornController.getNonEnterpriseAccountProjects(mockReq, mockRes, mockNext);

      // Assert
      expect(accountService.getNonEnterpriseAccountProjects).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle undefined response from services', async () => {
      // Arrange
      accountService.getEnterpriseAccounts.mockResolvedValue(undefined);

      // Act
      await AccountCornController.getEnterpriseAccounts(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Enterprise Accounts Listed Successfully.',
        data: [],
      });
    });

    it('should handle empty object response from services', async () => {
      // Arrange
      accountService.getEnterpriseAccounts.mockResolvedValue({});

      // Act
      await AccountCornController.getEnterpriseAccounts(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Enterprise Accounts Listed Successfully.',
        data: {},
      });
    });

    it('should handle request with query parameters', async () => {
      // Arrange
      const mockReqWithQuery = {
        query: {
          pageSize: 10,
          pageNo: 1,
          sortColumn: 'name',
          sortType: 'ASC',
        },
      };

      const mockEnterpriseAccounts = {
        count: 1,
        rows: [{ company: { id: 1, name: 'Company 1' }, projectCount: 3 }],
      };

      accountService.getEnterpriseAccounts.mockResolvedValue(mockEnterpriseAccounts);

      // Act
      await AccountCornController.getEnterpriseAccounts(mockReqWithQuery, mockRes, mockNext);

      // Assert
      expect(accountService.getEnterpriseAccounts).toHaveBeenCalledWith(mockReqWithQuery);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Enterprise Accounts Listed Successfully.',
        data: mockEnterpriseAccounts,
      });
    });

    it('should handle request with params', async () => {
      // Arrange
      const mockReqWithParams = {
        query: {},
        params: { id: '123' },
      };

      const mockNonEnterpriseAccountProjects = {
        count: 1,
        rows: [{ id: 1, projectName: 'Project 1' }],
      };

      accountService.getNonEnterpriseAccountProjects.mockResolvedValue(
        mockNonEnterpriseAccountProjects,
      );

      // Act
      await AccountCornController.getNonEnterpriseAccountProjects(
        mockReqWithParams,
        mockRes,
        mockNext,
      );

      // Assert
      expect(accountService.getNonEnterpriseAccountProjects).toHaveBeenCalledWith(
        mockReqWithParams,
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 200,
        message: 'Non Enterprise Accounts Listed Successfully.',
        data: mockNonEnterpriseAccountProjects,
      });
    });
  });
});
