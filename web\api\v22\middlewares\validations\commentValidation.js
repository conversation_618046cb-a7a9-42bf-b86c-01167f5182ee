const Joi = require('joi');

const commentValidation = {
  addComment: {
    body: Joi.object({
      comment: Joi.string().required(),
      DeliveryRequestId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  getComment: {
    params: Joi.object({
      DeliveryRequestId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
      ProjectId: Joi.number().required(),
    }),
  },

  getInspectionComment: {
    params: Joi.object({
      InspectionRequestId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
      ProjectId: Joi.number().required(),
    }),
  },
};
module.exports = commentValidation;
