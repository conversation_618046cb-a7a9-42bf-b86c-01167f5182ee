const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class DeviceToken extends Model {
    static associate(models) {
      DeviceToken.belongsTo(models.User);
    }
  }
  DeviceToken.init(
    {
      UserId: DataTypes.INTEGER,
      deviceToken: DataTypes.STRING,
      isDeleted: DataTypes.BOOLEAN,
    },
    {
      sequelize,
      modelName: 'DeviceToken',
    },
  );
  return DeviceToken;
};
