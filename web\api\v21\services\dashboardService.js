const moment = require('moment');
const _ = require('lodash');
const { Sequelize, Enterprise } = require('../models');
let { Project, Company, DeliveryRequest, Member, User } = require('../models');
const {
  Equipments,
  CraneRequest,
  ConcreteRequest,
  VoidList,
  InspectionRequest,
} = require('../models');
const helper = require('../helpers/domainHelper');

let publicProject;
let publicUser;
let publicMember;
const { Op } = Sequelize;

const dashboardService = {
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicProject = modelData.Project;
    publicUser = modelData.User;
    publicMember = modelData.Member;
  },

  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;

    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;

    domainName = await this.resolveDomainName(
      domainName,
      ParentCompanyId,
      inputData.user.email
    );
    const modelObj = await helper.getDynamicModel(domainName);

    DeliveryRequest = modelObj.DeliveryRequest;
    Member = modelObj.Member;
    Company = modelObj.Company;
    Project = modelObj.Project;
    User = modelObj.User;

    await this.resolveUser(inputData, inputData.user.email, domainName);

    return undefined;
  },

  async resolveDomainName(domainName, ParentCompanyId, email) {
    if (domainName) {
      const domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }

    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const userData = email && (await publicUser.findOne({ where: { email } }));
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });

        const companyId = memberData ? memberData.EnterpriseId : ParentCompanyId;
        const enterpriseValue = await Enterprise.findOne({
          where: { id: companyId, status: 'completed' },
        });

        domainName = enterpriseValue ? enterpriseValue.name.toLowerCase() : '';
      }
    }
    return domainName;
  },

  async resolveUser(inputData, email, domainName) {
    if (domainName) {
      inputData.user = await User.findOne({ where: { email } });
    }
  },

  async getDashboardData(inputData, done) {
    try {
      const incomeData = inputData.body;
      const modelName = await this.resolveModel(inputData, incomeData.RoleId);

      const totalNewProject = await modelName.getUserProjects(inputData.user);
      const projectCount = totalNewProject.length;

      const dashData = this.initializeDashData(projectCount);
      const {
        currentFirstDay,
        currentLastDay,
        previousFirstDay,
        previousLastDay,
      } = this.calculateDates();
      const ProjectId = this.getProjectId(inputData, incomeData);

      const [currentMonthProject, prevMonthProject] =
        await this.getCurrentAndPreviousProjectsCount(
          inputData,
          modelName,
          currentFirstDay,
          currentLastDay,
          previousFirstDay,
          previousLastDay
        );

      dashData.diffProjects =
        currentMonthProject.length - prevMonthProject.length;
      const totalProject = await this.getRelevantProjects(
        ProjectId,
        incomeData,
        totalNewProject,
        Project
      );

      if (totalProject.length > 0) {
        await this.aggregateData(
          totalProject,
          dashData,
          { currentFirstDay, currentLastDay, previousFirstDay, previousLastDay },
          done
        );
      } else {
        done(dashData, false);
      }
    } catch (e) {
      done(null, e);
    }
  },

  async resolveModel(inputData, roleId) {
    if (roleId !== 4) {
      await this.getDynamicModel(inputData);
      return Project;
    } else {
      await this.returnProjectModel();
      return publicProject;
    }
  },

  async getCurrentAndPreviousProjectsCount(
    inputData,
    modelName,
    currentFirstDay,
    currentLastDay,
    previousFirstDay,
    previousLastDay
  ) {
    const currentMonCondition = {
      createdAt: { [Op.between]: [currentFirstDay, currentLastDay] },
    };
    const prevMonCondition = {
      createdAt: { [Op.between]: [previousFirstDay, previousLastDay] },
    };

    return await Promise.all([
      modelName.getProjectCount(inputData.user, currentMonCondition),
      modelName.getProjectCount(inputData.user, prevMonCondition),
    ]);
  },

  initializeDashData(projectCount) {
    return {
      projects: projectCount,
      deliveryRequest: 0,
      member: 0,
      company: 0,
      equipment: 0,
      diffEquipment: 0,
      diffCompany: 0,
      diffMember: 0,
      diffDeliveryRequest: 0,
      diffProjects: 0,
    };
  },

  calculateDates() {
    const date = new Date();
    const y = date.getFullYear();
    const m = date.getMonth();
    const currentFirstDay = new Date(y, m, 1);
    const currentLastDay = new Date(y, m + 1, 0);

    const previousMonth = new Date(y, m, 1);
    previousMonth.setMonth(previousMonth.getMonth() - 1);
    const previousFirstDay = new Date(
      previousMonth.getFullYear(),
      previousMonth.getMonth(),
      1
    );
    const previousLastDay = new Date(
      previousMonth.getFullYear(),
      previousMonth.getMonth() + 1,
      0
    );

    return { currentFirstDay, currentLastDay, previousFirstDay, previousLastDay };
  },

  getProjectId(inputData, incomeData) {
    let ProjectId = inputData.params?.ProjectId;
    if (!ProjectId || ProjectId === 'undefined') {
      ProjectId = incomeData.ProjectId;
    }
    return ProjectId;
  },

  async getRelevantProjects(ProjectId, incomeData, totalNewProject, Project) {
    if (incomeData.PAadmin || (ProjectId && ProjectId !== 'undefined')) {
      const data = await Project.findByPk(ProjectId);
      return data ? [data] : [];
    }
    return totalNewProject;
  },

  async aggregateData(totalProject, dashData, dateRange, done) {
    await this.processEntities(
      totalProject,
      dashData,
      dateRange,
      {
        company: this.getTotalCompany,
        member: this.getTotalMember,
        equipment: this.getTotalEquipments,
        deliveryRequest: this.getTotalDeliveryRequest,
      },
      done
    );
  },

  async processEntities(totalProject, dashData, dateRange, entityHandlers, done) {
    for (const [key, handler] of Object.entries(entityHandlers)) {
      const result = await handler.call(this, totalProject, dateRange);
      Object.assign(dashData, {
        [key]: result.total,
        [`diff${key[0].toUpperCase() + key.slice(1)}`]: result.diff,
      });
    }
    done(dashData, false);
  },

  async calculateTotalAndDiff(
    totalProject,
    additionalCondition,
    entityModel,
    dateRange
  ) {
    let total = 0;
    let diff = 0;

    for (const element of totalProject) {
      const count = await this.processEntityForProject(
        entityModel,
        additionalCondition,
        element.id
      );
      total += count;

      const currentMonthCount = await this.processEntityInRange(
        entityModel,
        [dateRange.currentFirstDay, dateRange.currentLastDay],
        element.id,
        additionalCondition
      );
      const previousMonthCount = await this.processEntityInRange(
        entityModel,
        [dateRange.previousFirstDay, dateRange.previousLastDay],
        element.id,
        additionalCondition
      );

      diff += currentMonthCount - previousMonthCount;
    }

    return { total, diff };
  },

  async processEntityForProject(entityModel, condition, projectId) {
    return await entityModel.count({
      where: { ...condition, ProjectId: projectId, isDeleted: false },
    });
  },

  async processEntityInRange(
    entityModel,
    dateRange,
    projectId,
    additionalCondition = {}
  ) {
    return await entityModel.count({
      where: {
        createdAt: { [Op.between]: dateRange },
        ProjectId: projectId,
        ...additionalCondition,
        isDeleted: false,
      },
    });
  },

  async getTotalCompany(totalProject, dateRange) {
    return this.calculateTotalAndDiff(totalProject, {}, Company, dateRange);
  },

  async getTotalMember(totalProject, dateRange) {
    const roleCondition = { RoleId: { [Op.ne]: 1 }, isGuestUser: false };
    return this.calculateTotalAndDiff(
      totalProject,
      roleCondition,
      Member,
      dateRange
    );
  },

  async getTotalEquipments(totalProject, dateRange) {
    return this.calculateTotalAndDiff(totalProject, {}, Equipments, dateRange);
  },

  async getTotalDeliveryRequest(totalProject, dateRange) {
    const voidDelivery = await this.getVoidDeliveryRequests(totalProject);
    const deliveryCondition = {
      '$DeliveryRequest.id$': { [Op.and]: [{ [Op.notIn]: voidDelivery }] },
    };
    return this.calculateTotalAndDiff(
      totalProject,
      deliveryCondition,
      DeliveryRequest,
      dateRange
    );
  },

  async getVoidDeliveryRequests(totalProject) {
    const voidDeliveryRequestList = await VoidList.findAll({
      where: {
        ProjectId: totalProject[0].id,
        isDeliveryRequest: true,
        DeliveryRequestId: { [Op.ne]: null },
      },
    });

    return voidDeliveryRequestList.map((element) => element.DeliveryRequestId);
  },

  async getOverAllDelivery(inputData, newCondition, limit, offset) {
    try {
      await this.getDynamicModel(inputData);
      let projectData = [];
      let condition = { isDeleted: false };

      projectData = await this.populateProjectData(
        newCondition.ProjectId,
        inputData.user
      );

      if (newCondition.upcoming) {
        condition.deliveryStart = { [Op.gt]: new Date() };
      }

      const voidDelivery = await this.getVoidDeliveries(newCondition.ProjectId);
      condition['$DeliveryRequest.id$'] = {
        [Op.and]: [{ [Op.notIn]: voidDelivery }],
      };

      const deliveryList = await DeliveryRequest.findAndCountAll({
        include: [
          {
            association: 'Project',
            where: { isDeleted: false },
            attributes: ['id', 'projectName', 'publicSchemaId'],
          },
        ],
        where: {
          ProjectId: { [Op.in]: projectData },
          ...condition,
          isQueued: false,
        },
        attributes: ['id', 'DeliveryId', 'description', 'deliveryStart'],
        limit,
        offset,
        order: [['deliveryStart', 'ASC']],
      });

      return deliveryList;
    } catch (e) {
      return e;
    }
  },

  async populateProjectData(projectId, user) {
    if (projectId) {
      return [projectId];
    }

    const projects = await Project.getUserProjects(user);
    return projects.map((project) => project.id);
  },

  async getVoidDeliveries(projectId) {
    const voidDeliveryRequestList = await VoidList.findAll({
      where: {
        ProjectId: +projectId,
        isDeliveryRequest: true,
        DeliveryRequestId: { [Op.ne]: null },
      },
    });

    return voidDeliveryRequestList.map((element) => element.DeliveryRequestId);
  },

  async getOverAllConcreteRequest(inputData, newCondition, limit, offset) {
    try {
      await this.getDynamicModel(inputData);
      let projectData = [];
      let condition = { isDeleted: false };

      projectData = await this.populateProjectData(
        newCondition.ProjectId,
        inputData.user
      );

      if (newCondition.upcoming) {
        condition.concretePlacementStart = { [Op.gt]: new Date() };
      }

      const voidConcrete = await this.getVoidConcretes(newCondition.ProjectId);
      condition['$ConcreteRequest.id$'] = {
        [Op.and]: [{ [Op.notIn]: voidConcrete }],
      };

      const deliveryList = await ConcreteRequest.findAndCountAll({
        include: [
          {
            association: 'Project',
            where: { isDeleted: false },
            attributes: ['id', 'projectName', 'publicSchemaId'],
          },
        ],
        where: { ProjectId: { [Op.in]: projectData }, ...condition },
        attributes: ['id', 'ConcreteRequestId', 'description', 'concretePlacementStart'],
        limit,
        offset,
        order: [['concretePlacementStart', 'ASC']],
      });

      return deliveryList;
    } catch (e) {
      return e;
    }
  },

  async getVoidConcretes(projectId) {
    const voidConcreteRequestList = await VoidList.findAll({
      where: {
        ProjectId: +projectId,
        isDeliveryRequest: false,
        ConcreteRequestId: { [Op.ne]: null },
      },
    });

    return voidConcreteRequestList.map((element) => element.ConcreteRequestId);
  },

  async getGraphDelivery(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const incomeData = inputData.body;
      const newCondition = { ProjectId: incomeData.ProjectId };

      const deliveryListData = await this.getOverAllDelivery(
        inputData,
        newCondition
      );
      const deliveryList = deliveryListData.rows;
      const results = this.formatGraphData(
        deliveryList,
        (item) => item.deliveryStart
      );

      done({ count: results }, false);
    } catch (e) {
      done(null, e);
    }
  },

  async getOverAllCrane(inputData, newCondition) {
    try {
      await this.getDynamicModel(inputData);
      let projectData = [];
      let condition = { isDeleted: false };
      let condition1 = { isDeleted: false };

      projectData = await this.populateProjectData(
        newCondition.ProjectId,
        inputData.user
      );

      const voidCraneDelivery = await this.getVoidCraneDeliveries(
        newCondition.ProjectId
      );
      const voidDelivery = await this.getVoidDeliveries(newCondition.ProjectId);

      condition['$DeliveryRequest.id$'] = {
        [Op.and]: [{ [Op.notIn]: voidDelivery }],
      };
      condition1['$CraneRequest.id$'] = {
        [Op.and]: [{ [Op.notIn]: voidCraneDelivery }],
      };

      const craneRequests = await this.getCraneRequests(projectData, condition1);
      const deliveryRequests = await this.getDeliveryCraneRequests(
        projectData,
        condition
      );

      return [...craneRequests, ...deliveryRequests];
    } catch (e) {
      return e;
    }
  },

  async getVoidCraneDeliveries(projectId) {
    const voidCraneRequestList = await VoidList.findAll({
      where: {
        ProjectId: projectId,
        isDeliveryRequest: false,
        CraneRequestId: { [Op.ne]: null },
      },
    });

    return voidCraneRequestList.map((element) => element.CraneRequestId);
  },

  async getCraneRequests(projectData, condition) {
    return CraneRequest.findAll({
      include: [
        {
          association: 'Project',
          where: { isDeleted: false },
          attributes: ['id', 'projectName', 'publicSchemaId'],
        },
      ],
      where: { ProjectId: { [Op.in]: projectData }, ...condition },
      attributes: [
        'id',
        'CraneRequestId',
        'description',
        'craneDeliveryStart',
        'requestType',
        'craneDeliveryEnd',
      ],
      order: [['craneDeliveryStart', 'ASC']],
    });
  },

  async getDeliveryCraneRequests(projectData, condition) {
    return DeliveryRequest.findAll({
      include: [
        {
          association: 'Project',
          where: { isDeleted: false },
          attributes: ['id', 'projectName', 'publicSchemaId'],
        },
      ],
      where: {
        ProjectId: { [Op.in]: projectData },
        ...condition,
        isQueued: false,
        requestType: 'deliveryRequestWithCrane',
      },
      attributes: [
        'id',
        'DeliveryId',
        'description',
        'deliveryStart',
        'requestType',
      ],
      order: [['deliveryStart', 'ASC']],
    });
  },

  async getCraneGraphData(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const incomeData = inputData.body;
      const newCondition = { ProjectId: incomeData.ProjectId };

      const craneList = await this.getOverAllCrane(inputData, newCondition);
      const results = this.formatGraphData(craneList, (item) =>
        item.requestType === 'craneRequest'
          ? item.craneDeliveryStart
          : item.deliveryStart
      );

      done({ count: results }, false);
    } catch (e) {
      done(null, e);
    }
  },

  async getConcreteGraphData(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const incomeData = inputData.body;
      const newCondition = { ProjectId: incomeData.ProjectId };

      const deliveryListData = await this.getOverAllConcreteRequest(
        inputData,
        newCondition
      );
      const deliveryList = deliveryListData.rows;
      const results = this.formatGraphData(
        deliveryList,
        (item) => item.concretePlacementStart
      );

      done({ count: results }, false);
    } catch (e) {
      done(null, e);
    }
  },

  formatGraphData(itemList, dateKeyExtractor) {
    itemList.forEach((item) => {
      const dateKey = dateKeyExtractor(item);
      item.deliveryDate = moment(dateKey).format('MM-YYYY');
      item.shortmonth = moment(dateKey).format('MMM');
      item.month = moment(dateKey).format('MM');
      item.year = moment(dateKey).format('YYYY');
    });

    return _.sortBy(
      _.chain(itemList)
        .groupBy('deliveryDate')
        .map((value) => ({
          total: value.length,
          month: value[0].month,
          year: value[0].year,
          shortmonth: value[0].shortmonth,
        }))
        .value(),
      ['year', 'month']
    );
  },

  async getOverAllInspection(inputData, newCondition, limit, offset) {
    try {
      await this.getDynamicModel(inputData);
      let projectData = [];
      let condition = { isDeleted: false };

      projectData = await this.populateProjectData(
        newCondition.ProjectId,
        inputData.user
      );

      if (newCondition.upcoming) {
        condition.inspectionStart = { [Op.gt]: new Date() };
      }

      const voidInspection = await this.getVoidInspections(newCondition.ProjectId);
      condition['$InspectionRequest.id$'] = {
        [Op.and]: [{ [Op.notIn]: voidInspection }],
      };

      const deliveryList = await InspectionRequest.findAndCountAll({
        include: [
          {
            association: 'Project',
            where: { isDeleted: false },
            attributes: ['id', 'projectName', 'publicSchemaId'],
          },
        ],
        where: { ProjectId: { [Op.in]: projectData }, ...condition },
        attributes: ['id', 'InspectionId', 'description', 'inspectionStart'],
        limit,
        offset,
        order: [['inspectionStart', 'ASC']],
      });

      return deliveryList;
    } catch (e) {
      return e;
    }
  },

  async getVoidInspections(projectId) {
    const voidInspectionRequestList = await VoidList.findAll({
      where: {
        ProjectId: +projectId,
        isDeliveryRequest: false,
        InspectionRequestId: { [Op.ne]: null },
      },
    });

    return voidInspectionRequestList.map(
      (element) => element.InspectionRequestId
    );
  },
  async getReleasenoteVersion(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const loginUser = inputData.user;
      const newNversionseen = await User.update(
        { versionFlag: true },
        {
          where: {
            id: loginUser.id,
          },
        },
      );
      done(newNversionseen, false);
    } catch (e) {
      done(null, e);
    }
  },
  async upcomingDelivery(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      const offset = (pageNumber - 1) * pageSize;
      const deliveryList = await this.getOverAllDelivery(
        inputData,
        { upcoming: true },
        pageSize,
        offset,
      );
      done(deliveryList, false);
    } catch (e) {
      done(null, e);
    }
  },

  async getInspectionGraphData(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const incomeData = inputData.body;
      const newCondition = { ProjectId: incomeData.ProjectId };

      const inspectionListData = await this.getOverAllInspection(
        inputData,
        newCondition
      );
      const inspectionList = inspectionListData.rows;
      const results = this.formatGraphData(
        inspectionList,
        (item) => item.inspectionStart
      );

      done({ count: results }, false);
    } catch (e) {
      done(null, e);
    }
  },
};

module.exports = dashboardService;