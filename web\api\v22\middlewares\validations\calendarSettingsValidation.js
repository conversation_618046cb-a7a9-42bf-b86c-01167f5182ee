const Joi = require('joi');

const calendarSettingsValidation = {
  addCalendarEvent: {
    query: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
    }),
    body: Joi.object({
      description: Joi.string().required(),
      fromDate: Joi.string().required(),
      toDate: Joi.string().required(),
      isAllDay: Joi.boolean().required(),
      TimeZoneId: Joi.number().required(),
      GateId: Joi.array().required(),
      LocationId: Joi.array().required(),
      EquipmentId: Joi.array().required(),
      isApplicableToDelivery: Joi.boolean().required(),
      isApplicableToCrane: Joi.boolean().required(),
      isApplicableToConcrete: Joi.boolean().required(),
      recurrence: Joi.string().required(),
      repeatEveryCount: Joi.string().allow('', null),
      repeatEveryType: Joi.string().allow('', null),
      days: Joi.array().allow('', null),
      startTime: Joi.string().allow('', null),
      endTime: Joi.string().allow('', null),
      chosenDateOfMonth: Joi.boolean().allow('', null),
      dateOfMonth: Joi.string().allow('', null),
      monthlyRepeatType: Joi.string().allow('', null),
      createdBy: Joi.number().required(),
      endDate: Joi.string().required(),
      isApplicableToInspection: Joi.boolean().required(),
    }),
  },
  editCalendarEvent: {
    query: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
    body: Joi.object({
      description: Joi.string().required(),
      fromDate: Joi.string().required(),
      toDate: Joi.string().required(),
      TimeZoneId: Joi.number().required(),
      isAllDay: Joi.boolean().required(),
      isApplicableToDelivery: Joi.boolean().required(),
      isApplicableToCrane: Joi.boolean().required(),
      isApplicableToConcrete: Joi.boolean().required(),
      recurrence: Joi.string().required(),
      repeatEveryCount: Joi.string().allow('', null),
      repeatEveryType: Joi.string().allow('', null),
      days: Joi.array().allow('', null),
      startTime: Joi.string().allow('', null),
      endTime: Joi.string().allow('', null),
      chosenDateOfMonth: Joi.boolean().allow('', null),
      dateOfMonth: Joi.string().allow('', null),
      monthlyRepeatType: Joi.string().allow('', null),
      createdBy: Joi.number().required(),
      endDate: Joi.string().required(),
      isApplicableToInspection: Joi.boolean().required(),
      GateId: Joi.array().required(),
      LocationId: Joi.array().required(),
      EquipmentId: Joi.array().required(),
    }),
  },
  deleteCalendarEvent: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
    query: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
    }),
  },
  getCalendarEvents: {
    query: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
      start: Joi.string().required(),
      end: Joi.string().required(),
      search: Joi.string().optional().allow('', null),
      calendarView: Joi.string().required(),
      timezone: Joi.string().required(),
      isDST: Joi.boolean().required(),
    }),
  },
  getCalendarEvent: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
    query: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
    }),
  },
  getCalendarMonthEvents: {
    query: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
      start: Joi.string().required(),
      end: Joi.string().required(),
      search: Joi.string().optional().allow('', null),
      currentViewMonth: Joi.string().required(),
      timezone: Joi.string().required(),
      isDST: Joi.boolean().required(),
    }),
  },
};
module.exports = calendarSettingsValidation;
