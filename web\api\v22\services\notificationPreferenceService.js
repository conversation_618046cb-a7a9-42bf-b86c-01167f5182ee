const status = require('http-status');
const { Sequelize, Enterprise } = require('../models');
let {
  NotificationPreferenceItem,
  NotificationPreference,
  DigestNotification,
  Member,
  Gates,
  Project,
} = require('../models');
let { User } = require('../models');
const helper = require('../helpers/domainHelper');
// const mixpanelService = require('./mixpanelService');

let publicUser;
let publicMember;
const { Op } = Sequelize;

const ApiError = require('../helpers/apiError');

const notificationPreferenceService = {
  async setNotificationPreference(req, done) {
    try {
      await this.getDynamicModel(req);
      const condition = {
        MemberId: req.query.MemberId,
        ProjectId: req.query.ProjectId,
        ParentCompanyId: req.query.ParentCompanyId,
        isDeleted: false,
      };
      req.body.inAppNotification.map(async (item) => {
        await NotificationPreference.update(
          { instant: item.instant, dailyDigest: item.dailyDigest },
          {
            where: {
              NotificationPreferenceItemId: item.NotificationPreferenceItemId,
              ...condition,
            },
          },
        );
      });
      req.body.emailNotification.map(async (item) => {
        await NotificationPreference.update(
          { instant: item.instant, dailyDigest: item.dailyDigest },
          {
            where: {
              NotificationPreferenceItemId: item.NotificationPreferenceItemId,
              ...condition,
            },
          },
        );
      });
      await Member.update(
        {
          time: req.body.dailyDigestTiming.time,
          timeFormat: req.body.dailyDigestTiming.timeFormat,
          TimeZoneId: req.body.dailyDigestTiming.TimeZoneId,
        },
        {
          where: {
            id: req.query.MemberId,
            ProjectId: req.query.ProjectId,
            ParentCompanyId: req.query.ParentCompanyId,
            isDeleted: false,
          },
        },
      );
      done(true, false);
    } catch (e) {
      done(null, e);
    }
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    let enterpriseValue;
    let ProjectId;
    const incomeData = inputData;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    Gates = modelObj.Gates;
    Project = modelObj.Project;
    User = modelObj.User;
    Member = modelObj.Member;
    // NotificationPreferenceItem = modelObj.NotificationPreferenceItem;
    // NotificationPreference = modelObj.NotificationPreference;
    // DigestNotification = modelObj.DigestNotification;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return ProjectId;
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicUser = modelData.User;
    publicMember = modelData.Member;
  },
  async listNotificationPreference(req, done) {
    try {
      await this.getDynamicModel(req);
      const memberNotificationDetail = await NotificationPreference.findAll({
        where: {
          MemberId: +req.query.MemberId,
          ProjectId: +req.query.ProjectId,
          ParentCompanyId: +req.query.ParentCompanyId,
          isDeleted: false,
        },
        attributes: [
          'id',
          'MemberId',
          'ProjectId',
          'ParentCompanyId',
          'NotificationPreferenceItemId',
          'instant',
          'dailyDigest',
        ],
        include: [
          {
            association: 'NotificationPreferenceItem',
            where: { isDeleted: false },
            attributes: ['id', 'description', 'inappNotification', 'emailNotification', 'itemId'],
          },
        ],
      });
      const result = [];
      const getInappNotification = memberNotificationDetail.filter(
        (object) => object.NotificationPreferenceItem.inappNotification === true,
      );
      const getEmailNotification = memberNotificationDetail.filter(
        (object2) => object2.NotificationPreferenceItem.emailNotification === true,
      );
      const getDailyDigestTiming = await Member.findOne({
        where: {
          UserId: +req.user.id,
          isDeleted: false,
          ProjectId: +req.query.ProjectId,
          ParentCompanyId: +req.query.ParentCompanyId,
        },
        attributes: ['id', 'time', 'timeFormat', 'TimeZoneId'],
      });
      getInappNotification.sort((a, b) =>
        a.NotificationPreferenceItem.itemId > b.NotificationPreferenceItem.itemId ? 1 : -1,
      );
      getEmailNotification.sort((a, b) =>
        a.NotificationPreferenceItem.itemId > b.NotificationPreferenceItem.itemId ? 1 : -1,
      );
      const object3 = {
        inAppNotification: getInappNotification,
        emailNotification: getEmailNotification,
        digestTiming: getDailyDigestTiming,
      };
      result.push(object3);
      done(result, false);
    } catch (e) {
      done(null, e);
    }
  },
  async notificationPreferenceItems(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const notificationItems = await NotificationPreferenceItem.findAll({
        attributes: ['id', 'description', 'inappNotification', 'emailNotification', 'itemId'],
        group: ['NotificationPreferenceItem.id', 'NotificationPreferenceItem.description'],
      });
      done(notificationItems, false);
    } catch (e) {
      done(null, e);
    }
  },
};
module.exports = notificationPreferenceService;
