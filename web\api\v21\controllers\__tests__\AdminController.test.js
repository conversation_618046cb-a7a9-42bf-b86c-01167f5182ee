const status = require('http-status');

jest.mock('../../models', () => ({
  User: {
    getAll: jest.fn(),
    findOne: jest.fn(),
    getBy: jest.fn(),
  },
  Enterprise: {
    findOne: jest.fn(),
  },
  ParentCompany: {
    getBy: jest.fn(),
  },
  Project: {
    findAll: jest.fn(),
  },
  Sequelize: {
    and: jest.fn((...args) => args),
    or: jest.fn((...args) => args),
  },
}));
jest.mock('../helpers/apiError', () =>
  jest.fn().mockImplementation((msg, code) => ({ msg, code })),
);

// Create a mock UserSerializer
const mockUserSerializer = {
  serialize: jest.fn((user) => ({ ...user, serialized: true }))
};

// Mock the serializers module with correct path
jest.mock('../../serializers', () => ({
  UserSerializer: mockUserSerializer,
}));
jest.mock('../../services', () => ({
  adminService: {
    adminLogin: jest.fn(),
    emailDomain: jest.fn(),
    createExistAccountAdmin: jest.fn(),
    createNewAccountAdmin: jest.fn(),
    createAccountAdmin: jest.fn(),
  },
  authService: {
    forgotPassword: jest.fn(),
  },
}));

const { User, Enterprise, ParentCompany, Project, Sequelize } = require('../../models');
const { adminService, authService } = require('../../services');
const ApiError = require('../helpers/apiError');
const AdminController = require('../AdminController');

describe('AdminController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    jest.clearAllMocks();
    mockReq = { body: {}, params: {} };
    mockRes = { status: jest.fn().mockReturnThis(), json: jest.fn().mockReturnThis() };
    mockNext = jest.fn();
  });

  describe('users', () => {
    it('should return empty users list successfully', async () => {
      User.getAll.mockResolvedValue([]);
      await AdminController.users(mockReq, mockRes);
      expect(User.getAll).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ users: [] });
    });
    it('should return serialized users list successfully', async () => {
      const mockUsers = [
        { id: 1, email: '<EMAIL>' },
        { id: 2, email: '<EMAIL>' },
      ];
      User.getAll.mockResolvedValue(mockUsers);
      mockUserSerializer.serialize.mockImplementation((user) => ({ ...user, serialized: true }));
      await AdminController.users(mockReq, mockRes);
      expect(User.getAll).toHaveBeenCalled();
      expect(mockUserSerializer.serialize).toHaveBeenCalledTimes(2);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        users: [
          { id: 1, email: '<EMAIL>', serialized: true },
          { id: 2, email: '<EMAIL>', serialized: true },
        ],
      });
    });
  });

  describe('adminLogin', () => {
    it('should login admin successfully', async () => {
      mockReq.body = { email: '<EMAIL>', password: 'pass' };
      adminService.adminLogin.mockImplementation((data, cb) => cb({ token: 'tok', id: 1 }, null));
      mockUserSerializer.serialize.mockReturnValue({ id: 1, serialized: true });
      await AdminController.adminLogin(mockReq, mockRes, mockNext);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Login Successful',
        token: 'tok',
        userDetails: { id: 1, serialized: true },
      });
    });
    it('should call next with error if error returned', async () => {
      const error = new Error('fail');
      adminService.adminLogin.mockImplementation((data, cb) => cb(null, error));
      await AdminController.adminLogin(mockReq, mockRes, mockNext);
      expect(mockNext).toHaveBeenCalledWith(error);
    });
    it('should call next on thrown error', async () => {
      adminService.adminLogin.mockImplementation(() => {
        throw new Error('fail');
      });
      await AdminController.adminLogin(mockReq, mockRes, mockNext);
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('adminForgotPassword', () => {
    it('should send reset password email', async () => {
      mockReq.body = { email: '<EMAIL>' };
      User.findOne.mockResolvedValue({ id: 1, email: '<EMAIL>' });
      authService.forgotPassword.mockImplementation((user, cb) => cb({}, null));
      await AdminController.adminForgotPassword(mockReq, mockRes, mockNext);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Reset password email sent successfully',
      });
    });
    it('should call next with error if forgotPassword returns error', async () => {
      mockReq.body = { email: '<EMAIL>' };
      User.findOne.mockResolvedValue({ id: 1, email: '<EMAIL>' });
      const error = new Error('fail');
      authService.forgotPassword.mockImplementation((user, cb) => cb(null, error));
      await AdminController.adminForgotPassword(mockReq, mockRes, mockNext);
      expect(mockNext).toHaveBeenCalledWith(error);
    });
    it('should call next on thrown error', async () => {
      mockReq.body = { email: '<EMAIL>' };
      User.findOne.mockResolvedValue({ id: 1, email: '<EMAIL>' });
      authService.forgotPassword.mockImplementation(() => {
        throw new Error('fail');
      });
      await AdminController.adminForgotPassword(mockReq, mockRes, mockNext);
      expect(mockNext).toHaveBeenCalled();
    });
    it('should call next with not found error if user not found', async () => {
      mockReq.body = { email: '<EMAIL>' };
      User.findOne.mockResolvedValue(null);
      await AdminController.adminForgotPassword(mockReq, mockRes, mockNext);
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('createAccountAdmin', () => {
    it('should create account admin successfully when user exists', async () => {
      const userData = {
        basicDetails: { email: '<EMAIL>' },
        domainDetails: { name: 'testdomain' },
        projectCount: 5,
      };
      const userExist = { id: 1, email: '<EMAIL>' };
      const userDetail = { id: 1, email: '<EMAIL>' };

      mockReq.body = userData;
      User.findOne.mockResolvedValue(userExist);
      Enterprise.findOne.mockResolvedValue(null);
      adminService.emailDomain.mockResolvedValue('test.com');
      ParentCompany.getBy.mockResolvedValue(null);
      adminService.createExistAccountAdmin.mockImplementation((data, callback) =>
        callback(userDetail, null),
      );
      mockUserSerializer.serialize.mockReturnValue({ ...userDetail, serialized: true });

      await AdminController.createAccountAdmin(mockReq, mockRes, mockNext);

      expect(adminService.createExistAccountAdmin).toHaveBeenCalledWith(
        { ...userData, userExist },
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.CREATED);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Account Admin Created Successfully.',
        user: { ...userDetail, serialized: true },
      });
    });

    it('should return error when domain already exists', async () => {
      const userData = {
        basicDetails: { email: '<EMAIL>' },
        domainDetails: { name: 'existingdomain' },
      };

      mockReq.body = userData;
      User.findOne.mockResolvedValue(null);
      Enterprise.findOne.mockResolvedValue({ id: 1, name: 'existingdomain' });

      await AdminController.createAccountAdmin(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Domain name already exist',
          statusCode: status.BAD_REQUEST,
        }),
      );
    });

    it('should return error when project count is insufficient', async () => {
      const userData = {
        basicDetails: { email: '<EMAIL>' },
        domainDetails: { name: 'testdomain' },
        projectCount: 3,
      };
      const existParentCompany = { id: 1, emailDomainName: 'test.com' };
      const projectDet = [
        { id: 1, ParentCompanyId: 1, isAccount: false },
        { id: 2, ParentCompanyId: 1, isAccount: false },
        { id: 3, ParentCompanyId: 1, isAccount: false },
        { id: 4, ParentCompanyId: 1, isAccount: false },
      ];

      mockReq.body = userData;
      User.findOne.mockResolvedValue(null);
      Enterprise.findOne.mockResolvedValue(null);
      adminService.emailDomain.mockResolvedValue('test.com');
      ParentCompany.getBy.mockResolvedValue(existParentCompany);
      Project.findAll.mockResolvedValue(projectDet);

      await AdminController.createAccountAdmin(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Please enter higher project count',
          statusCode: status.BAD_REQUEST,
        }),
      );
    });

    it('should return error when already grouped to account', async () => {
      const userData = {
        basicDetails: { email: '<EMAIL>' },
        domainDetails: { name: 'testdomain' },
        projectCount: 5,
      };
      const existParentCompany = { id: 1, emailDomainName: 'test.com' };
      const projectDet = [{ id: 1, ParentCompanyId: 1, isAccount: true }];

      mockReq.body = userData;
      User.findOne.mockResolvedValue(null);
      Enterprise.findOne.mockResolvedValue(null);
      adminService.emailDomain.mockResolvedValue('test.com');
      ParentCompany.getBy.mockResolvedValue(existParentCompany);
      Project.findAll.mockResolvedValue(projectDet);

      await AdminController.createAccountAdmin(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Already grouped to a account',
          statusCode: status.BAD_REQUEST,
        }),
      );
    });

    it('should create account admin successfully when user does not exist and group is true', async () => {
      const userData = {
        basicDetails: { email: '<EMAIL>' },
        domainDetails: { name: 'newdomain' },
        projectCount: 5,
        group: true,
      };
      const userDetail = { id: 2, email: '<EMAIL>' };

      mockReq.body = userData;
      User.findOne.mockResolvedValue(null);
      Enterprise.findOne.mockResolvedValue(null);
      adminService.emailDomain.mockResolvedValue('test.com');
      ParentCompany.getBy.mockResolvedValue(null);
      adminService.createNewAccountAdmin.mockImplementation((data, callback) =>
        callback(userDetail, null),
      );
      mockUserSerializer.serialize.mockReturnValue({ ...userDetail, serialized: true });

      await AdminController.createAccountAdmin(mockReq, mockRes, mockNext);

      expect(adminService.createNewAccountAdmin).toHaveBeenCalledWith(
        userData,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.CREATED);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Account Admin Created Successfully.',
        user: { ...userDetail, serialized: true },
      });
    });

    it('should create account admin successfully when user does not exist and group is false', async () => {
      const userData = {
        basicDetails: { email: '<EMAIL>' },
        domainDetails: { name: 'newdomain' },
        projectCount: 5,
        group: false,
      };
      const userDetail = { id: 2, email: '<EMAIL>' };

      mockReq.body = userData;
      User.findOne.mockResolvedValue(null);
      Enterprise.findOne.mockResolvedValue(null);
      adminService.emailDomain.mockResolvedValue('test.com');
      ParentCompany.getBy.mockResolvedValue(null);
      adminService.createAccountAdmin.mockImplementation((data, callback) =>
        callback(userDetail, null),
      );
      mockUserSerializer.serialize.mockReturnValue({ ...userDetail, serialized: true });

      await AdminController.createAccountAdmin(mockReq, mockRes, mockNext);

      expect(adminService.createAccountAdmin).toHaveBeenCalledWith(
        userData,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.CREATED);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Account Admin Created Successfully.',
        user: { ...userDetail, serialized: true },
      });
    });

    it('should handle service error in createExistAccountAdmin', async () => {
      const userData = {
        basicDetails: { email: '<EMAIL>' },
        domainDetails: { name: 'testdomain' },
        projectCount: 5,
      };
      const userExist = { id: 1, email: '<EMAIL>' };
      const error = new Error('Service error');

      mockReq.body = userData;
      User.findOne.mockResolvedValue(userExist);
      Enterprise.findOne.mockResolvedValue(null);
      adminService.emailDomain.mockResolvedValue('test.com');
      ParentCompany.getBy.mockResolvedValue(null);
      adminService.createExistAccountAdmin.mockImplementation((data, callback) =>
        callback(null, error),
      );

      await AdminController.createAccountAdmin(mockReq, mockRes, mockNext);

      expect(adminService.createExistAccountAdmin).toHaveBeenCalledWith(
        { ...userData, userExist },
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(error);
    });

    it('should handle service error in createNewAccountAdmin', async () => {
      const userData = {
        basicDetails: { email: '<EMAIL>' },
        domainDetails: { name: 'newdomain' },
        projectCount: 5,
        group: true,
      };
      const error = new Error('Service error');

      mockReq.body = userData;
      User.findOne.mockResolvedValue(null);
      Enterprise.findOne.mockResolvedValue(null);
      adminService.emailDomain.mockResolvedValue('test.com');
      ParentCompany.getBy.mockResolvedValue(null);
      adminService.createNewAccountAdmin.mockImplementation((data, callback) =>
        callback(null, error),
      );

      await AdminController.createAccountAdmin(mockReq, mockRes, mockNext);

      expect(adminService.createNewAccountAdmin).toHaveBeenCalledWith(
        userData,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(error);
    });

    it('should handle service error in createAccountAdmin', async () => {
      const userData = {
        basicDetails: { email: '<EMAIL>' },
        domainDetails: { name: 'newdomain' },
        projectCount: 5,
        group: false,
      };
      const error = new Error('Service error');

      mockReq.body = userData;
      User.findOne.mockResolvedValue(null);
      Enterprise.findOne.mockResolvedValue(null);
      adminService.emailDomain.mockResolvedValue('test.com');
      ParentCompany.getBy.mockResolvedValue(null);
      adminService.createAccountAdmin.mockImplementation((data, callback) =>
        callback(null, error),
      );

      await AdminController.createAccountAdmin(mockReq, mockRes, mockNext);

      expect(adminService.createAccountAdmin).toHaveBeenCalledWith(
        userData,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(error);
    });

    it('should handle thrown error in createAccountAdmin', async () => {
      const userData = {
        basicDetails: { email: '<EMAIL>' },
        domainDetails: { name: 'testdomain' },
        projectCount: 5,
      };

      mockReq.body = userData;
      User.findOne.mockRejectedValue(new Error('Database error'));

      await AdminController.createAccountAdmin(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  describe('show', () => {
    it('should return user successfully', async () => {
      const user = { id: 1, email: '<EMAIL>' };
      mockReq.params = { id: '1' };
      User.getBy.mockResolvedValue(user);
      mockUserSerializer.serialize.mockReturnValue({ ...user, serialized: true });

      await AdminController.show(mockReq, mockRes);

      expect(User.getBy).toHaveBeenCalledWith({ id: '1' });
      expect(mockUserSerializer.serialize).toHaveBeenCalledWith(user);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ user: { ...user, serialized: true } });
    });

    it('should return not found when user does not exist', async () => {
      mockReq.params = { id: '999' };
      User.getBy.mockResolvedValue(null);

      await AdminController.show(mockReq, mockRes);

      expect(User.getBy).toHaveBeenCalledWith({ id: '999' });
      expect(mockRes.status).toHaveBeenCalledWith(status.NOT_FOUND);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'User not found.' });
    });
  });

  describe('update', () => {
    it('should update user successfully', async () => {
      const user = { id: 1, email: '<EMAIL>', update: jest.fn() };
      const updatedUser = { id: 1, email: '<EMAIL>' };

      mockReq.params = { id: '1' };
      mockReq.body = { email: '<EMAIL>' };
      User.getBy.mockResolvedValue(user);
      user.update.mockResolvedValue(updatedUser);
      mockUserSerializer.serialize.mockReturnValue({ ...updatedUser, serialized: true });

      await AdminController.update(mockReq, mockRes);

      expect(User.getBy).toHaveBeenCalledWith({ id: '1' });
      expect(user.update).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(mockUserSerializer.serialize).toHaveBeenCalledWith(updatedUser);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ user: { ...updatedUser, serialized: true } });
    });

    it('should return not found when user does not exist', async () => {
      mockReq.params = { id: '999' };
      User.getBy.mockResolvedValue(null);

      await AdminController.update(mockReq, mockRes);

      expect(User.getBy).toHaveBeenCalledWith({ id: '999' });
      expect(mockRes.status).toHaveBeenCalledWith(status.NOT_FOUND);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'User not found.' });
    });
  });

  describe('delete', () => {
    it('should delete user successfully', async () => {
      const user = { id: 1, email: '<EMAIL>', update: jest.fn() };
      const deletedUser = { id: 1, email: '<EMAIL>', isDeleted: true };

      mockReq.params = { id: '1' };
      User.getBy.mockResolvedValue(user);
      user.update.mockResolvedValue(deletedUser);
      mockUserSerializer.serialize.mockReturnValue({ ...deletedUser, serialized: true });

      await AdminController.delete(mockReq, mockRes);

      expect(User.getBy).toHaveBeenCalledWith({ id: '1' });
      expect(user.update).toHaveBeenCalledWith({ isDeleted: true });
      expect(mockUserSerializer.serialize).toHaveBeenCalledWith(deletedUser);
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({ user: { ...deletedUser, serialized: true } });
    });

    it('should return not found when user does not exist', async () => {
      mockReq.params = { id: '999' };
      User.getBy.mockResolvedValue(null);

      await AdminController.delete(mockReq, mockRes);

      expect(User.getBy).toHaveBeenCalledWith({ id: '999' });
      expect(mockRes.status).toHaveBeenCalledWith(status.NOT_FOUND);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'User not found.' });
    });
  });
});
