/* eslint-disable no-restricted-syntax */
/* eslint-disable no-loop-func */
/* eslint-disable no-await-in-loop */
/* eslint-disable no-new */
const moment = require('moment');
const Moment = require('moment');
const momenttz = require('moment-timezone');
const MomentRange = require('moment-range');
const httpStatus = require('http-status');

const momentRange = MomentRange.extendMoment(Moment);
const ExcelJS = require('exceljs');
const { Worker } = require('worker_threads');
const { stringify } = require('flatted');
const path = require('path');
const Cryptr = require('cryptr');
const MAILER = require('../mailer');

const bulkNdrProcess = path.join(__dirname, './bulkNdrProcess.js');
const ApiError = require('../helpers/apiError');
const {
    Sequelize,
    Enterprise,
    NotificationPreference,
    NotificationPreferenceItem,
    DigestNotification,
    TimeZone,
    CraneRequest,
    ProjectSettings,
    RequestRecurrenceSeries,
    CalendarSetting,
    LocationNotificationPreferences,
    Locations,
    EquipmentMapping
} = require('../models');
let {
    InspectionRequest,
    Member,
    InspectionPerson,
    InspectionGate,
    InspectionEquipment,
    InspectionCompany,
    Role,
    Gates,
    Equipments,
    DeliverDefineWork,
    Company,
    Project,
    DeliverDefine,
    InspectionHistory,
    DeliveryPersonNotification,
    VoidList,
    User,
    Notification
} = require('../models');
const helper = require('../helpers/domainHelper');
const notificationHelper = require('../helpers/notificationHelper');
const pushNotification = require('../config/fcm');
const calendarSettingsService = require('./calendarSettingsService');
const voidService = require('./voidService');
const concreteRequestService = require('./concreteRequestService');

let publicUser;
let publicMember;
const { Op } = Sequelize;

const inspectionService = {
    // Helper function to validate timezone
    async validateTimeZone(timeZoneId) {
        const eventTimeZone = await TimeZone.findOne({
            where: {
                isDeleted: false,
                id: +timeZoneId,
            },
            attributes: [
                'id',
                'location',
                'isDayLightSavingEnabled',
                'timeZoneOffsetInMinutes',
                'dayLightSavingTimeInMinutes',
                'timezone',
            ],
        });
        return eventTimeZone;
    },

    // Helper function to validate inspection times
    validateInspectionTimes(inspectionData) {
        if (inspectionData.startPicker === inspectionData.endPicker) {
            return { error: true, message: 'Inspection Start time and End time should not be the same' };
        }
        if (inspectionData.startPicker > inspectionData.endPicker) {
            return { error: true, message: 'Please enter From Time lesser than To Time' };
        }
        return { error: false };
    },

    // Helper function to validate inspection window dates
    async validateInspectionWindowDates(inspectionData, eventTimeZone, projectDetails) {
        let startDate;
        let endDate;
        if (inspectionData.recurrence) {
            startDate = await this.compareinspectionDateWithinspectionWindowDate(
                inspectionData.inspectionStart,
                inspectionData.startPicker,
                eventTimeZone.timezone,
                projectDetails.ProjectSettings.inspectionWindowTime,
                projectDetails.ProjectSettings.inspectionWindowTimeUnit,
            );
            endDate = await this.compareinspectionDateWithinspectionWindowDate(
                inspectionData.inspectionEnd,
                inspectionData.endPicker,
                eventTimeZone.timezone,
                projectDetails.ProjectSettings.inspectionWindowTime,
                projectDetails.ProjectSettings.inspectionWindowTimeUnit,
            );
        }

        if (startDate || endDate) {
            if (projectDetails.ProjectSettings.inspectionWindowTime === 0) {
                if (inspectionData.recurrence === 'Does Not Repeat') {
                    return { error: true, message: 'Please enter Future Date/Time' };
                }
                return { error: true, message: 'Please enter Future Start or End Date/Time' };
            }
            return {
                error: true,
                message: `Bookings can not be submitted within ${projectDetails.ProjectSettings.inspectionWindowTime} ${projectDetails.ProjectSettings.inspectionWindowTimeUnit} prior to the event`,
            };
        }
        return { error: false };
    },

    // Helper function to get next inspection ID
    async getNextInspectionId(projectId) {
        const lastIdValue = await InspectionRequest.findOne({
            where: { ProjectId: projectId, isDeleted: false },
            order: [['InspectionId', 'DESC']],
        });
        let id = 0;
        const newValue = JSON.parse(JSON.stringify(lastIdValue));
        if (newValue && newValue.InspectionId !== null && newValue.InspectionId !== undefined) {
            id = newValue.InspectionId;
        }
        return id;
    },

    // Helper function to get next crane request ID
    async getNextCraneRequestId(projectId) {
        let lastData = {};
        lastData = await CraneRequest.findOne({
            where: { ProjectId: +projectId, isDeleted: false },
            order: [['CraneRequestId', 'DESC']],
        });
        const InspectionRequestList = await InspectionRequest.findOne({
            where: {
                ProjectId: +projectId,
                isDeleted: false,
                isAssociatedWithCraneRequest: true,
            },
            order: [['CraneRequestId', 'DESC']],
        });
        if (InspectionRequestList) {
            if (lastData) {
                if (InspectionRequestList.CraneRequestId > lastData.CraneRequestId) {
                    lastData.CraneRequestId = InspectionRequestList.CraneRequestId;
                }
            } else {
                lastData = {};
                lastData.CraneRequestId = InspectionRequestList.CraneRequestId;
            }
        }
        if (lastData) {
            const data = lastData.CraneRequestId;
            lastData.CraneRequestId = 0;
            lastData.CraneRequestId = data + 1;
        } else {
            lastData = {};
            lastData.CraneRequestId = 1;
        }
        let craneId = 0;
        const newId = JSON.parse(JSON.stringify(lastData));
        if (newId && newId.CraneRequestId !== null && newId.CraneRequestId !== undefined) {
            craneId = newId.CraneRequestId;
        }
        return craneId;
    },

    // Helper function to create inspection parameters
    createInspectionParam(inspectionData, id, inspectionStart, inspectionEnd, memberDetails, craneId, recurrenceId) {
        return {
            description: inspectionData.description,
            escort: inspectionData.escort,
            vehicleDetails: inspectionData.vehicleDetails,
            notes: inspectionData.notes,
            InspectionId: id,
            inspectionStart,
            inspectionEnd,
            ProjectId: inspectionData.ProjectId,
            createdBy: memberDetails.id,
            isAssociatedWithCraneRequest: inspectionData.isAssociatedWithCraneRequest,
            requestType: inspectionData.requestType,
            cranePickUpLocation: inspectionData.cranePickUpLocation,
            craneDropOffLocation: inspectionData.craneDropOffLocation,
            CraneRequestId: inspectionData.requestType === 'InspectionRequestWithCrane' ? craneId : null,
            recurrenceId,
            LocationId: inspectionData.LocationId,
            inspectionType: inspectionData.inspectionType,
            OriginationAddress: inspectionData.originationAddress,
            vehicleType: inspectionData.vehicleType
        };
    },

    // Helper function to set approval status
    setApprovalStatus(inspectionParam, memberDetails, roleDetails, accountRoleDetails, projectDetails) {
        if (
            memberDetails.RoleId === roleDetails.id ||
            memberDetails.RoleId === accountRoleDetails.id ||
            memberDetails.isAutoApproveEnabled ||
            projectDetails.ProjectSettings.isAutoApprovalEnabled
        ) {
            inspectionParam.status = 'Approved';
            inspectionParam.approvedBy = memberDetails.id;
            inspectionParam.approved_at = new Date();
        }
        return inspectionParam;
    },

    // Helper function to process daily recurrence
    async processDailyRecurrence(params) {
        const { inspectionData, user, eventTimeZone, totalDays, startId, startCraneId, memberDetails, roleDetails, accountRoleDetails, projectDetails } = params;
        const startTime = inspectionData.startPicker;
        const endTime = inspectionData.endPicker;
        let dailyIndex = 0;
        let id = startId;
        let craneId = startCraneId;
        const eventsArray = [];

        const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
            inspectionData,
            user,
            inspectionData.requestType,
            eventTimeZone.timezone,
        );

        while (dailyIndex < totalDays.length) {
            const data = totalDays[dailyIndex];
            if (
                moment(data).isBetween(
                    moment(inspectionData.inspectionStart),
                    moment(inspectionData.inspectionEnd),
                    null,
                    '[]',
                ) ||
                moment(data).isSame(inspectionData.inspectionStart) ||
                moment(data).isSame(inspectionData.inspectionEnd)
            ) {
                id += 1;
                craneId += 1;
                const date = moment(data).format('MM/DD/YYYY');
                const chosenTimezoneinspectionStart = moment.tz(
                    `${date} ${startTime}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                );
                const chosenTimezoneinspectionEnd = moment.tz(
                    `${date} ${endTime}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                );
                const inspectionStart = chosenTimezoneinspectionStart
                    .clone()
                    .tz('UTC')
                    .format('YYYY-MM-DD HH:mm:ssZ');
                const inspectionEnd = chosenTimezoneinspectionEnd
                    .clone()
                    .tz('UTC')
                    .format('YYYY-MM-DD HH:mm:ssZ');

                let inspectionParam = this.createInspectionParam(
                    inspectionData,
                    id,
                    inspectionStart,
                    inspectionEnd,
                    memberDetails,
                    craneId,
                    recurrenceId
                );

                inspectionParam = this.setApprovalStatus(
                    inspectionParam,
                    memberDetails,
                    roleDetails,
                    accountRoleDetails,
                    projectDetails
                );

                eventsArray.push(inspectionParam);
                dailyIndex += +inspectionData.repeatEveryCount;
            }
        }

        if (eventsArray && eventsArray.length > 0) {
            const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                eventsArray,
                projectDetails,
                'add',
                inspectionData.GateId,
            );
            if (isOverlapping?.error) {
                return {
                    error: true,
                    message: isOverlapping.message,
                };
            }
        }

        return {
            error: false,
            eventsArray,
            lastId: id,
            lastCraneId: craneId
        };
    },

    // Helper function to process weekly recurrence
    async processWeeklyRecurrence(params) {
        const {
            inspectionData, user, eventTimeZone, startId, startCraneId,
            memberDetails, roleDetails, accountRoleDetails, projectDetails
        } = params;

        const { eventsArray, lastId, lastCraneId } = await this.generateWeeklyRecurrenceEvents({
            inspectionData, user, eventTimeZone, startId, startCraneId,
            memberDetails, roleDetails, accountRoleDetails, projectDetails
        });

        // Check for double booking
        if (eventsArray.length > 0) {
            const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                eventsArray, projectDetails, 'add', inspectionData.GateId
            );
            if (isOverlapping?.error) {
                return { error: true, message: isOverlapping.message };
            }
        }

        return { error: false, eventsArray, lastId, lastCraneId };
    },


    async generateWeeklyRecurrenceEvents(params) {
        const {
            inspectionData, user, eventTimeZone, startId, startCraneId,
            memberDetails, roleDetails, accountRoleDetails, projectDetails
        } = params;

        const startTime = inspectionData.startPicker;
        const endTime = inspectionData.endPicker;
        const startDayWeek = moment(inspectionData.inspectionStart).startOf('week');
        const endDayWeek = moment(inspectionData.inspectionEnd).endOf('week');
        const totalDaysOfRecurrence = Array.from(momentRange.range(startDayWeek, endDayWeek).by('day'));

        let id = startId;
        let craneId = startCraneId;
        const eventsArray = [];

        const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
            inspectionData, user, inspectionData.requestType, eventTimeZone.timezone
        );

        const repeatCount = +inspectionData.repeatEveryCount > 1
            ? +inspectionData.repeatEveryCount - 1
            : 1;
        const weekIncrement = +inspectionData.repeatEveryCount > 1 ? 7 : 0;

        for (let indexba = 0; indexba < totalDaysOfRecurrence.length; indexba += weekIncrement * repeatCount) {
            const totalLength = indexba + 6;
            for (let indexb = indexba; indexb <= totalLength; indexb++) {
                const data = totalDaysOfRecurrence[indexb];
                if (
                    data &&
                    !moment(data).isBefore(inspectionData.inspectionStart) &&
                    !moment(data).isAfter(inspectionData.inspectionEnd)
                ) {
                    const day = moment(data).format('dddd');
                    if (inspectionData.days.includes(day)) {
                        id++;
                        craneId++;

                        const date = moment(data).format('MM/DD/YYYY');
                        const inspectionStart = moment.tz(`${date} ${startTime}`, 'MM/DD/YYYY HH:mm', eventTimeZone.timezone)
                            .clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
                        const inspectionEnd = moment.tz(`${date} ${endTime}`, 'MM/DD/YYYY HH:mm', eventTimeZone.timezone)
                            .clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');

                        let inspectionParam = this.createInspectionParam(
                            inspectionData, id, inspectionStart, inspectionEnd, memberDetails, craneId, recurrenceId
                        );

                        inspectionParam = this.setApprovalStatus(
                            inspectionParam, memberDetails, roleDetails, accountRoleDetails, projectDetails
                        );

                        eventsArray.push(inspectionParam);
                    }
                }
            }
        }

        return { eventsArray, lastId: id, lastCraneId: craneId };
    },


    // Helper function to process no-repeat inspection
    async processNoRepeatInspection(params) {
        const { inspectionData, user, eventTimeZone, startId, startCraneId, memberDetails, roleDetails, accountRoleDetails, projectDetails } = params;
        let id = startId + 1;
        let craneId = startCraneId + 1;
        const eventsArray = [];

        const chosenTimezoneinspectionStart = moment.tz(
            `${inspectionData.inspectionStart} ${inspectionData.startPicker}`,
            'YYYY MM DD 00:00:00 HH:mm',
            eventTimeZone.timezone,
        );
        const chosenTimezoneinspectionEnd = moment.tz(
            `${inspectionData.inspectionEnd} ${inspectionData.endPicker}`,
            'YYYY MM DD 00:00:00 HH:mm',
            eventTimeZone.timezone,
        );
        const inspectionStart = chosenTimezoneinspectionStart
            .clone()
            .tz('UTC')
            .format('YYYY-MM-DD HH:mm:ssZ');
        const inspectionEnd = chosenTimezoneinspectionEnd
            .clone()
            .tz('UTC')
            .format('YYYY-MM-DD HH:mm:ssZ');

        const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
            inspectionData,
            user,
            inspectionData.requestType,
            eventTimeZone.timezone,
        );

        let inspectionParam = this.createInspectionParam(
            inspectionData,
            id,
            inspectionStart,
            inspectionEnd,
            memberDetails,
            craneId,
            recurrenceId
        );

        // Add GateId for no-repeat case
        inspectionParam.GateId = inspectionData.GateId;

        inspectionParam = this.setApprovalStatus(
            inspectionParam,
            memberDetails,
            roleDetails,
            accountRoleDetails,
            projectDetails
        );

        eventsArray.push(inspectionParam);

        if (eventsArray && eventsArray.length > 0) {
            const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                eventsArray,
                projectDetails,
                'add',
                inspectionData.GateId,
            );
            if (isOverlapping?.error) {
                return {
                    error: true,
                    message: isOverlapping.message,
                };
            }
        }

        return {
            error: false,
            eventsArray,
            lastId: id,
            lastCraneId: craneId
        };
    },


    // Helper function to process monthly recurrence
    async processMonthlyRecurrence(params) {
        const { inspectionData, user, eventTimeZone, startId, startCraneId, memberDetails, roleDetails, accountRoleDetails, projectDetails } = params;
        const startTime = inspectionData.startPicker;
        const endTime = inspectionData.endPicker;
        let id = startId;
        let craneId = startCraneId;
        const eventsArray = [];

        const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
            inspectionData,
            user,
            inspectionData.requestType,
            eventTimeZone.timezone,
        );

        // Generate all months in the range
        const allMonthsInPeriod = this.getAllMonthsInPeriod(
            inspectionData.inspectionStart,
            inspectionData.inspectionEnd
        );

        // Process each month
        const result = await this.buildMonthlyEvents({
            inspectionData,
            allMonthsInPeriod,
            startTime,
            endTime,
            eventTimeZone,
            id,
            craneId,
            memberDetails,
            roleDetails,
            accountRoleDetails,
            projectDetails,
            recurrenceId
        });

        id = result.id;
        craneId = result.craneId;
        eventsArray.push(...result.eventsArray);

        // Double booking check
        if (eventsArray.length > 0) {
            const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                eventsArray,
                projectDetails,
                'add',
                inspectionData.GateId,
            );
            if (isOverlapping?.error) {
                return { error: true, message: isOverlapping.message };
            }
        }

        return {
            error: false,
            eventsArray,
            lastId: id,
            lastCraneId: craneId
        };
    },

    // Helper function to generate months between start and end
    getAllMonthsInPeriod(startDate, endDate) {
        let start = moment(startDate);
        const end = moment(endDate).endOf('month');
        const months = [];
        while (start.isBefore(end)) {
            months.push(start.format('YYYY-MM'));
            start = start.add(1, 'month');
        }
        return months;
    },

    // Helper function to process monthly events
    async buildMonthlyEvents(params) {
        const { inspectionData, allMonthsInPeriod, startTime, endTime, eventTimeZone, memberDetails, roleDetails, accountRoleDetails, projectDetails, recurrenceId } = params;
        let { id, craneId } = params;
        const eventsArray = [];

        let k = 0;
        while (k < allMonthsInPeriod.length + 1) {
            const currentMonthDates = Array.from(
                { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                (_, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
            );

            if (inspectionData.chosenDateOfMonth) {
                const getDate = currentMonthDates.filter(
                    (value) => moment(value).format('DD') === inspectionData.dateOfMonth,
                );
                if (getDate.length === 1) {
                    if (
                        moment(getDate[0]).isBetween(
                            moment(inspectionData.inspectionStart),
                            moment(inspectionData.inspectionEnd),
                            null,
                            '[]',
                        ) ||
                        moment(getDate[0]).isSame(inspectionData.inspectionStart) ||
                        moment(getDate[0]).isSame(inspectionData.inspectionEnd)
                    ) {
                        id += 1;
                        craneId += 1;
                        const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                        const chosenTimezoneinspectionStart = moment.tz(
                            `${date} ${startTime}`,
                            'MM/DD/YYYY HH:mm',
                            eventTimeZone.timezone,
                        );
                        const chosenTimezoneinspectionEnd = moment.tz(
                            `${date} ${endTime}`,
                            'MM/DD/YYYY HH:mm',
                            eventTimeZone.timezone,
                        );
                        const inspectionStart = chosenTimezoneinspectionStart
                            .clone()
                            .tz('UTC')
                            .format('YYYY-MM-DD HH:mm:ssZ');
                        const inspectionEnd = chosenTimezoneinspectionEnd
                            .clone()
                            .tz('UTC')
                            .format('YYYY-MM-DD HH:mm:ssZ');

                        let inspectionParam = this.createInspectionParam(
                            inspectionData,
                            id,
                            inspectionStart,
                            inspectionEnd,
                            memberDetails,
                            craneId,
                            recurrenceId
                        );

                        inspectionParam = this.setApprovalStatus(
                            inspectionParam,
                            memberDetails,
                            roleDetails,
                            accountRoleDetails,
                            projectDetails
                        );

                        eventsArray.push(inspectionParam);
                    }
                }
            } else if (allMonthsInPeriod[k]) {
                const result = await this.processMonthlyWeekdayRecurrence({
                    inspectionData,
                    monthPeriod: allMonthsInPeriod[k],
                    startTime,
                    endTime,
                    eventTimeZone,
                    id,
                    craneId,
                    memberDetails,
                    roleDetails,
                    accountRoleDetails,
                    projectDetails,
                    recurrenceId
                });
                if (result.inspectionParam) {
                    eventsArray.push(result.inspectionParam);
                    id = result.id;
                    craneId = result.craneId;
                }
            }
            k += +inspectionData.repeatEveryCount;
        }

        return { id, craneId, eventsArray };
    },


    // Helper function to process monthly weekday recurrence
    async processMonthlyWeekdayRecurrence(params) {
        let { inspectionData, monthPeriod, startTime, endTime, eventTimeZone, id, craneId, memberDetails, roleDetails, accountRoleDetails, projectDetails, recurrenceId } = params;
        const dayOfMonth = inspectionData.monthlyRepeatType;
        const week = dayOfMonth.split(' ')[0].toLowerCase();
        const day = dayOfMonth.split(' ')[1].toLowerCase();
        const chosenDay = moment(monthPeriod, 'YYYY-MM')
            .startOf('month')
            .day(day);
        const getAllDays = [];
        if (chosenDay.date() > 7) chosenDay.add(7, 'd');
        const month = chosenDay.month();
        while (month === chosenDay.month()) {
            getAllDays.push(chosenDay.format('YYYY-MM-DD'));
            chosenDay.add(7, 'd');
        }
        let i = 0;
        if (week === 'second') {
            i += 1;
        } else if (week === 'third') {
            i += 2;
        } else if (week === 'fourth') {
            i += 3;
        } else if (week === 'last') {
            i = getAllDays.length - 1;
        }
        const finalDay = getAllDays[i];
        if (
            moment(finalDay).isBetween(
                moment(inspectionData.inspectionStart),
                moment(inspectionData.inspectionEnd),
                null,
                '[]',
            ) ||
            moment(finalDay).isSame(inspectionData.inspectionStart) ||
            moment(finalDay).isSame(inspectionData.inspectionEnd)
        ) {
            id += 1;
            craneId += 1;
            const date = moment(finalDay).format('MM/DD/YYYY');
            const chosenTimezoneinspectionStart = moment.tz(
                `${date} ${startTime}`,
                'MM/DD/YYYY HH:mm',
                eventTimeZone.timezone,
            );
            const chosenTimezoneinspectionEnd = moment.tz(
                `${date} ${endTime}`,
                'MM/DD/YYYY HH:mm',
                eventTimeZone.timezone,
            );
            const inspectionStart = chosenTimezoneinspectionStart
                .clone()
                .tz('UTC')
                .format('YYYY-MM-DD HH:mm:ssZ');
            const inspectionEnd = chosenTimezoneinspectionEnd
                .clone()
                .tz('UTC')
                .format('YYYY-MM-DD HH:mm:ssZ');

            let inspectionParam = this.createInspectionParam(
                inspectionData,
                id,
                inspectionStart,
                inspectionEnd,
                memberDetails,
                craneId,
                recurrenceId
            );

            inspectionParam = this.setApprovalStatus(
                inspectionParam,
                memberDetails,
                roleDetails,
                accountRoleDetails,
                projectDetails
            );

            return { inspectionParam, id, craneId };
        }
        return { inspectionParam: null, id, craneId };
    },

    // Helper function to process yearly recurrence
    async processYearlyRecurrence(params) {
        const { inspectionData, user, eventTimeZone, startId, startCraneId, memberDetails, roleDetails, accountRoleDetails, projectDetails } = params;
        const startTime = inspectionData.startPicker;
        const endTime = inspectionData.endPicker;
        let id = startId;
        let craneId = startCraneId;
        const eventsArray = [];

        const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
            inspectionData,
            user,
            inspectionData.requestType,
            eventTimeZone.timezone,
        );

        let startDate1 = moment(inspectionData.inspectionStart);
        const endDate1 = moment(inspectionData.inspectionEnd).endOf('month');
        const allMonthsInPeriod = [];

        while (startDate1.isBefore(endDate1)) {
            allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
            startDate1 = startDate1.add(12, 'month');
        }

        for (let k = 0; k < allMonthsInPeriod.length + 1; k += 1) {
            const currentMonthDates = Array.from(
                { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                (_, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
            );

            if (inspectionData.chosenDateOfMonth) {
                const result = await this.processYearlyDateRecurrence({
                    inspectionData,
                    currentMonthDates,
                    startTime,
                    endTime,
                    eventTimeZone,
                    id,
                    craneId,
                    memberDetails,
                    roleDetails,
                    accountRoleDetails,
                    projectDetails,
                    recurrenceId
                });
                if (result.inspectionParam) {
                    eventsArray.push(result.inspectionParam);
                    id = result.id;
                    craneId = result.craneId;
                }
            } else if (allMonthsInPeriod[k]) {
                const result = await this.processMonthlyWeekdayRecurrence({
                    inspectionData,
                    monthPeriod: allMonthsInPeriod[k],
                    startTime,
                    endTime,
                    eventTimeZone,
                    id,
                    craneId,
                    memberDetails,
                    roleDetails,
                    accountRoleDetails,
                    projectDetails,
                    recurrenceId
                });
                if (result.inspectionParam) {
                    eventsArray.push(result.inspectionParam);
                    id = result.id;
                    craneId = result.craneId;
                }
            }
        }

        if (eventsArray && eventsArray.length > 0) {
            const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                eventsArray,
                projectDetails,
                'add',
                inspectionData.GateId,
            );
            if (isOverlapping?.error) {
                return {
                    error: true,
                    message: isOverlapping.message,
                };
            }
        }

        return {
            error: false,
            eventsArray,
            lastId: id,
            lastCraneId: craneId
        };
    },

    // Helper function to process yearly date recurrence
    async processYearlyDateRecurrence(params) {
        let { inspectionData, currentMonthDates, startTime, endTime, eventTimeZone, id, craneId, memberDetails, roleDetails, accountRoleDetails, projectDetails, recurrenceId } = params;
        const getDate = currentMonthDates.filter(
            (value) => moment(value).format('DD') === inspectionData.dateOfMonth,
        );
        if (getDate.length === 1) {
            if (
                moment(getDate[0]).isBetween(
                    moment(inspectionData.inspectionStart),
                    moment(inspectionData.inspectionEnd),
                    null,
                    '[]',
                ) ||
                moment(getDate[0]).isSame(inspectionData.inspectionStart) ||
                moment(getDate[0]).isSame(inspectionData.inspectionEnd)
            ) {
                id += 1;
                craneId += 1;
                const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                const chosenTimezoneinspectionStart = moment.tz(
                    `${date} ${startTime}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                );
                const chosenTimezoneinspectionEnd = moment.tz(
                    `${date} ${endTime}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                );
                const inspectionStart = chosenTimezoneinspectionStart
                    .clone()
                    .tz('UTC')
                    .format('YYYY-MM-DD HH:mm:ssZ');
                const inspectionEnd = chosenTimezoneinspectionEnd
                    .clone()
                    .tz('UTC')
                    .format('YYYY-MM-DD HH:mm:ssZ');

                let inspectionParam = this.createInspectionParam(
                    inspectionData,
                    id,
                    inspectionStart,
                    inspectionEnd,
                    memberDetails,
                    craneId,
                    recurrenceId
                );

                inspectionParam = this.setApprovalStatus(
                    inspectionParam,
                    memberDetails,
                    roleDetails,
                    accountRoleDetails,
                    projectDetails
                );

                return { inspectionParam, id, craneId };
            }
        }
        return { inspectionParam: null, id, craneId };
    },

    // Helper function to create inspection parameter for recurrence events
    createRecurrenceInspectionParam(inspectionData, inspectionStart, inspectionEnd, memberDetails, recurrenceId) {
        return {
            description: inspectionData.description,
            escort: inspectionData.escort,
            vehicleDetails: inspectionData.vehicleDetails,
            notes: inspectionData.notes,
            InspectionId: null,
            inspectionStart,
            inspectionEnd,
            ProjectId: inspectionData.ProjectId,
            createdBy: memberDetails.id,
            isAssociatedWithCraneRequest: inspectionData.isAssociatedWithCraneRequest,
            requestType: inspectionData.requestType,
            cranePickUpLocation: inspectionData.cranePickUpLocation,
            craneDropOffLocation: inspectionData.craneDropOffLocation,
            CraneRequestId: null,
            recurrenceId,
            LocationId: inspectionData.LocationId,
            inspectionType: inspectionData.inspectionType,
            OriginationAddress: inspectionData.originationAddress,
            vehicleType: inspectionData.vehicleType
        };
    },

    // Helper function to set approval status for recurrence events
    setRecurrenceApprovalStatus(inspectionParam, memberDetails, roleDetails, accountRoleDetails, projectDetails) {
        if (
            memberDetails.RoleId === roleDetails.id ||
            memberDetails.RoleId === accountRoleDetails.id ||
            memberDetails.isAutoApproveEnabled ||
            projectDetails.ProjectSettings.isAutoApprovalEnabled
        ) {
            inspectionParam.status = 'Approved';
            inspectionParam.approvedBy = memberDetails.id;
            inspectionParam.approved_at = new Date();
        }
        return inspectionParam;
    },

    // Helper function to convert date and time to UTC format
    convertToUTCFormat(date, startTime, endTime, timezone) {
        const chosenTimezoneinspectionStart = moment.tz(
            `${date} ${startTime}`,
            'MM/DD/YYYY HH:mm',
            timezone,
        );
        const chosenTimezoneinspectionEnd = moment.tz(
            `${date} ${endTime}`,
            'MM/DD/YYYY HH:mm',
            timezone,
        );
        const inspectionStart = chosenTimezoneinspectionStart
            .clone()
            .tz('UTC')
            .format('YYYY-MM-DD HH:mm:ssZ');
        const inspectionEnd = chosenTimezoneinspectionEnd
            .clone()
            .tz('UTC')
            .format('YYYY-MM-DD HH:mm:ssZ');

        return { inspectionStart, inspectionEnd };
    },

    // Helper function to check overlap and return result
    async checkOverlapAndReturnResult(eventsArray, projectDetails, inspectionData, done) {
        if (eventsArray && eventsArray.length > 0) {
            const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                eventsArray,
                projectDetails,
                'edit',
                inspectionData.GateId,
            );
            if (isOverlapping?.error) {
                return done(null, {
                    message: isOverlapping.message,
                });
            }
            return done(eventsArray, false);
        }
    },

    // Helper function to process daily recurrence for createNewRecurrenceEvents
    async processRecurrenceDailyEvents(inspectionData, totalDays, memberDetails, roleDetails, accountRoleDetails, projectDetails, done) {
        const eventsArray = [];
        const startTime = inspectionData.inspectionStartTime;
        const endTime = inspectionData.inspectionEndTime;
        const { recurrenceId } = inspectionData;
        let dailyIndex = 0;

        while (dailyIndex < totalDays.length) {
            const data = totalDays[dailyIndex];
            if (this.isDateInRecurrenceRange(data, inspectionData)) {
                const date = moment(data).format('MM/DD/YYYY');
                const { inspectionStart, inspectionEnd } = this.convertToUTCFormat(
                    date, startTime, endTime, inspectionData.timezone
                );

                let inspectionParam = this.createRecurrenceInspectionParam(
                    inspectionData, inspectionStart, inspectionEnd, memberDetails, recurrenceId
                );

                inspectionParam = this.setRecurrenceApprovalStatus(
                    inspectionParam, memberDetails, roleDetails, accountRoleDetails, projectDetails
                );

                eventsArray.push(inspectionParam);
            }
            dailyIndex += +inspectionData.repeatEveryCount;
        }

        return this.checkOverlapAndReturnResult(eventsArray, projectDetails, inspectionData, done);
    },

    // Helper function to check if date is in recurrence range
    isDateInRecurrenceRange(data, inspectionData) {
        return (
            moment(data).isBetween(
                moment(inspectionData.inspectionStart),
                moment(inspectionData.recurrenceEndDate).add(1, 'day'),
                null,
                '[]',
            ) ||
            moment(data).isSame(inspectionData.inspectionStart) ||
            moment(data).isSame(inspectionData.recurrenceEndDate).add(1, 'day')
        );
    },

    // Helper function to process weekly recurrence for createNewRecurrenceEvents
    async processRecurrenceWeeklyEvents(inspectionData, memberDetails, roleDetails, accountRoleDetails, projectDetails, done) {
        const eventsArray = [];
        const startTime = inspectionData.inspectionStartTime;
        const endTime = inspectionData.inspectionEndTime;
        const { recurrenceId } = inspectionData;

        const startDayWeek = moment(inspectionData.inspectionStart).startOf('week');
        const endDayWeek = moment(inspectionData.recurrenceEndDate).endOf('week');
        const range1 = momentRange.range(moment(startDayWeek), moment(endDayWeek));
        const totalDaysOfRecurrence = Array.from(range1.by('day'));

        const { count, weekIncrement } = this.calculateWeeklyIncrements(inspectionData.repeatEveryCount);

        for (
            let indexba = 0;
            indexba < totalDaysOfRecurrence.length;
            indexba += weekIncrement * count
        ) {
            const totalLength = indexba + 6;
            for (let indexb = indexba; indexb <= totalLength; indexb += 1) {
                const data = totalDaysOfRecurrence[indexb];
                if (this.isValidWeeklyDate(data, inspectionData)) {
                    const day = moment(data).format('dddd');
                    const indexVal = inspectionData.days.includes(day);
                    if (indexVal) {
                        const date = moment(data).format('MM/DD/YYYY');
                        const { inspectionStart, inspectionEnd } = this.convertToUTCFormat(
                            date, startTime, endTime, inspectionData.timezone
                        );

                        let inspectionParam = this.createRecurrenceInspectionParam(
                            inspectionData, inspectionStart, inspectionEnd, memberDetails, recurrenceId
                        );

                        inspectionParam = this.setRecurrenceApprovalStatus(
                            inspectionParam, memberDetails, roleDetails, accountRoleDetails, projectDetails
                        );

                        eventsArray.push(inspectionParam);
                    }
                }
            }
        }

        return this.checkOverlapAndReturnResult(eventsArray, projectDetails, inspectionData, done);
    },

    // Helper function to calculate weekly increments
    calculateWeeklyIncrements(repeatEveryCount) {
        let count, weekIncrement;
        if (+repeatEveryCount > 1) {
            count = +repeatEveryCount - 1;
            weekIncrement = 7;
        } else {
            count = 1;
            weekIncrement = 0;
        }
        return { count, weekIncrement };
    },

    // Helper function to validate weekly date
    isValidWeeklyDate(data, inspectionData) {
        return (
            data &&
            !moment(data).isBefore(inspectionData.inspectionStart) &&
            !moment(data).isAfter(inspectionData.recurrenceEndDate)
        );
    },

    // Helper function to process monthly recurrence for createNewRecurrenceEvents
    async processRecurrenceMonthlyEvents(inspectionData, memberDetails, roleDetails, accountRoleDetails, projectDetails, done) {
        const eventsArray = [];
        const startTime = inspectionData.inspectionStartTime;
        const endTime = inspectionData.inspectionEndTime;
        const { recurrenceId } = inspectionData;

        const allMonthsInPeriod = this.getAllMonthsInPeriodData(inspectionData);
        let k = 0;

        while (k < allMonthsInPeriod.length + 1) {
            const currentMonthDates = this.getCurrentMonthDates(allMonthsInPeriod[k]);

            if (inspectionData.chosenDateOfMonth) {
                const result = this.handleChosenDateOfMonth({
                    inspectionData, currentMonthDates, startTime, endTime,
                    memberDetails, roleDetails, accountRoleDetails, projectDetails, recurrenceId
                });
                if (result) eventsArray.push(result);
            } else if (allMonthsInPeriod[k]) {
                const result = this.handleMonthlyRepeatType({
                    inspectionData, monthPeriod: allMonthsInPeriod[k], startTime, endTime,
                    memberDetails, roleDetails, accountRoleDetails, projectDetails, recurrenceId
                }
                );
                if (result) eventsArray.push(result);
            }
            k += +inspectionData.repeatEveryCount;
        }

        return this.checkOverlapAndReturnResult(eventsArray, projectDetails, inspectionData, done);
    },

    // Helper function to get all months in period
    getAllMonthsInPeriodData(inspectionData) {
        let startDate1 = moment(inspectionData.inspectionStart);
        const endDate1 = moment(inspectionData.recurrenceEndDate).endOf('month');
        const allMonthsInPeriod = [];

        while (startDate1.isBefore(endDate1)) {
            allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
            startDate1 = startDate1.add(1, 'month');
        }
        return allMonthsInPeriod;
    },

    // Helper function to get current month dates
    getCurrentMonthDates(monthPeriod) {
        return Array.from(
            { length: moment(monthPeriod, 'YYYY-MM').daysInMonth() },
            (_, j) => moment(monthPeriod, 'YYYY-MM').startOf('month').add(j, 'days'),
        );
    },

    // Helper function to handle chosen date of month
    handleChosenDateOfMonth(params) {
        const { inspectionData, currentMonthDates, startTime, endTime, memberDetails, roleDetails, accountRoleDetails, projectDetails, recurrenceId } = params;
        const getDate = currentMonthDates.filter(
            (value) => moment(value).format('DD') === inspectionData.dateOfMonth,
        );

        if (getDate.length === 1 && this.isDateInRecurrenceRange(getDate[0], inspectionData)) {
            const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
            const { inspectionStart, inspectionEnd } = this.convertToUTCFormat(
                date, startTime, endTime, inspectionData.timezone
            );

            let inspectionParam = this.createRecurrenceInspectionParam(
                inspectionData, inspectionStart, inspectionEnd, memberDetails, recurrenceId
            );

            return this.setRecurrenceApprovalStatus(
                inspectionParam, memberDetails, roleDetails, accountRoleDetails, projectDetails
            );
        }
        return null;
    },

    // Helper function to handle monthly repeat type
    handleMonthlyRepeatType(params) {
        const { inspectionData, monthPeriod, startTime, endTime, memberDetails, roleDetails, accountRoleDetails, projectDetails, recurrenceId } = params;
        const dayOfMonth = inspectionData.monthlyRepeatType;
        const week = dayOfMonth.split(' ')[0].toLowerCase();
        const day = dayOfMonth.split(' ')[1].toLowerCase();

        const chosenDay = moment(monthPeriod, 'YYYY-MM').startOf('month').day(day);
        const getAllDays = this.getAllDaysInMonth(chosenDay);
        const finalDay = this.getFinalDayByWeek(getAllDays, week);

        if (this.isDateInRecurrenceRange(finalDay, inspectionData)) {
            const date = moment(finalDay).format('MM/DD/YYYY');
            const { inspectionStart, inspectionEnd } = this.convertToUTCFormat(
                date, startTime, endTime, inspectionData.timezone
            );

            let inspectionParam = this.createRecurrenceInspectionParam(
                inspectionData, inspectionStart, inspectionEnd, memberDetails, recurrenceId
            );

            return this.setRecurrenceApprovalStatus(
                inspectionParam, memberDetails, roleDetails, accountRoleDetails, projectDetails
            );
        }
        return null;
    },

    // Helper function to get all days in month for a specific weekday
    getAllDaysInMonth(chosenDay) {
        const getAllDays = [];
        if (chosenDay.date() > 7) chosenDay.add(7, 'd');
        const month = chosenDay.month();
        while (month === chosenDay.month()) {
            getAllDays.push(chosenDay.format('YYYY-MM-DD'));
            chosenDay.add(7, 'd');
        }
        return getAllDays;
    },

    // Helper function to get final day by week position
    getFinalDayByWeek(getAllDays, week) {
        let i = 0;
        if (week === 'second') {
            i += 1;
        } else if (week === 'third') {
            i += 2;
        } else if (week === 'fourth') {
            i += 3;
        } else if (week === 'last') {
            i = getAllDays.length - 1;
        }
        return getAllDays[i];
    },

    // Helper function to process yearly recurrence for createNewRecurrenceEvents
    async processRecurrenceYearlyEvents(inspectionData, memberDetails, roleDetails, accountRoleDetails, projectDetails, done) {
        const eventsArray = [];
        const startTime = inspectionData.inspectionStartTime;
        const endTime = inspectionData.inspectionEndTime;
        const { recurrenceId } = inspectionData;

        const allMonthsInPeriod = this.getAllYearlyMonthsInPeriod(inspectionData);

        for (let k = 0; k < allMonthsInPeriod.length + 1; k += 1) {
            const currentMonthDates = this.getCurrentMonthDates(allMonthsInPeriod[k]);

            if (inspectionData.chosenDateOfMonth) {
                const result = this.handleChosenDateOfMonth({
                    inspectionData, currentMonthDates, startTime, endTime,
                    memberDetails, roleDetails, accountRoleDetails, projectDetails, recurrenceId
                });
                if (result) eventsArray.push(result);
            } else if (allMonthsInPeriod[k]) {
                const result = this.handleMonthlyRepeatType({
                    inspectionData, monthPeriod: allMonthsInPeriod[k], startTime, endTime,
                    memberDetails, roleDetails, accountRoleDetails, projectDetails, recurrenceId
                }
                );
                if (result) eventsArray.push(result);
            }
        }

        return this.checkOverlapAndReturnResult(eventsArray, projectDetails, inspectionData, done);
    },

    // Helper function to get all months in period for yearly recurrence
    getAllYearlyMonthsInPeriod(inspectionData) {
        let startDate1 = moment(inspectionData.inspectionStart);
        const endDate1 = moment(inspectionData.recurrenceEndDate).endOf('month');
        const allMonthsInPeriod = [];

        while (startDate1.isBefore(endDate1)) {
            allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
            startDate1 = startDate1.add(12, 'month');
        }
        return allMonthsInPeriod;
    },

    // Helper function to process recurrence events based on type
    async processRecurrenceEvents(params) {
        const { inspectionData, inputData, eventTimeZone, totalDays, id, craneId, memberDetails, roleDetails, accountRoleDetails, projectDetails } = params;
        const eventsArray = [];
        let currentId = id;
        let currentCraneId = craneId;

        if (inspectionData.recurrence === 'Daily') {
            const result = await this.processDailyRecurrence({
                inspectionData, user: inputData.user, eventTimeZone, totalDays,
                currentId, currentCraneId, memberDetails, roleDetails, accountRoleDetails, projectDetails
            }
            );
            if (result.error) return { error: true, message: result.message };
            eventsArray.push(...result.eventsArray);
            currentId = result.lastId;
            currentCraneId = result.lastCraneId;
        } else if (inspectionData.recurrence === 'Weekly') {
            const result = await this.processWeeklyRecurrence({
                inspectionData, user: inputData.user, eventTimeZone,
                currentId, currentCraneId, memberDetails, roleDetails, accountRoleDetails, projectDetails
            }
            );
            if (result.error) return { error: true, message: result.message };
            eventsArray.push(...result.eventsArray);
            currentId = result.lastId;
            currentCraneId = result.lastCraneId;
        } else if (inspectionData.recurrence === 'Monthly') {
            const result = await this.processMonthlyRecurrence({
                inspectionData, user: inputData.user, eventTimeZone,
                currentId, currentCraneId, memberDetails, roleDetails, accountRoleDetails, projectDetails
            }
            );
            if (result.error) return { error: true, message: result.message };
            eventsArray.push(...result.eventsArray);
            currentId = result.lastId;
            currentCraneId = result.lastCraneId;
        } else if (inspectionData.recurrence === 'Yearly') {
            const result = await this.processYearlyRecurrence({
                inspectionData, user: inputData.user, eventTimeZone,
                currentId, currentCraneId, memberDetails, roleDetails, accountRoleDetails, projectDetails
            });
            if (result.error) return { error: true, message: result.message };
            eventsArray.push(...result.eventsArray);
            currentId = result.lastId;
            currentCraneId = result.lastCraneId;
        } else if (inspectionData.recurrence === 'Does Not Repeat') {
            const result = await this.processNoRepeatInspection({
                inspectionData, user: inputData.user, eventTimeZone,
                currentId, currentCraneId, memberDetails, roleDetails, accountRoleDetails, projectDetails
            }
            );
            if (result.error) return { error: true, message: result.message };
            eventsArray.push(...result.eventsArray);
            currentId = result.lastId;
            currentCraneId = result.lastCraneId;
        }

        return { error: false, eventsArray, lastId: currentId, lastCraneId: currentCraneId };
    },

    // Helper function to create inspection associations
    async createInspectionAssociations(inspectionData, updateParam) {
        const { companies, persons, define } = inspectionData;
        const gates = [inspectionData.GateId];
        const equipments = inspectionData.EquipmentId;

        // Create company associations
        for (const element of companies) {
            const companyParam = { ...updateParam, CompanyId: element };
            await InspectionCompany.createInstance(companyParam);
        }

        // Create gate associations
        for (const element of gates) {
            const gateParam = { ...updateParam, GateId: element };
            await InspectionGate.createInstance(gateParam);
        }

        // Create equipment associations
        for (const element of equipments) {
            const equipmentParam = { ...updateParam, EquipmentId: element };
            await InspectionEquipment.createInstance(equipmentParam);
        }

        // Create person associations
        for (const element of persons) {
            const memberParam = { ...updateParam, MemberId: element };
            await InspectionPerson.createInstance(memberParam);
        }

        // Create define associations
        for (const element of define) {
            const defineParam = { ...updateParam, DeliverDefineWorkId: element };
            await DeliverDefine.createInstance(defineParam);
        }
    },

    // Helper function to create inspection history
    async createInspectionHistory(newInspectionData, memberDetails, loginUser, inspectionData) {
        const history = {
            InspectionRequestId: newInspectionData.id,
            InspectionId: newInspectionData.InspectionId,
            MemberId: memberDetails.id,
            type: 'create',
            description: `${loginUser.firstName} ${loginUser.lastName} Created Inspection Booking, ${inspectionData.description}.`,
        };
        await InspectionHistory.createInstance(history);

        if (newInspectionData.status === 'Approved') {
            const approvalHistory = {
                ProjectId: inspectionData.ProjectId,
                MemberId: memberDetails.id,
                InspectionRequestId: newInspectionData.id,
                isDeleted: false,
                type: 'approved',
                description: `${loginUser.firstName} ${loginUser.lastName} Approved Inspection Booking, ${inspectionData.description}.`,
            };
            await InspectionHistory.createInstance(approvalHistory);
        }
    },

    // Helper function to process events array and create inspections
    async processInspectionEvents(eventsArray, inspectionData, memberDetails, loginUser) {
        let newInspectionData = {};

        for (const event of eventsArray) {
            newInspectionData = await InspectionRequest.createInstance(event);

            // Create equipment mapping
            const mappingPayload = {
                EquipmentId: inspectionData.EquipmentId,
                startTime: event.inspectionStart,
                endTime: event.inspectionEnd,
                GateId: inspectionData.GateId,
                LocationId: event.LocationId,
                InspectionId: newInspectionData.id
            };
            await this.createEquipmentMapping(mappingPayload);

            // Create associations
            const updateParam = {
                InspectionId: newInspectionData.id,
                InspectionCode: newInspectionData.InspectionId,
                ProjectId: inspectionData.ProjectId,
            };
            await this.createInspectionAssociations(inspectionData, updateParam);

            // Create history
            await this.createInspectionHistory(newInspectionData, memberDetails, loginUser, inspectionData);
        }

        return newInspectionData;
    },

    // Helper function to create notification and get location preferences
    async createNotificationAndGetPreferences(inspectionData, newInspectionData, memberDetails, loginUser) {
        const locationChosen = await Locations.findOne({
            where: {
                ProjectId: inspectionData.ProjectId,
                id: inspectionData.LocationId,
            },
        });

        const history = {
            InspectionRequestId: newInspectionData.id,
            InspectionId: newInspectionData.InspectionId,
            MemberId: memberDetails.id,
            type: 'create',
            description: `${loginUser.firstName} ${loginUser.lastName} Created Inspection Booking, ${inspectionData.description}.`,
            locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Created Inspection Booking, ${inspectionData.description}. Location: ${locationChosen.locationPath}.`,
            memberData: [],
        };

        const notification = { ...history };
        notification.ProjectId = inspectionData.ProjectId;
        notification.LocationId = inspectionData.LocationId;
        notification.title = 'Inspection Booking Creation';
        notification.recurrenceType = `${inspectionData.recurrence} From ${moment(
            inspectionData.inspectionStart,
        ).format('MM/DD/YYYY')} to ${moment(inspectionData.inspectionEnd).format('MM/DD/YYYY')}`;
        notification.requestType = 'InspectionRequest';

        const newNotification = await Notification.createInstance(notification);

        const memberLocationPreference = await LocationNotificationPreferences.findAll({
            where: {
                ProjectId: inspectionData.ProjectId,
                LocationId: inspectionData.LocationId,
                follow: true,
            },
            include: [
                {
                    association: 'Member',
                    attributes: ['id', 'RoleId'],
                    where: {
                        [Op.and]: [
                            {
                                id: { [Op.ne]: memberDetails.id },
                            },
                        ],
                    },
                    include: [
                        {
                            association: 'User',
                            attributes: ['id', 'firstName', 'lastName', 'email'],
                        },
                    ],
                },
            ],
        });

        return { history, newNotification, memberLocationPreference };
    },

    // Helper function to get admin data for notifications
    async getAdminDataForNotifications(inspectionData, newNotification, memberLocationPreference) {
        const locationFollowMembers = [];
        memberLocationPreference.forEach(async (element) => {
            locationFollowMembers.push(element.Member.id);
        });

        const adminData = await Member.findAll({
            where: {
                [Op.and]: [
                    { ProjectId: inspectionData.ProjectId },
                    { isDeleted: false },
                    { id: { [Op.in]: inspectionData.persons } },
                    { id: { [Op.ne]: newNotification.MemberId } },
                    { id: { [Op.notIn]: locationFollowMembers } },
                ],
            },
            include: [
                {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName', 'email'],
                },
            ],
            attributes: ['id', 'RoleId'],
        });

        return adminData;
    },

    // Helper function to handle notifications and email sending
    async handleNotifications(params) {
        const { history, newNotification, memberLocationPreference, adminData, projectDetails, newInspectionData, inspectionData, memberDetails, loginUser } = params;
        if (memberLocationPreference && memberLocationPreference.length > 0) {
            // Send push notifications
            await pushNotification.sendMemberLocationPreferencePushNotification(
                memberLocationPreference,
                newInspectionData.InspectionRequestId,
                history.locationFollowDescription,
                newInspectionData.requestType,
                newInspectionData.ProjectId,
                newInspectionData.id,
                3,
            );

            // Create in-app notifications
            await notificationHelper.createMemberDeliveryLocationInAppNotification(
                DeliveryPersonNotification,
                inspectionData.ProjectId,
                newNotification.id,
                memberLocationPreference,
                3,
            );
        }

        // Set additional history data
        history.adminData = adminData;
        history.firstName = loginUser.firstName;
        history.profilePic = loginUser.profilePic;
        history.createdAt = new Date();
        history.ProjectId = inspectionData.ProjectId;
        history.projectName = projectDetails.projectName;

        // Create delivery person notification
        await notificationHelper.createDeliveryPersonNotification(
            adminData,
            [],
            projectDetails,
            newNotification,
            DeliveryPersonNotification,
            memberDetails,
            loginUser,
            3,
            'created a',
            'Inspection Request',
            `Inspection Booking (${newInspectionData.InspectionId} - ${newInspectionData.description})`,
            newInspectionData.id,
        );

        // Get notification preferences
        const checkMemberNotification = await NotificationPreference.findAll({
            where: {
                ProjectId: inspectionData.ProjectId,
                isDeleted: false,
            },
            attributes: [
                'id',
                'MemberId',
                'ProjectId',
                'ParentCompanyId',
                'NotificationPreferenceItemId',
                'instant',
                'dailyDigest',
            ],
            include: [
                {
                    association: 'NotificationPreferenceItem',
                    where: {
                        id: 3,
                        isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                },
            ],
        });

        history.notificationPreference = checkMemberNotification;

        // Send device token notifications
        await pushNotification.sendDeviceTokenForInspection(history, 3, inspectionData.ProjectId);

        // Send email notifications
        await this.sendEmailNotificationToUser(
            history,
            memberDetails,
            loginUser,
            newInspectionData,
            inspectionData,
            memberLocationPreference,
        );

        return history;
    },

    async newRequest(inputData, done) {
        try {
            await this.getDynamicModel(inputData);
            const eventTimeZone = await this.validateTimeZone(inputData.body.TimeZoneId);
            if (!eventTimeZone) {
                return done(null, { message: 'Provide a valid timezone' });
            }

            const inspectionData = inputData.body;
            const loginUser = inputData.user;
            const projectDetails = await Project.getProjectAndSettings({
                isDeleted: false,
                id: +inspectionData.ProjectId,
            });

            // Validate inspection times
            const timeValidation = this.validateInspectionTimes(inspectionData);
            if (timeValidation.error) {
                return done(null, { message: timeValidation.message });
            }

            // Validate inspection window dates
            const windowValidation = await this.validateInspectionWindowDates(inspectionData, eventTimeZone, projectDetails);
            if (windowValidation.error) {
                return done(null, { message: windowValidation.message });
            }

            if (projectDetails?.ProjectSettings) {
                this.checkInputDatas(inputData, async (checkResponse, checkError) => {
                    if (checkError) {
                        return done(null, checkError);
                    }

                    const memberDetails = await Member.getBy({
                        UserId: loginUser.id,
                        ProjectId: inspectionData.ProjectId,
                        isActive: true,
                        isDeleted: false,
                    });

                    if (!memberDetails) {
                        return done(null, {
                            message: 'You are not allowed create Inspection Booking for this project.',
                        });
                    }

                    const range = momentRange.range(
                        moment(inspectionData.inspectionStart),
                        moment(inspectionData.inspectionEnd),
                    );
                    const totalDays = Array.from(range.by('day'));

                    const id = await this.getNextInspectionId(memberDetails.ProjectId);
                    const craneId = await this.getNextCraneRequestId(memberDetails.ProjectId);

                    const roleDetails = await Role.getBy('Project Admin');
                    const accountRoleDetails = await Role.getBy('Account Admin');

                    // Process recurrence events
                    const recurrenceResult = await this.processRecurrenceEvents({
                        inspectionData,
                        inputData,
                        eventTimeZone,
                        totalDays,
                        id,
                        craneId,
                        memberDetails,
                        roleDetails,
                        accountRoleDetails,
                        projectDetails
                    });

                    if (recurrenceResult.error) {
                        return done(null, { message: recurrenceResult.message });
                    }

                    const { eventsArray } = recurrenceResult;

                    if (eventsArray.length === 0) {
                        return done(null, {
                            message: 'Bookings will not be created for the scheduled date/time',
                        });
                    }

                    // Process inspection events and create inspections
                    const newInspectionData = await this.processInspectionEvents(
                        eventsArray,
                        inspectionData,
                        memberDetails,
                        loginUser
                    );

                    if (Object.keys(newInspectionData).length > 0 && typeof newInspectionData === 'object') {
                        // Create notification and get preferences
                        const { history, newNotification, memberLocationPreference } = await this.createNotificationAndGetPreferences(
                            inspectionData,
                            newInspectionData,
                            memberDetails,
                            loginUser
                        );

                        // Get admin data for notifications
                        const adminData = await this.getAdminDataForNotifications(
                            inspectionData,
                            newNotification,
                            memberLocationPreference
                        );

                        // Handle notifications
                        const finalHistory = await this.handleNotifications({
                            history,
                            newNotification,
                            memberLocationPreference,
                            adminData,
                            projectDetails,
                            newInspectionData,
                            inspectionData,
                            memberDetails,
                            loginUser
                        });

                        // Handle additional notifications for specific roles
                        const memberLocationPreferenceNotify = await LocationNotificationPreferences.findAll({
                            where: {
                                ProjectId: +inspectionData.ProjectId,
                                LocationId: +inspectionData.LocationId,
                                follow: true,
                            },
                            include: [
                                {
                                    association: 'Member',
                                    attributes: ['id', 'RoleId'],
                                    where: {
                                        [Op.and]: [
                                            {
                                                id: { [Op.ne]: memberDetails.id },
                                            },
                                        ],
                                    },
                                    include: [
                                        {
                                            association: 'User',
                                            attributes: ['id', 'firstName', 'lastName', 'email'],
                                        },
                                    ],
                                },
                            ],
                        });

                        // Handle role-specific notifications
                        if (+memberDetails.RoleId === 4 || +memberDetails.RoleId === 3) {
                            const userEmails = await this.getMemberDetailData(
                                finalHistory,
                                memberLocationPreference,
                            );

                            if (userEmails.length > 0) {
                                await this.handleRoleSpecificNotifications(
                                    userEmails,
                                    memberDetails,
                                    loginUser,
                                    newInspectionData,
                                    inspectionData
                                );
                            }
                        }

                        if (memberLocationPreferenceNotify && memberLocationPreferenceNotify.length > 0) {
                            finalHistory.memberData = [];
                            finalHistory.memberData.push(...memberLocationPreferenceNotify);
                        }

                        return done(finalHistory, false);
                    }

                    return done(null, {
                        message: 'Bookings will not be created for the scheduled date/time',
                    });
                });
            } else {
                return done(null, { message: 'Project does not exist.' });
            }
        } catch (e) {
            return done(null, e);
        }
    },

    // Helper function to handle role-specific notifications
    async handleRoleSpecificNotifications(userEmails, memberDetails, loginUser, newInspectionData, inspectionData) {
        for (const element of userEmails) {
            await this.processRoleSpecificNotification(
                element,
                memberDetails,
                loginUser,
                newInspectionData,
                inspectionData
            );
        }
    },

    async processRoleSpecificNotification(element, memberDetails, loginUser, newInspectionData, inspectionData) {
        if (+element.RoleId !== 2 || +element.MemberId === +memberDetails.id) {
            return; // Skip if role doesn't match or same member
        }

        const name = element.firstName ? `${element.firstName} ${element.lastName}` : 'user';

        const memberRole = await Role.findOne({
            where: {
                id: memberDetails.RoleId,
                isDeleted: false,
            },
        });

        const mailPayload = {
            name,
            email: element.email,
            content: `We would like to inform you that
            ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has created a Inspection booking ${newInspectionData.InspectionId} and waiting for your approval. Kindly review the booking and update the status.`,
        };

        const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
            where: {
                MemberId: +element.MemberId,
                ProjectId: +inspectionData.ProjectId,
                LocationId: +inspectionData.LocationId,
                isDeleted: false,
            },
        });

        if (!isMemberFollowLocation) return;

        const memberNotification = await NotificationPreference.findOne({
            where: {
                MemberId: +element.MemberId,
                ProjectId: +inspectionData.ProjectId,
                isDeleted: false,
            },
            include: [
                {
                    association: 'NotificationPreferenceItem',
                    where: {
                        id: 8,
                        isDeleted: false,
                    },
                },
            ],
        });

        if (memberNotification?.instant) {
            await MAILER.sendMail(
                mailPayload,
                'notifyPAForApproval',
                `Inspection Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                `Inspection Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                async (info, err) => {
                    console.log(info, err);
                },
            );
        }

        if (memberNotification?.dailyDigest) {
            await this.createDailyDigestDataApproval({
                RoleId: +memberDetails.RoleId,
                MemberId: +element.MemberId,
                ProjectId: +inspectionData.ProjectId,
                ParentCompanyId: +inspectionData.ParentCompanyId,
                loginUser,
                dailyDigestMessage: 'created a',
                requestType: 'Inspection Request',
                messages: `Inspection Booking (${newInspectionData.InspectionId} - ${newInspectionData.description})`,
                messages2: 'and waiting for your approval',
                requestId: newInspectionData.id,
            });
        }
    },

    async compareinspectionDateWithinspectionWindowDate(
        dateStr,
        timeStr,
        timezoneStr,
        inspectionWindowTime,
        inspectionWindowTimeUnit,
    ) {
        const datetimeStr = `${moment(dateStr).format('YYYY-MM-DD')}T${timeStr}`;
        const datetime = moment.tz(datetimeStr, timezoneStr);
        const currentDatetime = moment
            .tz(timezoneStr)
            .add(inspectionWindowTime, inspectionWindowTimeUnit)
            .startOf('minute');
        return datetime.isSameOrBefore(currentDatetime);
    },
    async ReadAllnotification(inputData, done) {
        try {
            await this.getDynamicModel(inputData);
            const loginUser = inputData.user;
            const memberDetails = await Member.findOne({
                where: Sequelize.and({
                    UserId: loginUser.id,
                    isActive: true,
                    isDeleted: false,
                    ProjectId: inputData.query.ProjectId,
                }),
            });
            await DeliveryPersonNotification.update(
                { seen: true },
                {
                    where: {
                        ProjectId: inputData.query.ProjectId,
                        MemberId: memberDetails.id,
                        isDeleted: false,
                    },
                },
            );
            const condition = {
                seen: false,
                ProjectId: inputData.query.ProjectId,
            };
            const count = await DeliveryPersonNotification.getUnSeenCount(condition, loginUser);
            done(count.length, false);
        } catch (e) {
            return done(null, e);
        }
    },
    async getDynamicModel(inputData) {
        await this.returnProjectModel();
        let { domainName } = inputData.user;
        const incomeData = inputData;
        let enterpriseValue;
        let ProjectId;
        const ParentCompanyId = this.getParentCompanyId(inputData);

        domainName = await this.resolveDomainName(domainName, ParentCompanyId, inputData, enterpriseValue);

        const modelObj = await helper.getDynamicModel(domainName);
        this.assignModelObjects(modelObj);

        if (enterpriseValue) {
            const newUser = await User.findOne({ where: { email: inputData.user.email } });
            incomeData.user = newUser;
        }

        return ProjectId;
    },

    getParentCompanyId(inputData) {
        return inputData.body.ParentCompanyId
            ? inputData.body.ParentCompanyId
            : inputData.params.ParentCompanyId;
    },

    async getDomainFromName(domainName) {
        if (!domainName) return '';

        const domainEnterpriseValue = await Enterprise.findOne({
            where: { name: domainName.toLowerCase() },
        });

        return domainEnterpriseValue ? domainName : '';
    },

    async getDomainFromParentCompany(ParentCompanyId, inputData, enterpriseValue) {
        if (ParentCompanyId === undefined || ParentCompanyId === 'undefined') return '';

        const { email } = inputData.user || {};
        if (!email) return '';

        const userData = await publicUser.findOne({ where: { email } });
        if (!userData) return '';

        const memberData = await publicMember.findOne({
            where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });

        if (memberData) {
            enterpriseValue = await Enterprise.findOne({
                where: memberData.isAccount
                    ? { id: memberData.EnterpriseId, status: 'completed' }
                    : { ParentCompanyId, status: 'completed' },
            });
        } else {
            enterpriseValue = await Enterprise.findOne({
                where: { ParentCompanyId, status: 'completed' },
            });
        }

        return enterpriseValue ? enterpriseValue.name.toLowerCase() : '';
    },

    async resolveDomainName(domainName, ParentCompanyId, inputData, enterpriseValue) {
        domainName = await this.getDomainFromName(domainName);

        if (!domainName) {
            domainName = await this.getDomainFromParentCompany(ParentCompanyId, inputData, enterpriseValue);
        }

        return domainName;
    },


    assignModelObjects(modelObj) {
        InspectionRequest = modelObj.InspectionRequest;
        Member = modelObj.Member;
        InspectionPerson = modelObj.InspectionPerson;
        InspectionGate = modelObj.InspectionGate;
        InspectionEquipment = modelObj.InspectionEquipment;
        InspectionCompany = modelObj.InspectionCompany;
        Role = modelObj.Role;
        Gates = modelObj.Gates;
        Equipments = modelObj.Equipments;
        DeliverDefineWork = modelObj.DeliverDefineWork;
        Company = modelObj.Company;
        Project = modelObj.Project;
        User = modelObj.User;
        DeliverDefine = modelObj.DeliverDefine;
        InspectionHistory = modelObj.InspectionHistory;
        VoidList = modelObj.VoidList;
        DeliveryPersonNotification = modelObj.DeliveryPersonNotification;
        Notification = modelObj.Notification;
    },

    async Markallnotification(inputData) {
        try {
            await this.getDynamicModel(inputData);
            const loginUser = inputData.user;
            const { params } = inputData;

            const memberDetails = await Member.findOne({
                where: Sequelize.and({
                    UserId: loginUser.id,
                    isActive: true,
                    isDeleted: false,
                    ProjectId: params.ProjectId,
                }),
            });
            await DeliveryPersonNotification.update(
                { seen: true },
                {
                    where: {
                        ProjectId: params.ProjectId,
                        MemberId: memberDetails.id,
                        isDeleted: false,
                    },
                },
            );
            const condition = {
                seen: false,
                ProjectId: params.ProjectId,
            };
            const count = await DeliveryPersonNotification.getUnSeenCount(condition, loginUser);
            return { status: 200, data: count.length };
        } catch (e) {
            console.log(e);
        }
    },
    async returnProjectModel() {
        const modelData = await helper.returnProjectModel();
        publicMember = modelData.Member;
        publicUser = modelData.User;
    },
    async checkInputDatas(inputData, done) {
        await this.getDynamicModel(inputData);
        const inspectionData = inputData.body;
        const { companies, persons, define } = inspectionData;
        const gates = [inspectionData.GateId];
        const equipments = inspectionData.EquipmentId;
        const inputProjectId = inspectionData.ProjectId;
        const memberList = await Member.count({
            where: { id: { [Op.in]: persons }, ProjectId: inputProjectId, isDeleted: false },
        });
        const gateList = await Gates.count({
            where: { id: { [Op.in]: gates }, ProjectId: inputProjectId, isDeleted: false },
        });
        const equipmentList = await Equipments.count({
            where: { id: { [Op.in]: equipments }, ProjectId: inputProjectId, isDeleted: false },
        });
        const defineList = await DeliverDefineWork.count({
            where: { id: { [Op.in]: define }, ProjectId: inputProjectId, isDeleted: false },
        });
        const companyList = await Company.count({
            where: {
                [Op.or]: [
                    {
                        id: { [Op.in]: companies },
                        ProjectId: +inputProjectId,
                        isDeleted: false,
                    },
                    {
                        id: {
                            [Op.in]: companies,
                        },
                        isParent: true,
                        ParentCompanyId: +inspectionData.ParentCompanyId,
                        isDeleted: false,
                    },
                ],
            },
        });
        if (inspectionData.persons && inspectionData.persons.length > 0 && memberList !== persons.length) {
            return done(null, { message: 'Some Member is not in the project' });
        }
        if (inspectionData.GateId && gateList !== gates.length) {
            return done(null, { message: 'Mentioned Gate is not in the project' });
        }
        if ((inspectionData.EquipmentId && equipments[0] != 0) && equipmentList !== equipments.length) {
            return done(null, { message: 'Mentioned Equipment is not in this project' });
        }
        if (
            inspectionData.companies &&
            inspectionData.companies.length > 0 &&
            companyList !== companies.length
        ) {
            return done(null, { message: 'Some Company is not in the project' });
        }
        if (inspectionData.define && inspectionData.define.length > 0 && defineList !== define.length) {
            return done(null, { message: 'Some Definable Feature of Work is not in the project' });
        }
        return done(true, false);
    },
    // Helper function to setup edit request data
    async setupEditRequestData(inputData) {
        await this.getDynamicModel(inputData);
        const inspectionData = inputData.body;
        const loginUser = inputData.user;
        const { recurrenceId } = inspectionData;

        const projectSettingDetails = await Project.getProjectAndSettings({
            isDeleted: false,
            id: +inspectionData.ProjectId,
        });
        const memberDetails = await Member.getBy({
            UserId: loginUser.id,
            ProjectId: inspectionData.ProjectId,
            isActive: true,
            isDeleted: false,
        });
        const range = momentRange.range(
            moment(inspectionData.inspectionStart),
            moment(inspectionData.recurrenceEndDate).add(1, 'day'),
        );
        const totalDays = Array.from(range.by('day'));
        const roleDetails = await Role.getBy('Project Admin');
        const accountRoleDetails = await Role.getBy('Account Admin');

        return {
            inspectionData,
            loginUser,
            recurrenceId,
            projectSettingDetails,
            memberDetails,
            totalDays,
            roleDetails,
            accountRoleDetails
        };
    },

    // Helper function to convert timezone for edit request
    convertTimezoneForEdit(inspectionData) {
        const startTime = inspectionData.inspectionStartTime;
        const endTime = inspectionData.inspectionEndTime;
        const date1 = moment(inspectionData.inspectionStart).format('MM/DD/YYYY');
        const date2 = moment(inspectionData.recurrenceEndDate).format('MM/DD/YYYY');

        const chosenTimezoneDeliveryStart = moment.tz(
            `${date1} ${startTime}`,
            'MM/DD/YYYY HH:mm',
            inspectionData.timezone,
        );

        const chosenTimezoneDeliveryEnd = moment.tz(
            `${date2} ${endTime}`,
            'MM/DD/YYYY HH:mm',
            inspectionData.timezone,
        );

        const deliveryStartUTC = chosenTimezoneDeliveryStart
            .clone()
            .tz('UTC')
            .format('YYYY-MM-DD HH:mm:ssZ');
        const recurrenceEndUTC = chosenTimezoneDeliveryEnd
            .clone()
            .tz('UTC')
            .format('YYYY-MM-DD HH:mm:ssZ');

        return { deliveryStartUTC, recurrenceEndUTC };
    },

    // Helper function to handle series option 1 (single event edit)
    async handleSeriesOption1(inspectionData, deliveryStartUTC, projectSettingDetails) {
        const requestArray = [];
        requestArray.push({
            ProjectId: inspectionData.ProjectId,
            inspectionStart: deliveryStartUTC,
            inspectionEnd: inspectionData.inspectionEnd,
            id: inspectionData.id,
        });
        const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
            requestArray,
            projectSettingDetails,
            'edit',
            inspectionData.GateId,
        );
        if (isOverlapping?.error) {
            return {
                error: true,
                message: isOverlapping.message,
            };
        }
        return { error: false };
    },

    // Helper function to update series data
    updateSeriesData(AllRequestSeries, requestSeries, updateRequestSeries, newRequestSeries) {
        AllRequestSeries.forEach((item1) => {
            const match = requestSeries.find(
                (item2) =>
                    new Date(item2.inspectionStart).getTime() ===
                    new Date(item1.inspectionStart).getTime(),
            );
            if (match) {
                match.description = item1.description;
                match.escort = item1.escort;
                match.vehicleDetails = item1.vehicleDetails;
                match.notes = item1.notes;
                match.inspectionStart = item1.inspectionStart;
                match.inspectionEnd = item1.inspectionEnd;
                match.ProjectId = item1.ProjectId;
                match.createdBy = item1.createdBy;
                match.isAssociatedWithCraneRequest = item1.isAssociatedWithCraneRequest;
                match.requestType = item1.requestType;
                match.cranePickUpLocation = item1.cranePickUpLocation;
                match.craneDropOffLocation = item1.craneDropOffLocation;
                match.CraneRequestId = null;
                match.LocationId = item1.LocationId;
                match.inspectionType = item1.inspectionType;
                match.OriginationAddress = item1.OriginationAddress;
                match.vehicleType = item1.vehicleType;
                match.status = item1.status;
                match.approvedBy = item1.approvedBy;
                match.approved_at = item1.approved_at;
                match.GateId = item1.GateId;
                updateRequestSeries.push(match);
            } else {
                newRequestSeries.push(item1);
            }
        });
    },

    // Helper function to identify series to delete
    identifyDeleteSeries(requestSeries, AllRequestSeries, deleteRequestSeries) {
        requestSeries.forEach((item2) => {
            const match = AllRequestSeries.find(
                (item1) =>
                    new Date(item2.inspectionStart).getTime() ===
                    new Date(item1.inspectionStart).getTime(),
            );
            if (!match) {
                deleteRequestSeries.push(item2);
            }
        });
    },

    // Helper function to handle series option 2 with recurrence editing
    async handleSeriesOption2WithRecurrence(params, done) {
        const { inspectionData, memberDetails, totalDays, roleDetails, accountRoleDetails, projectSettingDetails, requestSeries } = params;
        let AllRequestSeries = [];
        let updateRequestSeries = [];
        let newRequestSeries = [];
        let deleteRequestSeries = [];

        await this.createNewRecurrenceEvents(
            inspectionData,
            memberDetails,
            totalDays,
            roleDetails,
            accountRoleDetails,
            projectSettingDetails,
            async (checkResponse, checkError) => {
                if (checkError) {
                    return done(null, checkError);
                }
                AllRequestSeries = checkResponse;

                // Update series data using helper function
                this.updateSeriesData(AllRequestSeries, requestSeries, updateRequestSeries, newRequestSeries);

                // Identify series to delete using helper function
                this.identifyDeleteSeries(requestSeries, AllRequestSeries, deleteRequestSeries);

                // Process deletions and updates
                await this.processSeriesDeletionsAndUpdates(deleteRequestSeries, updateRequestSeries, inspectionData);

                // Create new bookings for recurrence
                if (newRequestSeries.length > 0) {
                    const result = await this.createNewSeriesBookings(newRequestSeries, memberDetails, inspectionData);
                    if (result.error) {
                        return done(null, { message: result.message });
                    }
                }
            }
        );
    },

    // Helper function to process series deletions and updates
    async processSeriesDeletionsAndUpdates(deleteRequestSeries, updateRequestSeries, inspectionData) {
        // Delete old bookings
        for (const deleteRequest of deleteRequestSeries) {
            const deleteRequestPayload = { isDeleted: true };
            await InspectionRequest.update(deleteRequestPayload, {
                where: { id: deleteRequest.id },
            });
            const deleteMappingPayload = {
                GateId: deleteRequest.GateId,
                LocationId: deleteRequest.LocationId,
                InspectionId: deleteRequest.id
            };
            await this.deleteEquipmentMapping(deleteMappingPayload);
        }

        // Update existing bookings
        for (const updateRequest of updateRequestSeries) {
            await InspectionRequest.update(updateRequest, {
                where: { id: updateRequest.id },
            });
            const mappingPayload = {
                EquipmentId: inspectionData.EquipmentId,
                startTime: updateRequest.inspectionStart,
                endTime: updateRequest.inspectionEnd,
                GateId: updateRequest.GateId,
                LocationId: updateRequest.LocationId,
                InspectionId: updateRequest.id
            };
            await this.createEquipmentMapping(mappingPayload);
        }
    },

    // Helper function to create new series bookings
    async createNewSeriesBookings(newRequestSeries, memberDetails, inspectionData) {
        let id = null;
        const lastIdValue = await InspectionRequest.findOne({
            where: { ProjectId: memberDetails.ProjectId },
            order: [['InspectionId', 'DESC']],
        });
        const newValue = JSON.parse(JSON.stringify(lastIdValue));

        if (newValue && newValue.InspectionId !== null && newValue.InspectionId !== undefined) {
            id = +newValue.InspectionId;
            for (const newRequest of newRequestSeries) {
                id += 1;
                newRequest.InspectionId = id;
                const newInspectionData = await InspectionRequest.createInstance(newRequest);

                // Create equipment mapping
                const mappingPayload = {
                    EquipmentId: inspectionData.EquipmentId,
                    startTime: newRequest.inspectionStart,
                    endTime: newRequest.inspectionEnd,
                    GateId: newRequest.GateId,
                    LocationId: newRequest.LocationId,
                    InspectionId: newInspectionData.id
                };
                await this.createEquipmentMapping(mappingPayload);

                // Create associations
                const updateParam = {
                    InspectionId: newInspectionData.id,
                    InspectionCode: newInspectionData.InspectionId,
                    ProjectId: inspectionData.ProjectId,
                };
                await this.createInspectionAssociations(inspectionData, updateParam);

                // Create history
                const history = {
                    InspectionRequestId: newInspectionData.id,
                    InspectionId: newInspectionData.InspectionId,
                    MemberId: memberDetails.id,
                    type: 'create',
                    description: `${inspectionData.loginUser.firstName} ${inspectionData.loginUser.lastName} Created Inspection Booking, ${inspectionData.description}.`,
                };
                await InspectionHistory.createInstance(history);
            }
            return { error: false };
        } else {
            return { error: true, message: 'Unable to create new Bookings' };
        }
    },

    // Helper function to handle series option 1 recurrence updates
    async handleSeriesOption1RecurrenceUpdates(inspectionData, recurrenceId, deliveryStartUTC) {
        const editInspectionData = await InspectionRequest.findOne({ where: { id: inspectionData.id } });

        if (editInspectionData && inspectionData.recurrenceId) {
            const previousRecordInThisEventSeries = await InspectionRequest.findAll({
                where: [
                    Sequelize.and({
                        recurrenceId,
                        inspectionStart: {
                            [Op.lt]: deliveryStartUTC,
                        },
                        isDeleted: false,
                    }),
                ],
                order: [['id', 'DESC']],
            });

            const NextSeriesLastRecord = await InspectionRequest.findAll({
                where: [
                    Sequelize.and({
                        recurrenceId,
                        inspectionStart: {
                            [Op.gt]: deliveryStartUTC,
                        },
                        isDeleted: false,
                    }),
                ],
                order: [['id', 'DESC']],
            });

            if (
                ((NextSeriesLastRecord && NextSeriesLastRecord.length > 0) ||
                    (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 0)) &&
                !(
                    NextSeriesLastRecord &&
                    NextSeriesLastRecord.length > 0 &&
                    previousRecordInThisEventSeries &&
                    previousRecordInThisEventSeries.length > 0
                )
            ) {
                if (NextSeriesLastRecord && NextSeriesLastRecord.length > 0) {
                    const chosenTimezoneinspectionStart = moment.tz(
                        `${inspectionData.nextSeriesRecurrenceStartDate}  '00:00'`,
                        'YYYY-MM-DD HH:mm',
                        inspectionData.timezone,
                    );
                    const utcDate = chosenTimezoneinspectionStart
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                    await RequestRecurrenceSeries.update(
                        {
                            recurrenceStartDate: utcDate,
                        },
                        {
                            where: {
                                id: NextSeriesLastRecord[0].recurrenceId,
                            },
                        },
                    );
                }
                if (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 0) {
                    await RequestRecurrenceSeries.update(
                        {
                            recurrenceEndDate: previousRecordInThisEventSeries[0].inspectionStart,
                        },
                        {
                            where: {
                                id: previousRecordInThisEventSeries[0].recurrenceId,
                            },
                        },
                    );
                }
            }
        }
    },

    // Helper function to handle series option 2 processing
    async handleSeriesOption2Processing(params, done) {
        const { inspectionData, recurrenceId, editInspectionData, memberDetails, totalDays, roleDetails, accountRoleDetails, projectSettingDetails } = params;
        let requestSeries = [];
        if (inspectionData.seriesOption === 2) {
            requestSeries = await InspectionRequest.findAll({
                where: [
                    Sequelize.and({
                        recurrenceId,
                        inspectionStart: {
                            [Op.gte]: editInspectionData.inspectionStart,
                        },
                        isDeleted: false,
                    }),
                ],
            });
        }

        if (inspectionData.recurrenceEdited) {
            await this.handleSeriesOption2WithRecurrence({
                inspectionData,
                memberDetails,
                totalDays,
                roleDetails,
                accountRoleDetails,
                projectSettingDetails,
                requestSeries
            },
                done
            );
        }
    },

    async editRequest(inputData, done) {
        try {
            const setupData = await this.setupEditRequestData(inputData);
            const { inspectionData, loginUser, recurrenceId, projectSettingDetails, memberDetails, totalDays, roleDetails, accountRoleDetails } = setupData;

            const { deliveryStartUTC } = this.convertTimezoneForEdit(inspectionData);

            if (!projectSettingDetails) {
                return done(null, { message: 'Project settings not found.' });
            }

            const editInspectionData = await InspectionRequest.findOne({ where: { id: inspectionData.id } });

            // Handle different series options
            if (inspectionData.seriesOption === 1) {
                const result = await this.handleSeriesOption1(inspectionData, deliveryStartUTC, projectSettingDetails);
                if (result.error) {
                    return done(null, { message: result.message });
                }

                // Handle recurrence updates for series option 1
                await this.handleSeriesOption1RecurrenceUpdates(inspectionData, recurrenceId, deliveryStartUTC);
            }

            if (inspectionData.seriesOption === 2) {
                await this.handleSeriesOption2Processing({
                    inspectionData,
                    recurrenceId,
                    editInspectionData,
                    memberDetails,
                    totalDays,
                    roleDetails,
                    accountRoleDetails,
                    projectSettingDetails
                },
                    done
                );
            }

            // Handle series option 3 (All events)
            if (inspectionData.seriesOption === 3) {
                await this.handleSeriesOption3Processing(
                    inspectionData,
                    recurrenceId,
                    memberDetails,
                    loginUser,
                    done
                );
            }

            return done({ message: 'Inspection request updated successfully' }, false);

        } catch (e) {
            return done(null, e);
        }
    },

    // Helper function to handle series option 3 processing
    async handleSeriesOption3Processing(inspectionData, recurrenceId, memberDetails, loginUser, done) {
        const editSeriesRequests = await InspectionRequest.findAll({
            where: [
                Sequelize.and({
                    recurrenceId,
                    isDeleted: false,
                }),
            ],
        });

        for (const idDetails of editSeriesRequests) {
            const existsInspectionRequest = await InspectionRequest.findOne({
                where: {
                    id: +idDetails.id,
                },
            });

            if (existsInspectionRequest) {
                await this.checkInputDatas(inspectionData, async (checkResponse, checkError) => {
                    if (checkError) {
                        return done(null, checkError);
                    }

                    // Update inspection parameters
                    const InspectionParam = this.createEditInspectionParam(inspectionData, idDetails);

                    await InspectionRequest.update(InspectionParam, {
                        where: { id: +idDetails.id },
                    });

                    // Update equipment mapping
                    const mappingPayload = {
                        EquipmentId: inspectionData.EquipmentId,
                        startTime: InspectionParam.inspectionStart,
                        endTime: InspectionParam.inspectionEnd,
                        GateId: inspectionData.GateId,
                        LocationId: inspectionData.LocationId,
                        InspectionId: idDetails.id
                    };
                    await this.createEquipmentMapping(mappingPayload);

                    // Update associations
                    await this.updateInspectionAssociations(inspectionData, idDetails.id);

                    // Create history
                    await this.createEditHistory(inspectionData, memberDetails, loginUser, idDetails);
                });
            }
        }
    },

    // Helper function to create edit inspection parameters
    createEditInspectionParam(inspectionData, idDetails) {
        const InspectionParam = {
            description: inspectionData.description,
            escort: inspectionData.escort,
            vehicleDetails: inspectionData.vehicleDetails,
            notes: inspectionData.notes,
            isAssociatedWithCraneRequest: inspectionData.isAssociatedWithCraneRequest,
            requestType: inspectionData.requestType,
            cranePickUpLocation: inspectionData.cranePickUpLocation,
            craneDropOffLocation: inspectionData.craneDropOffLocation,
            recurrenceId: inspectionData.recurrenceId,
            LocationId: inspectionData.LocationId,
            inspectionType: inspectionData.inspectionType,
            OriginationAddress: inspectionData.originationAddress,
            vehicleType: inspectionData.vehicleType
        };

        if (inspectionData.seriesOption === 1) {
            InspectionParam.inspectionStart = inspectionData.inspectionStart;
            InspectionParam.inspectionEnd = inspectionData.inspectionEnd;
        }

        if (inspectionData.seriesOption === 2 || inspectionData.seriesOption === 3) {
            const utcinspectionStartTimestamp = moment.utc(idDetails.inspectionStart);
            const localStartTimestamp = utcinspectionStartTimestamp.tz(inspectionData.timezone);
            const utcinspectionEndTimestamp = moment.utc(idDetails.inspectionEnd);
            const localEndTimestamp = utcinspectionEndTimestamp.tz(inspectionData.timezone);

            InspectionParam.inspectionStart = this.convertTimezoneToUtc(
                moment(localStartTimestamp).format('MM/DD/YYYY'),
                inspectionData.timezone,
                inspectionData.inspectionStartTime,
            );
            InspectionParam.inspectionEnd = this.convertTimezoneToUtc(
                moment(localEndTimestamp).format('MM/DD/YYYY'),
                inspectionData.timezone,
                inspectionData.inspectionEndTime,
            );
        }

        return InspectionParam;
    },

    // Helper function to update inspection associations
    async updateInspectionAssociations(inspectionData, inspectionId) {
        // Delete existing associations
        const condition = { InspectionId: inspectionId };
        await this.updateValues(condition, () => { });

        // Create new associations
        const updateParam = {
            InspectionId: inspectionId,
            InspectionCode: inspectionId,
            ProjectId: inspectionData.ProjectId,
        };
        await this.createInspectionAssociations(inspectionData, updateParam);
    },

    // Helper function to create edit history
    async createEditHistory(inspectionData, memberDetails, loginUser, idDetails) {
        const history = {
            InspectionRequestId: idDetails.id,
            InspectionId: idDetails.InspectionId,
            MemberId: memberDetails.id,
            type: 'edit',
            description: `${loginUser.firstName} ${loginUser.lastName} Updated Inspection Booking, ${inspectionData.description}.`,
        };
        await InspectionHistory.createInstance(history);
    },
    async updateCompanyHistory(addedCompany, deletedCompany, history, loginUser) {
        const newHistory = history;
        let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Company`;
        let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Company`;
        addedCompany.forEach(async (element, i) => {
            const newCompanyData = await Company.findOne({
                where: { id: element.CompanyId },
            });
            if (i === 0) {
                if (i === addedCompany.length - 1) {
                    addDesc += ` ${newCompanyData.companyName}`;
                    newHistory.description = addDesc;
                    InspectionHistory.createInstance(newHistory);
                } else {
                    addDesc += ` ${newCompanyData.companyName}`;
                }
            } else if (i === addedCompany.length - 1) {
                addDesc += `,${newCompanyData.companyName}`;
                newHistory.description = addDesc;
                InspectionHistory.createInstance(newHistory);
            } else {
                addDesc += `,${newCompanyData.companyName}`;
            }
        });
        deletedCompany.forEach(async (element, i) => {
            const newCompanyData = await Company.findOne({
                where: { id: element.CompanyId },
            });
            if (i === 0) {
                if (i === deletedCompany.length - 1) {
                    deleteDesc += ` ${newCompanyData.companyName}`;
                    newHistory.description = deleteDesc;
                    InspectionHistory.createInstance(newHistory);
                } else {
                    deleteDesc += ` ${newCompanyData.companyName}`;
                }
            } else if (i === deletedCompany.length - 1) {
                deleteDesc += `,${newCompanyData.companyName}`;
                newHistory.description = deleteDesc;
                InspectionHistory.createInstance(newHistory);
            } else {
                deleteDesc += `,${newCompanyData.companyName}`;
            }
        });
    },
    async updateGateHistory(addedGate, deletedGate, history, loginUser) {
        const newHistory = history;
        let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Gate`;
        let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Gate`;
        addedGate.forEach(async (element, i) => {
            const newGateData = await Gates.findOne({
                where: { id: element.GateId },
            });
            if (i === 0) {
                if (i === addedGate.length - 1) {
                    addDesc += ` ${newGateData.gateName}`;
                    newHistory.description = addDesc;
                    InspectionHistory.createInstance(newHistory);
                } else {
                    addDesc += ` ${newGateData.gateName}`;
                }
            } else if (i === addedGate.length - 1) {
                addDesc += `,${newGateData.gateName}`;
                newHistory.description = addDesc;
                InspectionHistory.createInstance(newHistory);
            } else {
                addDesc += `,${newGateData.gateName}`;
            }
        });
        deletedGate.forEach(async (element, i) => {
            const newGateData = await Gates.findOne({
                where: { id: element.GateId },
            });
            if (i === 0) {
                if (i === deletedGate.length - 1) {
                    deleteDesc += ` ${newGateData.gateName}`;
                    newHistory.description = deleteDesc;
                    InspectionHistory.createInstance(newHistory);
                } else {
                    deleteDesc += ` ${newGateData.gateName}`;
                }
            } else if (i === deletedGate.length - 1) {
                deleteDesc += `,${newGateData.gateName}`;
                newHistory.description = deleteDesc;
                InspectionHistory.createInstance(newHistory);
            } else {
                deleteDesc += `,${newGateData.gateName}`;
            }
        });
    },
    async updateEquipmentHistory(addedEquipment, deletedEquipment, history, loginUser) {
        const newHistory = history;
        let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Equipment`;
        let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Equipment`;
        addedEquipment.forEach(async (element, i) => {
            const newEquipmentData = await Equipments.findOne({
                where: { id: element.EquipmentId },
            });
            if (i === 0) {
                if (i === addedEquipment.length - 1) {
                    addDesc += ` ${newEquipmentData.equipmentName}`;
                    newHistory.description = addDesc;
                    InspectionHistory.createInstance(newHistory);
                } else {
                    addDesc += ` ${newEquipmentData.equipmentName}`;
                }
            } else if (i === addedEquipment.length - 1) {
                addDesc += `,${newEquipmentData.equipmentName}`;
                newHistory.description = addDesc;
                InspectionHistory.createInstance(newHistory);
            } else {
                addDesc += `,${newEquipmentData.equipmentName}`;
            }
        });
        deletedEquipment.forEach(async (element, i) => {
            const newEquipmentData = await Equipments.findOne({
                where: { id: element.EquipmentId },
            });
            if (i === 0) {
                if (i === deletedEquipment.length - 1) {
                    deleteDesc += ` ${newEquipmentData.equipmentName}`;
                    newHistory.description = deleteDesc;
                    InspectionHistory.createInstance(newHistory);
                } else {
                    deleteDesc += ` ${newEquipmentData.equipmentName}`;
                }
            } else if (i === deletedEquipment.length - 1) {
                deleteDesc += `,${newEquipmentData.equipmentName}`;
                newHistory.description = deleteDesc;
                InspectionHistory.createInstance(newHistory);
            } else {
                deleteDesc += `,${newEquipmentData.equipmentName}`;
            }
        });
    },
    async updatePersonHistory(addedPerson, deletedPerson, history, loginUser) {
        const newHistory = history;
        let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Member`;
        let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Member`;
        addedPerson.forEach(async (element, i) => {
            const newMemberData = await Member.findOne({
                where: { id: element.MemberId, isDeleted: false },
                include: [
                    {
                        association: 'User',
                        attributes: ['firstName', 'lastName'],
                    },
                ],
            });
            if (i === 0) {
                if (i === addedPerson.length - 1) {
                    if (newMemberData.User.firstName) {
                        addDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
                    }
                    newHistory.description = addDesc;
                    InspectionHistory.createInstance(newHistory);
                } else if (newMemberData.User.firstName) {
                    addDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
                }
            } else if (i === addedPerson.length - 1) {
                if (newMemberData.User.firstName) {
                    addDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
                }
                newHistory.description = addDesc;
                InspectionHistory.createInstance(newHistory);
            } else if (newMemberData.User.firstName) {
                addDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
            }
        });
        deletedPerson.forEach(async (element, i) => {
            const newMemberData = await Member.findOne({
                where: { id: element.MemberId, isDeleted: false },
                include: [
                    {
                        association: 'User',
                        attributes: ['firstName', 'lastName'],
                    },
                ],
            });
            if (i === 0) {
                if (i === deletedPerson.length - 1) {
                    if (newMemberData.User.firstName) {
                        deleteDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
                    }
                    newHistory.description = deleteDesc;
                    InspectionHistory.createInstance(newHistory);
                } else if (newMemberData.User.firstName) {
                    deleteDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
                }
            } else if (i === deletedPerson.length - 1) {
                if (newMemberData.User.firstName) {
                    deleteDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
                }
                newHistory.description = deleteDesc;
                InspectionHistory.createInstance(newHistory);
            } else if (newMemberData.User.firstName) {
                deleteDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
            }
        });
    },
    async updateDefineHistory(addedDefine, deletedDefine, history, loginUser) {
        const newHistory = history;
        let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the DFOW`;
        let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the DFOW`;
        addedDefine.forEach(async (element, i) => {
            const newDefineData = await DeliverDefineWork.findOne({
                where: { id: element.DeliverDefineWorkId },
            });
            if (i === 0) {
                if (i === addedDefine.length - 1) {
                    addDesc += ` ${newDefineData.DFOW}`;
                    newHistory.description = addDesc;
                    InspectionHistory.createInstance(newHistory);
                } else {
                    addDesc += ` ${newDefineData.DFOW}`;
                }
            } else if (i === addedDefine.length - 1) {
                addDesc += `,${newDefineData.DFOW}`;
                newHistory.description = addDesc;
                InspectionHistory.createInstance(newHistory);
            } else {
                addDesc += `,${newDefineData.DFOW}`;
            }
        });
        deletedDefine.forEach(async (element, i) => {
            const newDefineData = await DeliverDefineWork.findOne({
                where: { id: element.DeliverDefineWorkId },
            });
            if (i === 0) {
                if (i === deletedDefine.length - 1) {
                    deleteDesc += ` ${newDefineData.DFOW}`;
                    newHistory.description = deleteDesc;
                    InspectionHistory.createInstance(newHistory);
                } else {
                    deleteDesc += ` ${newDefineData.DFOW}`;
                }
            } else if (i === deletedDefine.length - 1) {
                deleteDesc += `,${newDefineData.DFOW}`;
                newHistory.description = deleteDesc;
                InspectionHistory.createInstance(newHistory);
            } else {
                deleteDesc += `,${newDefineData.DFOW}`;
            }
        });
    },
    async lastinspection(inputData, done) {
        try {
            const { params } = inputData;
            let data;
            const lastData = await InspectionRequest.findOne({
                where: { ProjectId: params.ProjectId, isDeleted: false },
                order: [['InspectionId', 'DESC']],
            });
            if (lastData) {
                data = lastData.InspectionId + 1;
            } else {
                data = 1;
            }
            done({ InspectionId: data }, false);
        } catch (e) {
            done(null, e);
        }
    },
    async updateValues(condition, done) {
        try {
            await InspectionCompany.update({ isDeleted: true }, { where: condition });
            await InspectionPerson.update({ isDeleted: true }, { where: condition });
            await InspectionGate.update({ isDeleted: true }, { where: condition });
            await InspectionEquipment.update({ isDeleted: true }, { where: condition });
            await DeliverDefine.update({ isDeleted: true }, { where: condition });
            done({ status: 'ok' }, false);
        } catch (e) {
            done(null, e);
        }
    },

    // Helper function to build filter conditions for listNDR
    async buildFilterConditions(incomeData, params, voidInspection, inputData) {
        const condition = {
            ProjectId: +params.ProjectId,
            isQueued: incomeData.queuedNdr,
            isDeleted: false,
        };

        // Handle void filter
        if (params.void === '0' || params.void === 0) {
            condition['$InspectionRequest.id$'] = {
                [Op.and]: [{ [Op.notIn]: voidInspection }],
            };
        } else {
            condition['$InspectionRequest.id$'] = {
                [Op.and]: [{ [Op.in]: voidInspection }],
            };
        }

        // Apply various filters
        if (incomeData.descriptionFilter) {
            condition.description = {
                [Sequelize.Op.iLike]: `%${incomeData.descriptionFilter}%`,
            };
        }

        if (incomeData.pickFrom) {
            condition.cranePickUpLocation = {
                [Sequelize.Op.iLike]: `%${incomeData.pickFrom}%`,
            };
        }

        if (incomeData.pickTo) {
            condition.craneDropOffLocation = {
                [Sequelize.Op.iLike]: `%${incomeData.pickTo}%`,
            };
        }

        if (incomeData.inspectionStatusFilter) {
            condition.inspectionStatus = {
                [Sequelize.Op.iLike]: `%${incomeData.inspectionStatusFilter}%`,
            };
        }

        if (incomeData.inspectionTypeFilter) {
            condition.inspectionType = {
                [Sequelize.Op.iLike]: `%${incomeData.inspectionTypeFilter}%`,
            };
        }

        if (incomeData.equipmentFilter || incomeData.equipmentFilter == 0) {
            condition['$equipmentDetails.Equipment.id$'] = incomeData.equipmentFilter;
        }

        if (incomeData.locationFilter) {
            condition['$location.locationPath$'] = incomeData.locationFilter;
        }

        if (incomeData.statusFilter) {
            if (incomeData.statusFilter === 'Delivered') {
                condition.inspectionStatus = {
                    [Sequelize.Op.or]: [
                        {
                            [Sequelize.Op.iLike]: 'Pass',
                        },
                        {
                            [Sequelize.Op.iLike]: 'Fail',
                        }
                    ]
                };
            } else if (incomeData.statusFilter === 'Approved') {
                condition.status = incomeData.statusFilter;
                condition.inspectionStatus = null;
            } else {
                condition.status = incomeData.statusFilter;
            }
        }

        if (incomeData.dateFilter) {
            const startDateTime = moment(incomeData.dateFilter, 'YYYY-MM-DD')
                .startOf('day')
                .utcOffset(Number(inputData.headers.timezoneoffset), true);
            const endDateTime = moment(incomeData.dateFilter, 'YYYY-MM-DD')
                .endOf('day')
                .utcOffset(Number(inputData.headers.timezoneoffset), true);
            condition.inspectionStart = {
                [Op.between]: [moment(startDateTime), moment(endDateTime)],
            };
        }

        return condition;
    },

    // Helper function to build search conditions for listNDR
    async buildSearchConditions(incomeData, params) {
        let searchCondition = {};

        if (incomeData.search) {
            const searchDefault = [
                {
                    '$approverDetails.User.firstName$': {
                        [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                    },
                },
                {
                    '$equipmentDetails.Equipment.equipmentName$': {
                        [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                    },
                },
                {
                    '$location.locationPath$': {
                        [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                    },
                },
                {
                    description: {
                        [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                    },
                },
                {
                    cranePickUpLocation: {
                        [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                    },
                },
                {
                    craneDropOffLocation: {
                        [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                    },
                },
            ];

            if (!Number.isNaN(+incomeData.search)) {
                searchCondition = {
                    [Op.and]: [
                        {
                            [Op.or]: [
                                searchDefault,
                                {
                                    [Op.and]: [
                                        {
                                            InspectionId: +incomeData.search,
                                            isDeleted: false,
                                            ProjectId: +params.ProjectId,
                                        },
                                    ],
                                },
                            ],
                        },
                    ],
                };
            } else {
                searchCondition = {
                    [Op.and]: [
                        {
                            [Op.or]: searchDefault,
                        },
                    ],
                };
            }
        }

        return searchCondition;
    },

    // Helper function to handle sorting logic
    async applySorting(response, sort, sortByField) {
        if (sort === 'ASC') {
            response.sort(function (a, b) {
                if (a[sortByField] > b[sortByField]) {
                    return 1;
                } else if (b[sortByField] > a[sortByField]) {
                    return -1;
                } else {
                    return 0;
                }
            });
        } else {
            response.sort(function (a, b) {
                if (b[sortByField] > a[sortByField]) {
                    return 1;
                } else if (a[sortByField] > b[sortByField]) {
                    return -1;
                } else {
                    return 0;
                }
            });
        }
        return response;
    },

    async listNDR(inputData, done) {
        try {
            await this.getDynamicModel(inputData);
            const { params } = inputData;
            const loginUser = inputData.user;
            const incomeData = inputData.body;
            let { sort } = inputData.body;
            let { sortByField } = inputData.body;
            let order;

            if (params.void >= 1 && params.void <= 0) {
                return done(null, { message: 'Please enter void as 1 or 0' });
            }

            const memberDetails = await Member.findOne({
                where: Sequelize.and({
                    UserId: loginUser.id,
                    ProjectId: params.ProjectId,
                    isDeleted: false,
                    isActive: true,
                }),
            });

            if (!memberDetails) {
                return done(null, { message: 'Project Id/Member does not exist' });
            }

            // Get void inspection list
            const voidList = await VoidList.findAll({
                where: {
                    ProjectId: params.ProjectId,
                    InspectionRequestId: { [Op.ne]: null },
                },
            });
            const voidInspection = voidList.map(element => element.InspectionRequestId);

            // Build filter conditions
            const condition = await this.buildFilterConditions(incomeData, params, voidInspection, inputData);

            // Handle upcoming filter
            if (incomeData.upcoming) {
                condition.inspectionStart = {
                    [Op.gt]: new Date(),
                };
                order = 'ASC';
                sort = 'ASC';
                sortByField = 'inspectionStart';
            }

            // Build search conditions
            const searchCondition = await this.buildSearchConditions(incomeData, params);

            const offset = (+params.pageNo - 1) * +params.pageSize;
            const roleId = memberDetails.RoleId;
            const memberId = memberDetails.id;

            // Determine if we need to use calendar data or regular data
            const needsCalendarData = incomeData.companyFilter ||
                incomeData.gateFilter ||
                incomeData.memberFilter ||
                incomeData.assignedFilter ||
                (memberDetails.RoleId === 4 &&
                    (params.void === '0' || params.void === 0) &&
                    !incomeData.upcoming) ||
                (memberDetails.RoleId === 3 &&
                    (params.void === '0' || params.void === 0) &&
                    !incomeData.upcoming);

            if (needsCalendarData) {
                const processCalendarParams = { condition, roleId, memberId, searchCondition, order, sort, sortByField, incomeData, params, offset, inputData, memberDetails };
                await this.processCalendarDataRequest(processCalendarParams, done);
            } else {
                const processParamsData = { condition, roleId, memberId, searchCondition, order, sort, sortByField, incomeData, params, offset, inputData }
                await this.processRegularDataRequest(processParamsData, done);
            }

        } catch (e) {
            done(null, e);
        }
    },

    // Helper function to process calendar data requests
    async processCalendarDataRequest(paramsData, done) {
        const { condition, roleId, memberId, searchCondition, order, sort, sortByField, incomeData, params, offset, inputData, memberDetails } = paramsData;
        const result = { count: 0, rows: [] };
        const inspectionList = await InspectionRequest.getCalendarData(
            condition,
            roleId,
            memberId,
            searchCondition,
            order,
            sort,
            sortByField,
        );

        this.getSearchData({
            incomeData,
            inspectionList: inspectionList.rows,
            result: [],
            limit: +params.pageSize,
            index: 0,
            count: 0,
            memberDetails
        },
            async (checkResponse, checkError) => {
                if (!checkError) {
                    this.getLimitData(
                        checkResponse,
                        0,
                        +params.pageSize,
                        [],
                        incomeData,
                        inputData.headers.timezoneoffset,
                        async (newResponse, newError) => {
                            if (!newError) {
                                const sortedResponse = await this.applySorting(newResponse, sort, sortByField);
                                result.rows = sortedResponse.slice(offset, offset + +params.pageSize);
                                result.count = checkResponse.length;
                                done(result, false);
                            } else {
                                done(null, { message: 'Something went wrong' });
                            }
                        },
                    );
                } else {
                    done(null, { message: 'Something went wrong' });
                }
            },
        );
    },

    // Helper function to process regular data requests
    async processRegularDataRequest(paramsData, done) {
        const { condition, roleId, memberId, searchCondition, order, sort, sortByField, incomeData, params, offset, inputData } = paramsData;
        const newResult = { count: 0, rows: [] };
        const inspectionList = await InspectionRequest.getAll(
            condition,
            roleId,
            memberId,
            +params.pageSize,
            offset,
            searchCondition,
            order,
            sort,
            sortByField,
        );

        this.getLimitData(
            inspectionList,
            0,
            +params.pageSize,
            [],
            incomeData,
            inputData.headers.timezoneoffset,
            async (newResponse, newError) => {
                if (!newError) {
                    const sortedResponse = await this.applySorting(newResponse, sort, sortByField);
                    newResult.rows = sortedResponse.slice(offset, offset + +params.pageSize);
                    newResult.count = inspectionList.length;
                    done(newResult, false);
                } else {
                    done(null, { message: 'Something went wrong' });
                }
            },
        );
    },

    // Refactored getLimitData function - simplified without recursion
    async getLimitData(result, index, limit, finalResult, incomeData, timezoneoffset, done) {
        if (index < limit) {
            finalResult.push(result);
            this.getLimitData(
                result,
                index + 1,
                limit,
                finalResult,
                incomeData,
                timezoneoffset,
                (response, err) => {
                    if (!err) {
                        done(response, false);
                    } else {
                        done(null, err);
                    }
                },
            );
        } else {
            done(result, false);
        }
    },

    // Helper function to check company filter condition
    async checkCompanyFilterCondition(incomeData, elementId) {
        if (!incomeData.companyFilter) {
            return true;
        }

        const data = await InspectionCompany.findOne({
            where: {
                InspectionId: elementId,
                CompanyId: incomeData.companyFilter,
                isDeleted: false,
            },
        });
        return !!data;
    },

    // Helper function to check gate filter condition
    async checkGateFilterCondition(incomeData, elementId) {
        if (!incomeData.gateFilter) {
            return true;
        }

        const data = await InspectionGate.findOne({
            where: {
                InspectionId: elementId,
                GateId: incomeData.gateFilter,
                isDeleted: false,
            },
        });
        return !!data;
    },

    // Helper function to check member filter condition
    async checkMemberFilterCondition(incomeData, elementId) {
        if (!incomeData.memberFilter) {
            return true;
        }

        const data = await InspectionPerson.findOne({
            where: {
                InspectionId: elementId,
                MemberId: incomeData.memberFilter,
                isDeleted: false,
            },
        });
        return !!data;
    },

    // Helper function to check if element passes all filter conditions
    async checkElementFilterConditions(incomeData, element) {
        const companyCondition = await this.checkCompanyFilterCondition(incomeData, element.id);
        const gateCondition = await this.checkGateFilterCondition(incomeData, element.id);
        const memberCondition = await this.checkMemberFilterCondition(incomeData, element.id);

        return companyCondition && gateCondition && memberCondition;
    },

    // Refactored getSearchData function using iteration instead of recursion
    async checkFilters(element, incomeData) {
        const status = { companyCondition: true, gateCondition: true, memberCondition: true };

        if (incomeData.companyFilter) {
            const data = await InspectionCompany.findOne({
                where: {
                    InspectionId: element.id,
                    CompanyId: incomeData.companyFilter,
                    isDeleted: false,
                },
            });
            if (!data) status.companyCondition = false;
        }

        if (incomeData.gateFilter) {
            const data = await InspectionGate.findOne({
                where: {
                    InspectionId: element.id,
                    GateId: incomeData.gateFilter,
                    isDeleted: false,
                },
            });
            if (!data) status.gateCondition = false;
        }

        if (incomeData.memberFilter) {
            const data = await InspectionPerson.findOne({
                where: {
                    InspectionId: element.id,
                    MemberId: incomeData.memberFilter,
                    isDeleted: false,
                },
            });
            if (!data) status.memberCondition = false;
        }

        return status;
    },

    async getSearchData(params, done) {
        const { incomeData, inspectionList, result, limit, index, count, memberDetails } = params;
        const elementValue = inspectionList[index];

        if (!elementValue) return done(result, false);

        const element = JSON.parse(JSON.stringify(elementValue));
        const status = await this.checkFilters(element, incomeData);

        if (status.companyCondition && status.gateCondition && status.memberCondition) {
            result.push(element);
        }

        if (index < inspectionList.length - 1) {
            return this.getSearchData(
                { incomeData, inspectionList, result, limit, index: index + 1, count: count + 1, memberDetails },
                (response, err) => {
                    if (!err) done(response, false);
                    else done(null, err);
                }
            );
        }

        return done(result, false);
    },

    async getNDRData(inputData, done) {
        try {
            await this.getDynamicModel(inputData);
            const { params } = inputData;
            const condition = {
                id: params.inspectionRequestId,
            };
            const inspectionList = await InspectionRequest.getNDRData(condition);
            done(inspectionList, false);
        } catch (e) {
            done(null, e);
        }
    },
    async getMemberData(inputData, done) {
        try {
            await this.getDynamicModel(inputData);
            const { params } = inputData;
            const condition = {
                UserId: inputData.user.id,
                ProjectId: params.ProjectId,
            };
            const memberData = await Member.getBy(condition);
            done(memberData, false);
        } catch (e) {
            done(null, e);
        }
    },

    // Helper function to validate NDR status update permissions
    async validateNDRStatusUpdatePermissions(updateData, loginUser, statusValue) {
        const memberValue = await Member.findOne({
            where: Sequelize.and({
                UserId: loginUser.id,
                ProjectId: statusValue.ProjectId,
                isDeleted: false,
            }),
        });

        if (!memberValue) {
            return { error: true, message: 'Member not found in project.' };
        }

        if (![1, 2, 3, 4].includes(memberValue.RoleId)) {
            return { error: true, message: 'Insufficient permissions to update status.' };
        }

        // Special validation for SC role
        if (memberValue.RoleId === 4) {
            const NDRData = await InspectionRequest.getNDRData({ id: updateData.id });
            if (+loginUser.id !== +NDRData.createdUserDetails.User.id) {
                return {
                    error: true,
                    message: 'SC can able to Inspection the NDR which was created by him only.'
                };
            }
        }

        return { error: false, memberValue };
    },

    // Helper function to get location and notification preferences
    async getLocationAndNotificationPreferences(statusValue, memberValue) {
        const locationChosen = await Locations.findOne({
            where: {
                ProjectId: statusValue.ProjectId,
                id: statusValue.LocationId,
            },
        });

        const memberLocationPreference = await LocationNotificationPreferences.findAll({
            where: {
                ProjectId: statusValue.ProjectId,
                LocationId: statusValue.LocationId,
                follow: true,
            },
            include: [
                {
                    association: 'Member',
                    attributes: ['id', 'RoleId'],
                    where: {
                        [Op.and]: [
                            {
                                id: { [Op.ne]: memberValue.id },
                            },
                        ],
                    },
                    include: [
                        {
                            association: 'User',
                            attributes: ['id', 'firstName', 'lastName', 'email'],
                        },
                    ],
                },
            ],
        });

        return { locationChosen, memberLocationPreference };
    },

    // Helper function to prepare history and notification data
    async prepareHistoryAndNotificationData(statusValue, memberValue, loginUser, updateData, locationChosen) {
        const history = {
            InspectionRequestId: statusValue.id,
            MemberId: memberValue.id,
            InspectionId: statusValue.InspectionId,
        };

        const notification = { ...history };
        notification.ProjectId = statusValue.ProjectId;

        if (statusValue?.recurrence?.recurrence) {
            notification.recurrenceType = `${statusValue.recurrence.recurrence} From ${moment(
                statusValue.recurrence.recurrenceStartDate,
            ).format('MM/DD/YYYY')} to ${moment(
                statusValue.recurrence.recurrenceEndDate,
            ).format('MM/DD/YYYY')}`;
        }

        notification.requestType = 'InspectionRequest';

        return { history, notification };
    },

    // Helper function to handle approved status
    async handleApprovedStatus(updateData, statusValue, memberValue, loginUser, history, notification, locationChosen) {
        if (updateData.statuschange && updateData.statuschange === 'Reverted') {
            history.type = 'approved';
            history.description = `${loginUser.firstName} ${loginUser.lastName} Reverted the status from Inspection to approved for Inspection Booking , ${statusValue.description}`;
            history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Reverted the status from Inspection to approved for Inspection Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;
        } else {
            history.type = 'approved';
            history.description = `${loginUser.firstName} ${loginUser.lastName} Approved the Inspection Booking`;
            history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Approved the Inspection Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;
        }

        if (updateData.inspectionStatus) {
            notification.title = `Inspection Booking is Passed by ${loginUser.firstName} ${loginUser.lastName}`;
            await InspectionRequest.update(
                { inspectionStatus: updateData.inspectionStatus, approvedBy: memberValue.id, approved_at: new Date() },
                { where: { id: updateData.id } },
            );
        } else {
            notification.title = `Inspection Booking Approved by ${loginUser.firstName} ${loginUser.lastName}`;
            await InspectionRequest.update(
                { status: updateData.status, approvedBy: memberValue.id, approved_at: new Date() },
                { where: { id: updateData.id } },
            );
        }

        const object = {
            ProjectId: statusValue.ProjectId,
            MemberId: memberValue.id,
            InspectionRequestId: statusValue.id,
            isDeleted: false,
            type: updateData.status.toLowerCase(),
            description: history.description,
        };
        await InspectionHistory.createInstance(object);

        return { history, notification };
    },

    // Helper function to handle declined or delivered status
    async handleDeclinedOrDeliveredStatus(updateData, statusValue, memberValue, loginUser, history, notification, locationChosen) {
        history.type = updateData.status.toLowerCase();
        history.description = `${loginUser.firstName} ${loginUser.lastName} ${updateData.status} the Inspection Booking`;
        history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} ${updateData.status} the Inspection Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;

        notification.title = `Inspection Booking was Completed and ${updateData.inspectionStatus} by ${loginUser.firstName} ${loginUser.lastName}`;
        await InspectionRequest.update(
            { inspectionStatus: updateData.inspectionStatus, status: updateData.status, approvedBy: memberValue.id, approved_at: new Date() },
            { where: { id: updateData.id } },
        );

        const object = {
            ProjectId: statusValue.ProjectId,
            MemberId: memberValue.id,
            InspectionRequestId: statusValue.id,
            isDeleted: false,
            type: updateData.status.toLowerCase(),
            description: history.description,
        };
        await InspectionHistory.createInstance(object);

        return { history, notification };
    },

    // Helper function to get member and admin data for notifications
    async getMemberAndAdminDataForNotifications(statusValue, memberLocationPreference, memberValue, newNotification) {
        const locationFollowMembers = [];
        memberLocationPreference.forEach(async (element) => {
            locationFollowMembers.push(element.Member.id);
        });

        const NDRData = await InspectionRequest.getNDRData({ id: statusValue.id });
        const bookingMemberDetails = [];
        NDRData.memberDetails.forEach(async (element) => {
            bookingMemberDetails.push(element.Member.id);
        });

        const personData = await InspectionPerson.findAll({
            where: { InspectionId: statusValue.id, isDeleted: false },
            include: [
                {
                    association: 'Member',
                    include: [
                        {
                            association: 'User',
                            attributes: ['id', 'firstName', 'lastName', 'email'],
                        },
                    ],
                    where: {
                        [Op.and]: {
                            RoleId: {
                                [Op.notIn]: [1, 2],
                            },
                            id: { [Op.notIn]: locationFollowMembers },
                        },
                    },
                    attributes: ['id', 'RoleId'],
                },
            ],
            attributes: ['id'],
        });

        const adminData = await Member.findAll({
            where: {
                [Op.and]: [
                    { ProjectId: statusValue.ProjectId },
                    { isDeleted: false },
                    { id: { [Op.in]: bookingMemberDetails } },
                    { id: { [Op.ne]: newNotification.MemberId } },
                    { id: { [Op.notIn]: locationFollowMembers } },
                ],
            },
            include: [
                {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName', 'email'],
                },
            ],
            attributes: ['id'],
        });

        return { personData, adminData, locationFollowMembers, bookingMemberDetails };
    },


    async updateNDRStatus(inputData, done) {
        try {
            await this.getDynamicModel(inputData);
            const updateData = inputData.body;
            const loginUser = inputData.user;

            const statusValue = await InspectionRequest.findOne({
                where: { id: updateData.id },
                include: [
                    {
                        association: 'recurrence',
                        required: false,
                        attributes: [
                            'id',
                            'recurrence',
                            'recurrenceStartDate',
                            'recurrenceEndDate',
                            'dateOfMonth',
                            'monthlyRepeatType',
                            'repeatEveryCount',
                            'days',
                            'requestType',
                            'repeatEveryType',
                            'chosenDateOfMonth',
                            'createdBy',
                            'chosenDateOfMonthValue',
                        ],
                    },
                ],
            });

            if (!statusValue) {
                return done(null, { message: 'Id does not exist.' });
            }

            // Validate permissions
            const permissionResult = await this.validateNDRStatusUpdatePermissions(updateData, loginUser, statusValue);
            if (permissionResult.error) {
                return done(null, { message: permissionResult.message });
            }
            const { memberValue } = permissionResult;

            // Get location and notification preferences
            const { locationChosen, memberLocationPreference } = await this.getLocationAndNotificationPreferences(statusValue, memberValue);

            // Prepare history and notification data
            const { history, notification } = await this.prepareHistoryAndNotificationData(statusValue, memberValue, loginUser, updateData, locationChosen);

            // Handle different status types
            if (updateData.status === 'Approved') {
                const result = await this.handleApprovedStatus(updateData, statusValue, memberValue, loginUser, history, notification, locationChosen);
                await this.processApprovedStatusNotifications(result.history, result.notification, statusValue, memberLocationPreference, memberValue, loginUser, done);
            } else if (updateData.status === 'Declined' || updateData.status === 'Delivered') {
                const result = await this.handleDeclinedOrDeliveredStatus(updateData, statusValue, memberValue, loginUser, history, notification, locationChosen);
                const paramsData = { history: result.history, notificaiton: result.notification, statusValue, memberLocationPreference, memberValue, loginUser, updateData }
                await this.processDeclinedOrDeliveredStatusNotifications(paramsData, done);
            } else {
                return done(null, { message: 'Invalid Status' });
            }

        } catch (e) {
            return done(null, e);
        }
    },

    // Helper function to process approved status notifications
    async processApprovedStatusNotifications(history, notification, statusValue, memberLocationPreference, memberValue, loginUser, done) {
        // Add common history properties
        history.firstName = loginUser.firstName;
        history.profilePic = loginUser.profilePic;
        history.createdAt = new Date();
        history.ProjectId = statusValue.ProjectId;

        const projectDetails = await Project.findByPk(statusValue.ProjectId);
        history.projectName = projectDetails.projectName;

        const newNotification = await Notification.createInstance(notification);

        // Get member and admin data
        const { personData, adminData } = await this.getMemberAndAdminDataForNotifications(statusValue, memberLocationPreference, memberValue, newNotification);

        // Send notifications
        if (memberLocationPreference && memberLocationPreference.length > 0) {
            await pushNotification.sendMemberLocationPreferencePushNotification(
                memberLocationPreference,
                statusValue.InspectionRequestId,
                history.locationFollowDescription,
                statusValue.requestType,
                statusValue.ProjectId,
                statusValue.id,
                6,
            );

            await notificationHelper.createMemberDeliveryLocationInAppNotification(
                DeliveryPersonNotification,
                statusValue.ProjectId,
                newNotification.id,
                memberLocationPreference,
                6,
            );
        }

        history.memberData = personData;
        history.adminData = adminData;

        await notificationHelper.createDeliveryPersonNotification(
            adminData,
            personData,
            projectDetails,
            newNotification,
            DeliveryPersonNotification,
            memberValue,
            loginUser,
            6,
            'approved a',
            'Inspection Request',
            `Inspection Booking (${statusValue.InspectionId} - ${statusValue.description})`,
            statusValue.id,
        );

        // Get notification preferences and send push notifications
        const checkMemberNotification = await NotificationPreference.findAll({
            where: {
                ProjectId: statusValue.ProjectId,
                isDeleted: false,
            },
            attributes: [
                'id',
                'MemberId',
                'ProjectId',
                'ParentCompanyId',
                'NotificationPreferenceItemId',
                'instant',
                'dailyDigest',
            ],
            include: [
                {
                    association: 'NotificationPreferenceItem',
                    where: {
                        id: 6,
                        isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                },
            ],
        });

        history.notificationPreference = checkMemberNotification;
        await pushNotification.sendDeviceTokenForInspection(history, 6, statusValue.ProjectId);

        if (memberLocationPreference && memberLocationPreference.length > 0) {
            history.memberData.push(...memberLocationPreference);
        }

        // Send guest user notifications
        await this.sendGuestUserNotifications(statusValue, loginUser, 'Approved');

        return done(history, false);
    },

    // Helper function to send guest user notifications
    async sendGuestUserNotifications(statusValue, loginUser, status) {
        const exist = await InspectionRequest.findOne({
            include: [
                {
                    association: 'memberDetails',
                    required: false,
                    where: { isDeleted: false, isActive: true },
                    attributes: ['id'],
                    include: [
                        {
                            association: 'Member',
                            attributes: ['id', 'isGuestUser'],
                            include: [
                                {
                                    association: 'User',
                                    attributes: [
                                        'email',
                                        'phoneCode',
                                        'phoneNumber',
                                        'firstName',
                                        'lastName',
                                    ],
                                },
                            ],
                        },
                    ],
                },
            ],
            where: { id: statusValue.id },
        });

        if (exist?.memberDetails) {
            const userDataMail = exist.memberDetails;
            for (const userMail of userDataMail) {
                const responsibleGuestUser = userMail.Member.isGuestUser;
                if (responsibleGuestUser) {
                    const guestMailPayload = {
                        email: userMail.Member.User.email,
                        guestName: userMail.Member.User.firstName,
                        content: `We would like to inform you that
                    ${loginUser.firstName} ${loginUser.lastName} ${status} the Inspection Booking - ${statusValue.description}.`,
                    };
                    await MAILER.sendMail(
                        guestMailPayload,
                        'notifyGuestOnEdit',
                        `Inspection Booking ${status} by ${loginUser.firstName} `,
                        `Inspection Booking ${status}`,
                        async (info, err) => {
                            console.log(info, err);
                        },
                    );
                }
            }
        }
    },

    // Helper function to process declined or delivered status notifications
    async processDeclinedOrDeliveredStatusNotifications(params, done) {
        const { history, notification, statusValue, memberLocationPreference, memberValue, loginUser, updateData } = params;
        // Add common history properties
        history.firstName = loginUser.firstName;
        history.profilePic = loginUser.profilePic;
        history.createdAt = new Date();
        history.ProjectId = statusValue.ProjectId;

        const projectDetails = await Project.findByPk(statusValue.ProjectId);
        history.projectName = projectDetails.projectName;

        const newNotification = await Notification.createInstance(notification);

        // Get member and admin data
        const { personData, adminData } = await this.getMemberAndAdminDataForNotifications(statusValue, memberLocationPreference, memberValue, newNotification);

        // Send notifications similar to approved status
        if (memberLocationPreference && memberLocationPreference.length > 0) {
            await pushNotification.sendMemberLocationPreferencePushNotification(
                memberLocationPreference,
                statusValue.InspectionRequestId,
                history.locationFollowDescription,
                statusValue.requestType,
                statusValue.ProjectId,
                statusValue.id,
                6,
            );

            await notificationHelper.createMemberDeliveryLocationInAppNotification(
                DeliveryPersonNotification,
                statusValue.ProjectId,
                newNotification.id,
                memberLocationPreference,
                6,
            );
        }

        history.memberData = personData;
        history.adminData = adminData;

        // Get notification preferences and send push notifications
        const checkMemberNotification = await NotificationPreference.findAll({
            where: {
                ProjectId: statusValue.ProjectId,
                isDeleted: false,
            },
            attributes: [
                'id',
                'MemberId',
                'ProjectId',
                'ParentCompanyId',
                'NotificationPreferenceItemId',
                'instant',
                'dailyDigest',
            ],
            include: [
                {
                    association: 'NotificationPreferenceItem',
                    where: {
                        id: 6,
                        isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                },
            ],
        });

        history.notificationPreference = checkMemberNotification;

        await notificationHelper.createDeliveryPersonNotification(
            adminData,
            personData,
            projectDetails,
            newNotification,
            DeliveryPersonNotification,
            memberValue,
            loginUser,
            6,
            `${updateData.status.toLowerCase()}`,
            'Inspection Request',
            `Inspection Booking (${statusValue.InspectionId} - ${statusValue.description})`,
            statusValue.id,
        );

        await pushNotification.sendDeviceTokenForInspection(history, 6, statusValue.ProjectId);

        if (memberLocationPreference && memberLocationPreference.length > 0) {
            history.memberData.push(...memberLocationPreference);
        }

        // Send guest user notifications
        await this.sendGuestUserNotifications(statusValue, loginUser, updateData.status);

        // Handle special case for 'Delivered' status
        if (updateData.status === 'Delivered') {
            const userEmails = await this.getMemberDetailData(history, memberLocationPreference);
            if (userEmails.length > 0) {
                await this.sendSpecialDeliveredEmailNotifications(userEmails, memberValue, loginUser, statusValue);
            }
        }

        return done(history, false);
    },

    // Helper function to send special email notifications for delivered status
    async sendSpecialDeliveredEmailNotifications(userEmails, memberValue, loginUser, statusValue) {
        for (const element of userEmails) {
            let name = element.firstName ? `${element.firstName} ${element.lastName}` : 'user';
            const time = moment(statusValue.inspectionStart).format('MM-DD-YYYY');

            const mailPayload = {
                userName: name,
                email: element.email,
                description: statusValue.description,
                userName1: `${loginUser.firstName} ${loginUser.lastName}`,
                inspectionID: statusValue.InspectionId,
                InspectionId: statusValue.InspectionId,
                status_timestamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
                timestamp: time,
            };

            const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
                where: {
                    MemberId: +element.MemberId,
                    ProjectId: +statusValue.ProjectId,
                    LocationId: +statusValue.LocationId,
                    isDeleted: false,
                },
            });

            if (isMemberFollowLocation) {
                const memberNotification = await NotificationPreference.findOne({
                    where: {
                        MemberId: +element.MemberId,
                        ProjectId: +statusValue.ProjectId,
                        isDeleted: false,
                    },
                    include: [
                        {
                            association: 'NotificationPreferenceItem',
                            where: {
                                id: 10,
                                isDeleted: false,
                            },
                        },
                    ],
                });

                if (memberNotification?.instant) {
                    await MAILER.sendMail(
                        mailPayload,
                        'deliveredDR',
                        'Inspection Booking status updated',
                        'Inspection Booking status updated',
                        async (info, err) => {
                            console.log(info, err);
                        },
                    );
                }

                if (memberNotification?.dailyDigest) {
                    await this.createDailyDigestData({
                        RoleId: +memberValue.RoleId,
                        MemberId: +element.MemberId,
                        ProjectId: +statusValue.ProjectId,
                        ParentCompanyId: +statusValue.ParentCompanyId,
                        loginUser,
                        dailyDigestMessage: 'delivered a',
                        requestType: 'Inspection Request',
                        messages: `Inspection Booking (${statusValue.InspectionId} - ${statusValue.description})`,
                        requestId: statusValue.id,
                    });
                }
            }
        }
    },

    // Helper function to send email notifications to users
    async sendEmailNotificationsToUsers(userEmails, memberValue, loginUser, statusValue, history) {
        for (const element of userEmails) {
            let name = element.firstName ? `${element.firstName} ${element.lastName}` : 'user';

            const memberRole = await Role.findOne({
                where: {
                    id: memberValue.RoleId,
                    isDeleted: false,
                },
            });

            const mailPayload = {
                name,
                email: element.email,
                content: `We would like to inform you that
                ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has updated the status of Inspection booking ${statusValue.InspectionId}.`,
            };

            const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
                where: {
                    MemberId: +element.MemberId,
                    ProjectId: +statusValue.ProjectId,
                    LocationId: +statusValue.LocationId,
                    isDeleted: false,
                },
            });

            if (isMemberFollowLocation) {
                const memberNotification = await NotificationPreference.findOne({
                    where: {
                        MemberId: +element.MemberId,
                        ProjectId: +statusValue.ProjectId,
                        isDeleted: false,
                    },
                    include: [
                        {
                            association: 'NotificationPreferenceItem',
                            where: {
                                id: 6,
                                isDeleted: false,
                            },
                        },
                    ],
                });

                if (memberNotification?.instant) {
                    await MAILER.sendMail(
                        mailPayload,
                        'deliveredDR',
                        'Inspection Booking status updated',
                        'Inspection Booking status updated',
                        async (info, err) => {
                            console.log(info, err);
                        },
                    );
                }
            }
        }
    },
    async validateFileAndMember(inputData) {
        const { file } = inputData;
        const ProjectId = +inputData.params.ProjectId;

        // Check if user is a valid member
        const memberDetail = await Member.findOne({
            where: [
                Sequelize.and(
                    {
                        UserId: inputData.user.id,
                        ProjectId,
                        isDeleted: false,
                    },
                    Sequelize.or({ RoleId: [1, 2, 3, 4] }),
                ),
            ],
        });
        if (!memberDetail) {
            return { error: 'Project does not exist or you are not a valid member.' };
        }

        // Validate file presence
        if (!file?.originalname) {
            return { error: 'Please select a file.' };
        }

        // Validate filename structure
        const splitValue = file.originalname.split('.');
        const extension = splitValue.pop();
        const fileName = splitValue.join('.');
        const firstSplitFileName = fileName.split('_');
        if (firstSplitFileName.length !== 3) {
            return { error: 'Invalid file' };
        }

        const projectFileName = firstSplitFileName[0];
        const projectId = firstSplitFileName[1];

        // Validate project name and ID
        const projectDetails = await Project.findByPk(ProjectId);
        if (
            projectDetails.projectName.toLowerCase() !== projectFileName.toLowerCase() ||
            +ProjectId !== +projectId
        ) {
            return { error: 'Invalid file' };
        }

        if (extension !== 'xlsx') {
            return { error: 'Please choose valid file' };
        }

        return { valid: true, file, ProjectId };
    },

    async bulkInspectionRequestUpload(inputData, done) {
        try {
            await this.getDynamicModel(inputData);

            const validation = await this.validateFileAndMember(inputData);
            if (validation.error) {
                return done(null, { message: validation.error });
            }

            const { file } = validation;

            const newWorkbook = new ExcelJS.Workbook();
            await newWorkbook.xlsx.readFile(file.path);
            const worksheet = newWorkbook.getWorksheet('Inspection Booking');

            this.createInspectionRequestFile(worksheet, inputData, (resValue, error) => {
                if (!error) {
                    return done(resValue, false);
                }
                return done(null, error);
            });
        } catch (e) {
            done(null, e);
        }
    },

    async createInspectionRequestFile(InspectionRequestWorksheet, inputData, done) {
        try {
            await this.getDynamicModel(inputData);
            const existProjectId = inputData.params.ProjectId;
            const ProjectId = +existProjectId;
            const loginUser = inputData.user;
            const projectDetails = await Project.findByPk(ProjectId);
            let fileFormat = true;
            const worksheet = InspectionRequestWorksheet;
            const ndrRecords = [];
            let headers;
            if (worksheet) {
                worksheet.eachRow(async (rowData, rowNumber) => {
                    const singleRowData = rowData.values;
                    singleRowData.shift();
                    if (rowNumber === 2) {
                        headers = rowData.values;
                    } else if (singleRowData.length > 0 && rowNumber >= 2) {
                        const getRow = rowData.values;
                        const description = getRow[2];
                        if (description) {
                            ndrRecords.push(rowData.values);
                        }
                    }
                });
                if (ndrRecords !== undefined && ndrRecords.length === 0) {
                    return done(null, {
                        message: 'Please upload proper document / Please fill Mandatory columns',
                    });
                }
                if (inputData.file) {
                    if (+headers.length === 15) {
                        fileFormat = false;
                    }
                }
                if (fileFormat) {
                    const worker = new Worker(bulkNdrProcess);
                    const object = stringify({
                        projectDetails,
                        loginUser,
                        ndrRecords,
                        ProjectId,
                        inputData,
                    });
                    worker.postMessage(object);
                    worker.on('message', (data) => {
                        if (data === 'success') {
                            const socketObject = {
                                message: data,
                                loginUserId: loginUser.id,
                            };
                            global.io.emit('bulkNdrNotification', socketObject);
                            worker.terminate();
                        }
                    });
                    worker.on('exit', (data) => {
                        console.log('worker thread exit ', data);
                    });
                    done({ message: 'success' }, false);
                } else {
                    done(null, { message: 'Invalid File Format' });
                }
            } else {
                done(null, { message: 'Invalid File' });
            }
        } catch (e) {
            done(null, e);
        }
    },
    async deleteQueuedNdr(input, done) {
        try {
            await this.getDynamicModel(input);
            const reqData = input.body;
            const inputData = {
                isDeleted: true,
            };
            if (reqData.queuedInspectionRequestSelectAll) {
                const deleteValue = await InspectionRequest.update(inputData, {
                    where: {
                        ProjectId: reqData.ProjectId,
                        isQueued: true,
                    },
                });
                done(deleteValue, false);
            } else {
                const { id } = input.body;
                const deleteValue = await InspectionRequest.update(inputData, {
                    where: { id: { [Op.in]: id } },
                });
                done(deleteValue, false);
            }
        } catch (e) {
            done(null, e);
        }
    },
    async editMultipleInspectionRequest(req) {
        try {
            const payload = req.body;
            if (!payload.InspectionRequestIds || payload.InspectionRequestIds.length === 0) {
                return { message: 'Please select Inspection Booking to update.!' };
            }

            await this.getDynamicModel(req);
            const loginUser = req.user;
            const projectSettingDetails = await this.getProjectSettings(payload.ProjectId);
            const editedFields = this.parseEditedFields(payload.editedFields);

            for (let mainIndex = 1; mainIndex <= payload.InspectionRequestIds.length; mainIndex += 1) {
                const inspectionRequestId = payload.InspectionRequestIds[mainIndex - 1];
                const result = await this.processSingleInspectionRequest(
                    payload,
                    inspectionRequestId,
                    loginUser,
                    projectSettingDetails,
                    editedFields,
                    mainIndex
                );

                if (!result.success) {
                    return result;
                }

                if (mainIndex === payload.InspectionRequestIds.length) {
                    return { success: true, data: result.data };
                }
            }

            return { success: true, data: {} };
        } catch (e) {
            return e;
        }
    },

    parseEditedFields(editedFields) {
        const fieldsArray = editedFields.split(',');
        return {
            responsiblePersonsEdited: fieldsArray.includes('Responsible Person'),
            escortEdited: fieldsArray.includes('Escort')
        };
    },

    async getProjectSettings(projectId) {
        return await Project.getProjectAndSettings({
            isDeleted: false,
            id: +projectId,
        });
    },

    async processSingleInspectionRequest(payload, inspectionRequestId, loginUser, projectSettingDetails, editedFields, mainIndex) {
        const inspectionRequestDetail = await this.getInspectionRequestDetail(inspectionRequestId);
        const existsSingleInspectionRequest = await this.getSingleInspectionRequestData(inspectionRequestId);

        const validationResult = await this.validateInspectionRequest(payload, inspectionRequestId, existsSingleInspectionRequest, projectSettingDetails);
        if (validationResult.error) {
            return validationResult;
        }

        const inspectionParam = await this.buildInspectionParam(payload, inspectionRequestDetail, loginUser, projectSettingDetails);

        if (inspectionRequestDetail?.recurrenceId) {
            await this.handleRecurrenceUpdates(payload, inspectionRequestDetail, existsSingleInspectionRequest, loginUser);
        }

        const updateResult = await this.updateInspectionRequestData(
            payload,
            inspectionRequestId,
            inspectionParam,
            inspectionRequestDetail,
            loginUser,
            editedFields,
            projectSettingDetails
        );

        if (!updateResult.success) {
            return updateResult;
        }

        const notificationResult = await this.handleNotificationsData(
            payload,
            inspectionRequestId,
            inspectionRequestDetail,
            existsSingleInspectionRequest,
            loginUser,
            mainIndex
        );

        return notificationResult;
    },

    async getInspectionRequestDetail(inspectionRequestId) {
        return await InspectionRequest.findOne({
            where: [Sequelize.and({ id: inspectionRequestId })],
        });
    },

    async getSingleInspectionRequestData(inspectionRequestId) {
        return await InspectionRequest.getSingleInspectionRequestData({
            id: +inspectionRequestId,
        });
    },

    async validateInspectionRequest(payload, inspectionRequestId, existsSingleInspectionRequest, projectSettingDetails) {
        if (payload.inspectionStart && payload.inspectionEnd) {
            const requestArray = [{
                ProjectId: payload.ProjectId,
                inspectionStart: payload.inspectionStart,
                inspectionEnd: payload.inspectionEnd,
                id: inspectionRequestId,
            }];

            const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                requestArray,
                projectSettingDetails,
                'edit',
                existsSingleInspectionRequest.gateDetails[0].Gate.id,
            );

            if (isOverlapping?.error) {
                return { success: false, message: isOverlapping.message };
            }
        }

        if ((payload.inspectionStart && !payload.inspectionEnd) || (!payload.inspectionStart && payload.inspectionEnd)) {
            return { success: false, message: 'Booking start or end time is missing' };
        }

        return { success: true };
    },

    async buildInspectionParam(payload, inspectionRequestDetail, loginUser, projectSettingDetails) {
        const inspectionParam = {};

        if (payload.EquipmentId && payload.EquipmentId.length > 0) {
            const craneResult = await this.handleCraneRequestLogic(payload, inspectionRequestDetail);
            Object.assign(inspectionParam, craneResult);
        }

        const approvalResult = await this.setApprovalStatusForEdit(inspectionParam, loginUser, projectSettingDetails);
        Object.assign(inspectionParam, approvalResult);

        inspectionParam.recurrenceId = null;
        return inspectionParam;
    },

    async handleCraneRequestLogic(payload, inspectionRequestDetail) {
        const result = {};

        if (payload.isAssociatedWithCraneRequest && !inspectionRequestDetail.isAssociatedWithCraneRequest) {
            const craneId = await this.generateNewCraneRequestId(payload.ProjectId);
            result.CraneRequestId = craneId;
            result.requestType = 'InspectionRequestWithCrane';
            result.cranePickUpLocation = payload.cranePickUpLocation;
            result.craneDropOffLocation = payload.craneDropOffLocation;
            result.isAssociatedWithCraneRequest = payload.isAssociatedWithCraneRequest;
        } else if (payload.isAssociatedWithCraneRequest && inspectionRequestDetail.isAssociatedWithCraneRequest) {
            result.requestType = 'InspectionRequestWithCrane';
            result.cranePickUpLocation = payload.cranePickUpLocation;
            result.craneDropOffLocation = payload.craneDropOffLocation;
            result.isAssociatedWithCraneRequest = payload.isAssociatedWithCraneRequest;
        } else if (!payload.isAssociatedWithCraneRequest) {
            result.requestType = 'InspectionRequest';
            result.cranePickUpLocation = null;
            result.craneDropOffLocation = null;
            result.isAssociatedWithCraneRequest = payload.isAssociatedWithCraneRequest;
        }

        return result;
    },

    async generateNewCraneRequestId(projectId) {
        const lastData = await CraneRequest.findOne({
            where: { ProjectId: +projectId, isDeleted: false },
            order: [['CraneRequestId', 'DESC']],
        });

        const inspectionRequestList = await InspectionRequest.findOne({
            where: {
                ProjectId: +projectId,
                isDeleted: false,
                isAssociatedWithCraneRequest: true,
            },
            order: [['CraneRequestId', 'DESC']],
        });

        let craneRequestId = 1;
        if (lastData && inspectionRequestList) {
            craneRequestId = Math.max(lastData.CraneRequestId, inspectionRequestList.CraneRequestId) + 1;
        } else if (lastData) {
            craneRequestId = lastData.CraneRequestId + 1;
        } else if (inspectionRequestList) {
            craneRequestId = inspectionRequestList.CraneRequestId + 1;
        }

        return craneRequestId;
    },

    async setApprovalStatusForEdit(inspectionParam, loginUser, projectSettingDetails) {
        const memberData = await Member.getBy({
            UserId: loginUser.id,
            ProjectId: +inspectionParam.ProjectId,
        });

        if ((memberData.RoleId === 2 || memberData.RoleId === 1) ||
            memberData.isAutoApproveEnabled ||
            projectSettingDetails.ProjectSettings.isAutoApprovalEnabled) {
            return {
                status: 'Approved',
                approvedBy: memberData.id,
                approved_at: new Date()
            };
        }

        return {};
    },

    async handleRecurrenceUpdates(payload, inspectionRequestDetail, existsSingleInspectionRequest, loginUser) {
        const { recurrenceId } = inspectionRequestDetail;

        const previousSeriesRecurrenceEndDate = moment(inspectionRequestDetail.inspectionStart)
            .add(-1, 'days')
            .format('YYYY-MM-DD');
        const utcPreviousSeriesRecurrenceEndDate = moment.tz(
            `${previousSeriesRecurrenceEndDate}  '00:00'`,
            'YYYY-MM-DD HH:mm',
            payload.timezone,
        ).clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');

        const nextSeriesRecurrenceStartDate = moment(inspectionRequestDetail.inspectionStart)
            .add(1, 'days')
            .format('YYYY-MM-DD');
        const utcNextSeriesRecurrenceStartDate = moment.tz(
            `${nextSeriesRecurrenceStartDate}  '00:00'`,
            'YYYY-MM-DD HH:mm',
            payload.timezone,
        ).clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');

        await this.updateRecurrenceSeries(recurrenceId, utcPreviousSeriesRecurrenceEndDate, utcNextSeriesRecurrenceStartDate, payload, loginUser, existsSingleInspectionRequest);
    },

    async updateRecurrenceSeries(recurrenceId, utcPreviousSeriesRecurrenceEndDate, utcNextSeriesRecurrenceStartDate, payload, loginUser, existsSingleInspectionRequest) {
        const previousRecordInThisEventSeries = await InspectionRequest.findAll({
            where: [Sequelize.and({
                recurrenceId,
                id: { [Op.lt]: recurrenceId },
                isDeleted: false,
            })],
            order: [['id', 'DESC']],
        });

        const nextSeriesLastRecord = await InspectionRequest.findAll({
            where: [Sequelize.and({
                recurrenceId,
                id: { [Op.gt]: recurrenceId },
                isDeleted: false,
            })],
            order: [['id', 'DESC']],
        });

        await this.processRecurrenceSeriesUpdates(
            previousRecordInThisEventSeries,
            nextSeriesLastRecord,
            utcPreviousSeriesRecurrenceEndDate,
            utcNextSeriesRecurrenceStartDate,
            payload,
            loginUser,
            existsSingleInspectionRequest
        );
    },

    async processRecurrenceSeriesUpdates(previousRecords, nextRecords, utcPreviousEnd, utcNextStart, payload, loginUser, existsSingleInspectionRequest) {
        if (previousRecords && previousRecords.length === 1) {
            await InspectionRequest.update(
                { recurrenceId: null },
                { where: { id: previousRecords[0].id } }
            );
        }

        if (nextRecords && nextRecords.length === 1) {
            await InspectionRequest.update(
                { recurrenceId: null },
                { where: { id: nextRecords[0].id } }
            );
        }

        if (previousRecords && previousRecords.length > 1) {
            await RequestRecurrenceSeries.update(
                { recurrenceEndDate: utcPreviousEnd },
                { where: { id: previousRecords[0].recurrenceId } }
            );
        }

        if (nextRecords && nextRecords.length > 1) {
            if (!previousRecords || previousRecords.length <= 1) {
                await RequestRecurrenceSeries.update(
                    { recurrenceStartDate: utcNextStart },
                    { where: { id: nextRecords[0].recurrenceId } }
                );
            } else if (previousRecords && previousRecords.length > 1) {
                await this.createNewRecurrenceSeries(payload, loginUser, existsSingleInspectionRequest, utcNextStart, nextRecords);
            }
        }
    },

    async createNewRecurrenceSeries(payload, loginUser, existsSingleInspectionRequest, utcNextStart, nextRecords) {
        const recurrenceObject = {
            ProjectId: payload.ProjectId,
            ParentCompanyId: payload.ParentCompanyId,
            recurrence: existsSingleInspectionRequest.recurrence.recurrence,
            repeatEveryCount: existsSingleInspectionRequest.recurrence.repeatEveryCount,
            repeatEveryType: existsSingleInspectionRequest.recurrence.repeatEveryType,
            days: existsSingleInspectionRequest.recurrence.days,
            dateOfMonth: existsSingleInspectionRequest.recurrence.dateOfMonth,
            chosenDateOfMonth: existsSingleInspectionRequest.recurrence.chosenDateOfMonth,
            monthlyRepeatType: existsSingleInspectionRequest.recurrence.monthlyRepeatType,
            requestType: existsSingleInspectionRequest.requestType,
            createdBy: loginUser.id,
            recurrenceStartDate: utcNextStart,
            recurrenceEndDate: existsSingleInspectionRequest.recurrence.recurrenceEndDate,
        };

        const recurrenceSeries = await RequestRecurrenceSeries.createInstance(recurrenceObject);
        const newRecurrenceId = recurrenceSeries.id;

        for (const element of nextRecords) {
            await InspectionRequest.update(
                { recurrenceId: newRecurrenceId },
                { where: { id: element.id } }
            );
        }
    },

    async updateInspectionRequestData(payload, inspectionRequestId, inspectionParam, inspectionRequestDetail, loginUser, editedFields, projectSettingDetails) {
        const idDetails = await InspectionRequest.findOne({
            where: [Sequelize.and({ id: inspectionRequestId })],
        });

        if (!idDetails) {
            return { success: false, message: 'something Went wrong!!!' };
        }

        await InspectionRequest.update(inspectionParam, { where: { id: idDetails.id } });

        const memberData = await Member.getBy({
            UserId: loginUser.id,
            ProjectId: +payload.ProjectId,
        });

        const history = this.createHistoryObject(idDetails, loginUser, memberData);

        await this.updateAssociations(payload, idDetails, memberData, history, loginUser, editedFields);
        await this.updateInspectionTimes(payload, inspectionRequestId);
        await this.handleVoidRequest(payload, inspectionRequestId, memberData, loginUser, inspectionRequestDetail);
        await this.handleStatusUpdate(payload, inspectionRequestId, memberData);

        const updatedInspectionRequest = await InspectionRequest.getSingleInspectionRequestData({ id: +idDetails.id });
        await this.handleStatusChanges(updatedInspectionRequest, idDetails, memberData, projectSettingDetails, payload, loginUser, inspectionRequestDetail);

        return { success: true, data: history };
    },

    createHistoryObject(idDetails, loginUser, memberData) {
        return {
            InspectionRequestId: idDetails.id,
            InspectionId: idDetails.InspectionId,
            MemberId: memberData.id,
            type: 'edit',
            description: `${loginUser.firstName} ${loginUser.lastName} Edited this Inspection Booking.`,
        };
    },

    async updateAssociations(payload, idDetails, memberData, history, loginUser, editedFields) {
        const condition = Sequelize.and({
            ProjectId: +payload.ProjectId,
            InspectionId: idDetails.id,
        });

        const updateParam = {
            InspectionId: idDetails.id,
            inspectionCode: idDetails.InspectionId,
            ProjectId: idDetails.ProjectId,
            isDeleted: false,
        };

        if (payload.companies && payload.companies.length > 0) {
            await this.updateCompanyAssociations(payload, condition, updateParam, history, loginUser);
        }

        if (editedFields.responsiblePersonsEdited && payload.persons && payload.persons.length > 0) {
            await this.updatePersonAssociations(payload, condition, updateParam, history, loginUser);
        }

        if (payload.define && payload.define.length > 0) {
            await this.updateDefineAssociations(payload, condition, updateParam, history, loginUser);
        }

        if (editedFields.escortEdited && payload.escort) {
            await InspectionRequest.update(
                { escort: payload.escort },
                { where: { id: idDetails.id } }
            );
        }

        if (payload.GateId) {
            await this.updateGateAssociations(payload, condition, updateParam, history, loginUser);
        }

        if (payload.EquipmentId && payload.EquipmentId.length > 0) {
            await this.updateEquipmentAssociations(payload, condition, updateParam, history, loginUser);
        }
    },

    async updateCompanyAssociations(payload, condition, updateParam, history, loginUser) {
        const existCompanies = await InspectionCompany.findAll({ where: condition });
        const deletedCompany = existCompanies.filter(e =>
            payload.companies.indexOf(e.CompanyId) === -1 && e.isDeleted === false
        );

        await InspectionCompany.update({ isDeleted: true }, { where: condition });

        const addedCompany = [];
        for (const element of payload.companies) {
            const index = existCompanies.findIndex(item => item.CompanyId === element);
            const companyParam = { ...updateParam, CompanyId: element };

            if (index !== -1) {
                await InspectionCompany.update(companyParam, { where: { id: existCompanies[index].id } });
                if (existCompanies[index].isDeleted !== false) {
                    addedCompany.push(existCompanies[index]);
                }
            } else {
                const newCompanyData = await InspectionCompany.createInstance(companyParam);
                addedCompany.push(newCompanyData);
            }
        }

        await this.updateCompanyHistory(addedCompany, deletedCompany, history, loginUser);
    },


    async updatePersonAssociations(payload, condition, updateParam, history, loginUser) {
        const existPerson = await InspectionPerson.findAll({ where: condition });
        const deletedPerson = existPerson.filter(e =>
            payload.persons.indexOf(e.MemberId) === -1 && e.isDeleted === false
        );

        await InspectionPerson.update({ isDeleted: true }, { where: condition });

        const addedPerson = [];
        for (const element of payload.persons) {
            const existing = existPersonMap.get(element);
            const memberParam = { ...updateParam, MemberId: element };

            if (existing) {
                // Update existing person
                await InspectionPerson.update(memberParam, { where: { id: existing.id } });

                // If previously marked deleted, consider it as added again
                if (existing.isDeleted !== false) {
                    addedPerson.push(existing);
                }
            } else {
                // Create new person
                const newPersonData = await InspectionPerson.createInstance(memberParam);
                addedPerson.push(newPersonData);
            }
        }

        await this.updatePersonHistory(addedPerson, deletedPerson, history, loginUser);
    },

    async updateDefineAssociations(payload, condition, updateParam, history, loginUser) {
        const existDefine = await DeliverDefine.findAll({ where: condition });
        const deletedDefine = existDefine.filter(e =>
            payload.define.indexOf(e.DeliverDefineWorkId) === -1 && e.isDeleted === false
        );

        await DeliverDefine.update({ isDeleted: true }, { where: condition });

        const addedDefineData = [];
        for (const element of payload.define) {
            const index = existDefine.findIndex(item => item.DeliverDefineWorkId === element);
            const defineParam = { ...updateParam, DeliverDefineWorkId: element };

            if (index !== -1) {
                await DeliverDefine.update(defineParam, { where: { id: existDefine[index].id } });
                if (existDefine[index].isDeleted !== false) {
                    addedDefineData.push(existDefine[index]);
                }
            } else {
                const newDefineData = await DeliverDefine.createInstance(defineParam);
                addedDefineData.push(newDefineData);
            }
        }

        await this.updateDefineHistory(addedDefineData, deletedDefine, history, loginUser);
    },


    async updateGateAssociations(payload, condition, updateParam, history, loginUser) {
        const gates = [payload.GateId];
        const existGate = await InspectionGate.findAll({ where: condition });
        const deletedGate = existGate.filter(e =>
            gates.indexOf(e.GateId) === -1 && e.isDeleted === false
        );

        await InspectionGate.update({ isDeleted: true }, { where: condition });

        const addedGate = [];
        for (const element of gates) {
            const index = existGate.findIndex(item => item.GateId === element);
            const gateParam = { ...updateParam, GateId: element };

            if (index !== -1) {
                await InspectionGate.update(gateParam, { where: { id: existGate[index].id } });
                if (existGate[index].isDeleted !== false) {
                    addedGate.push(existGate[index]);
                }
            } else {
                const newGateData = await InspectionGate.createInstance(gateParam);
                addedGate.push(newGateData);
            }
        }

        await this.updateGateHistory(addedGate, deletedGate, history, loginUser);
    },


    async updateEquipmentAssociations(payload, condition, updateParam, history, loginUser) {
        const equipments = payload.EquipmentId;

        await InspectionRequest.update(
            { escort: payload.escort },
            { where: { id: payload.InspectionRequestIds[0] } }
        );

        const existEquipment = await InspectionEquipment.findAll({ where: condition });
        const deletedEquipment = existEquipment.filter(e =>
            equipments.indexOf(e.EquipmentId) === -1 && e.isDeleted === false
        );

        await InspectionEquipment.update({ isDeleted: true }, { where: condition });

        const addedEquipment = [];
        for (const element of equipments) {
            const index = existEquipment.findIndex(item => item.EquipmentId === element);
            const equipmentParam = { ...updateParam, EquipmentId: element };

            if (index !== -1) {
                await InspectionEquipment.update(equipmentParam, { where: { id: existEquipment[index].id } });
                if (existEquipment[index].isDeleted !== false) {
                    addedEquipment.push(existEquipment[index]);
                }
            } else {
                const newEquipmentData = await InspectionEquipment.createInstance(equipmentParam);
                addedEquipment.push(newEquipmentData);
            }
        }

        await this.updateEquipmentHistory(addedEquipment, deletedEquipment, history, loginUser);
    },


    async updateInspectionTimes(payload, inspectionRequestId) {
        if (payload.inspectionStart && payload.inspectionEnd) {
            const startDate = new Date(payload.inspectionStart).getTime();
            const currentDate = new Date().getTime();
            const endDate = new Date(payload.inspectionEnd).getTime();

            if (startDate > currentDate && endDate > currentDate) {
                const inspectionParam = {
                    inspectionStart: payload.inspectionStart,
                    inspectionEnd: payload.inspectionEnd,
                };
                await InspectionRequest.update(inspectionParam, { where: { id: inspectionRequestId } });
            }
        }
    },

    async handleVoidRequest(payload, inspectionRequestId, memberData, loginUser, inspectionRequestDetail) {
        if (payload.void === true) {
            const existVoid = await VoidList.findOne({
                where: Sequelize.and({ InspectionRequestId: inspectionRequestId }),
            });

            if (!existVoid) {
                const voidcreate = {
                    InspectionRequestId: inspectionRequestId,
                    ProjectId: payload.ProjectId,
                    ParentCompanyId: payload.ParentCompanyId,
                };
                const newVoidData = await VoidList.createInstance(voidcreate);

                if (newVoidData) {
                    const object = {
                        ProjectId: payload.ProjectId,
                        MemberId: memberData.id,
                        InspectionRequestId: inspectionRequestId,
                        isDeleted: false,
                        type: 'void',
                        description: `${loginUser.firstName} ${loginUser.lastName} Voided the Inspection Booking, ${inspectionRequestDetail.description}`,
                    };
                    await InspectionHistory.createInstance(object);
                }
            } else {
                return { success: false, message: 'Inspection Booking already is in void list.' };
            }
        }
    },

    async handleStatusUpdate(payload, inspectionRequestId, memberData) {
        if (payload.status && (memberData.RoleId === 2 || memberData.RoleId === 1)) {
            const inspectionParam2 = {
                status: payload.status,
                approvedBy: memberData.id,
                approved_at: new Date(),
            };
            await InspectionRequest.update(inspectionParam2, { where: { id: inspectionRequestId } });
        }
    },

    async handleStatusChanges(updatedInspectionRequest, idDetails, memberData, projectSettingDetails, payload, loginUser, inspectionRequestDetail) {
        const editedNDR = await InspectionRequest.getNDRData({ id: idDetails.id });
        const existsInspectionRequest = await InspectionRequest.getSingleInspectionRequestData({ id: +idDetails.id });

        const { fieldsChanged, inspectionDateTimeChanged } = await this.detectChanges(editedNDR, existsInspectionRequest);

        if (!payload.status) {
            await this.updateStatusBasedOnChanges({
                currentStatus: existsInspectionRequest.status,
                fieldsChanged,
                inspectionDateTimeChanged,
                memberData,
                projectSettingDetails,
                editedNDRId: editedNDR.id,
                loginUser,
                inspectionRequestDetail
            });
        }
    },

    async detectChanges(editedNDR, existsInspectionRequest) {
        let tagsUpdated = false;
        let fieldsChanged = false;

        // Check for changes in various associations
        const associationChanges = await this.checkAssociationChanges(editedNDR, existsInspectionRequest);
        tagsUpdated = associationChanges.tagsUpdated;

        // Check for changes in main fields
        fieldsChanged = this.checkMainFieldChanges(existsInspectionRequest, editedNDR, tagsUpdated);

        const inspectionDateTimeChanged = this.checkDateTimeChanges(existsInspectionRequest, editedNDR);

        return { fieldsChanged, inspectionDateTimeChanged };
    },

    async checkAssociationChanges(editedNDR, existsInspectionRequest) {
        const hasChanges = (edited, existing, keySelector) => {
            if (!Array.isArray(edited) || !Array.isArray(existing)) return false;
            if (edited.length === 0 || existing.length === 0) return false;

            const added = edited.filter(el =>
                !existing.find(ex => keySelector(ex) === keySelector(el))
            );
            const deleted = existing.filter(el =>
                !edited.find(ed => keySelector(ed) === keySelector(el))
            );
            return added.length > 0 || deleted.length > 0;
        };

        const tagsUpdated =
            hasChanges(editedNDR.defineWorkDetails, existsInspectionRequest.defineWorkDetails, el => el.id) ||
            hasChanges(editedNDR.gateDetails, existsInspectionRequest.gateDetails, el => el.Gate.id) ||
            hasChanges(editedNDR.equipmentDetails, existsInspectionRequest.equipmentDetails, el => el.Equipment.id) ||
            hasChanges(editedNDR.companyDetails, existsInspectionRequest.companyDetails, el => el.Company.id) ||
            hasChanges(editedNDR.memberDetails, existsInspectionRequest.memberDetails, el => el.Member.id);

        return { tagsUpdated };
    },


    checkMainFieldChanges(existsInspectionRequest, editedNDR, tagsUpdated) {
        return (
            existsInspectionRequest.description !== editedNDR.description ||
            existsInspectionRequest.CraneRequestId !== editedNDR.CraneRequestId ||
            existsInspectionRequest.LocationId !== editedNDR.LocationId ||
            existsInspectionRequest.requestType !== editedNDR.requestType ||
            existsInspectionRequest.vehicleDetails !== editedNDR.vehicleDetails ||
            existsInspectionRequest.notes !== editedNDR.notes ||
            existsInspectionRequest.isAssociatedWithCraneRequest !== editedNDR.isAssociatedWithCraneRequest ||
            existsInspectionRequest.escort !== editedNDR.escort ||
            existsInspectionRequest.craneDropOffLocation !== editedNDR.craneDropOffLocation ||
            existsInspectionRequest.cranePickUpLocation !== editedNDR.cranePickUpLocation ||
            tagsUpdated ||
            existsInspectionRequest.recurrence !== editedNDR.recurrence ||
            existsInspectionRequest.chosenDateOfMonth !== editedNDR.chosenDateOfMonth ||
            existsInspectionRequest.dateOfMonth !== editedNDR.dateOfMonth ||
            existsInspectionRequest.monthlyRepeatType !== editedNDR.monthlyRepeatType ||
            existsInspectionRequest.days !== editedNDR.days ||
            existsInspectionRequest.repeatEveryType !== editedNDR.repeatEveryType ||
            existsInspectionRequest.repeatEveryCount !== editedNDR.repeatEveryCount
        );
    },

    checkDateTimeChanges(existsInspectionRequest, editedNDR) {
        return (
            +new Date(existsInspectionRequest.inspectionStart) !== +new Date(editedNDR.inspectionStart) ||
            +new Date(existsInspectionRequest.inspectionEnd) !== +new Date(editedNDR.inspectionEnd)
        );
    },

    async updateStatusBasedOnChanges(params) {
        const { currentStatus, fieldsChanged, inspectionDateTimeChanged, memberData, projectSettingDetails, editedNDRId, loginUser, inspectionRequestDetail } = params;
        const hasChanges = fieldsChanged || inspectionDateTimeChanged;
        const canAutoApprove = memberData.RoleId === 2 ||
            memberData.isAutoApproveEnabled ||
            projectSettingDetails.ProjectSettings.isAutoApprovalEnabled;

        if (currentStatus === 'Delivered' || currentStatus === 'Approved' ||
            currentStatus === 'Expired' || currentStatus === 'Declined' ||
            currentStatus === 'Pending') {

            if (hasChanges) {
                if (canAutoApprove) {
                    await InspectionRequest.update(
                        { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
                        { where: { id: editedNDRId } }
                    );

                    if (currentStatus === 'Expired' || currentStatus === 'Declined') {
                        const object = {
                            ProjectId: projectSettingDetails.ProjectId,
                            MemberId: memberData.id,
                            InspectionRequestId: editedNDRId,
                            isDeleted: false,
                            type: 'approved',
                            description: `${loginUser.firstName} ${loginUser.lastName} Approved the Inspection Booking, ${inspectionRequestDetail.description}`,
                        };
                        await InspectionHistory.createInstance(object);
                    }
                } else {
                    await InspectionRequest.update(
                        { status: 'Pending' },
                        { where: { id: editedNDRId } }
                    );
                }
            }
        }
    },

    async handleNotificationsData(payload, inspectionRequestId, inspectionRequestDetail, existsSingleInspectionRequest, loginUser, mainIndex) {
        const memberData = await Member.getBy({
            UserId: loginUser.id,
            ProjectId: +payload.ProjectId,
        });

        const history = await this.createNotificationHistory(payload, inspectionRequestId, inspectionRequestDetail, existsSingleInspectionRequest, loginUser, memberData);
        const notification = await this.createNotification(history, payload, loginUser, existsSingleInspectionRequest);

        await this.sendNotifications({ payload, inspectionRequestId, inspectionRequestDetail, existsSingleInspectionRequest, loginUser, memberData, history, notification });

        if (mainIndex === payload.InspectionRequestIds.length) {
            return { success: true, data: history };
        }

        return { success: true, data: history };
    },

    async createNotificationHistory(payload, inspectionRequestId, inspectionRequestDetail, existsSingleInspectionRequest, loginUser, memberData) {
        const locationChosen = await Locations.findOne({
            where: {
                ProjectId: +payload.ProjectId,
                id: existsSingleInspectionRequest.LocationId,
            },
        });

        const projectDetails = await Project.findByPk(+payload.ProjectId);

        return {
            InspectionRequestId: inspectionRequestId,
            InspectionId: inspectionRequestDetail.InspectionId,
            MemberId: memberData.id,
            type: 'edit',
            description: `${loginUser.firstName} ${loginUser.lastName} Updated the Inspection Booking, ${inspectionRequestDetail.description}`,
            locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Updated the Inspection Booking, ${inspectionRequestDetail.description}. Location: ${locationChosen.locationPath}.`,
            firstName: loginUser.firstName,
            profilePic: loginUser.profilePic,
            createdAt: new Date(),
            ProjectId: payload.ProjectId,
            projectName: projectDetails.projectName,
        };
    },

    async createNotification(history, payload, loginUser, existsSingleInspectionRequest) {
        const notification = {
            ...history,
            ProjectId: history.ProjectId,
            title: `Inspection Booking Updated by ${loginUser.firstName} ${loginUser.lastName}`,
            requestType: 'InspectionRequest',
        };

        if (existsSingleInspectionRequest?.recurrence?.recurrence) {
            notification.recurrenceType = `${existsSingleInspectionRequest.recurrence.recurrence} From ${moment(existsSingleInspectionRequest.recurrence.recurrenceStartDate).format('MM/DD/YYYY')} to ${moment(existsSingleInspectionRequest.recurrence.recurrenceEndDate).format('MM/DD/YYYY')}`;
        }

        return await Notification.createInstance(notification);
    },

    async sendNotifications(params) {
        const { payload, inspectionRequestId, inspectionRequestDetail, existsSingleInspectionRequest, loginUser, memberData, history, notification } = params;
        const memberLocationPreference = await this.getMemberLocationPreferences(payload, existsSingleInspectionRequest, memberData);
        const adminData = await this.getAdminData(payload, memberLocationPreference, history, memberData);

        await this.sendLocationNotifications(memberLocationPreference, inspectionRequestDetail, history, payload, notification);
        await this.sendAdminNotifications(adminData, payload, notification, memberData, loginUser, inspectionRequestDetail);

        const updatedInspectionRequest = await InspectionRequest.getSingleInspectionRequestData({ id: +inspectionRequestId });
        await this.updateEditInspectionRequestHistory(updatedInspectionRequest, existsSingleInspectionRequest, updatedInspectionRequest, history, loginUser);
        await pushNotification.sendDeviceTokenForInspection(history, 5, payload.ProjectId);

        if (memberData.RoleId === 4 || memberData.RoleId === 3) {
            await this.sendRoleSpecificNotifications(payload, inspectionRequestDetail, existsSingleInspectionRequest, loginUser, memberData, history);
        }
    },

    async getMemberLocationPreferences(payload, existsSingleInspectionRequest, memberData) {
        return await LocationNotificationPreferences.findAll({
            where: {
                ProjectId: payload.ProjectId,
                LocationId: +existsSingleInspectionRequest.LocationId,
                follow: true,
            },
            include: [{
                association: 'Member',
                attributes: ['id', 'RoleId'],
                where: { [Op.and]: [{ id: { [Op.ne]: memberData.id } }] },
                include: [{
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName', 'email'],
                }],
            }],
        });
    },

    async getAdminData(payload, memberLocationPreference, history, memberData) {
        const locationFollowMembers = memberLocationPreference.map(element => element.Member.id);
        const persons = history.persons || [];

        return await Member.findAll({
            where: {
                [Op.and]: [
                    { ProjectId: payload.ProjectId },
                    { isDeleted: false },
                    { id: { [Op.in]: persons } },
                    { id: { [Op.ne]: history.MemberId } },
                    { id: { [Op.notIn]: locationFollowMembers } },
                ],
            },
            include: [{
                association: 'User',
                attributes: ['id', 'firstName', 'lastName', 'email'],
            }],
            attributes: ['id', 'RoleId'],
        });
    },

    async sendLocationNotifications(memberLocationPreference, inspectionRequestDetail, history, payload, notification) {
        if (memberLocationPreference && memberLocationPreference.length > 0) {
            await pushNotification.sendMemberLocationPreferencePushNotification(
                memberLocationPreference,
                inspectionRequestDetail.InspectionRequestId,
                history.locationFollowDescription,
                'InspectionRequest',
                payload.ProjectId,
                inspectionRequestDetail.id,
                5,
            );

            await notificationHelper.createMemberDeliveryLocationInAppNotification(
                DeliveryPersonNotification,
                payload.ProjectId,
                notification.id,
                memberLocationPreference,
                5,
            );
        }
    },

    async sendAdminNotifications(adminData, payload, notification, memberData, loginUser, inspectionRequestDetail) {
        const projectDetails = await Project.findByPk(+payload.ProjectId);

        await notificationHelper.createDeliveryPersonNotification(
            adminData,
            [],
            projectDetails,
            notification,
            DeliveryPersonNotification,
            memberData,
            loginUser,
            5,
            'updated a',
            'Inspection Request',
            `Inspection Booking (${inspectionRequestDetail.InspectionId} - ${inspectionRequestDetail.description})`,
            inspectionRequestDetail.id,
        );
    },

    async sendRoleSpecificNotifications(payload, inspectionRequestDetail, existsSingleInspectionRequest, loginUser, memberData, history) {
        const userEmails = await this.getMemberDetailData(history, []);

        if (userEmails.length > 0) {
            await this.sendEmailNotificationsToAdmins(userEmails, payload, loginUser, memberData, inspectionRequestDetail);
            await this.sendGuestUserNotificationsData(payload, inspectionRequestDetail, loginUser);
        }
    },

    async sendEmailNotificationsToAdmins(userEmails, payload, loginUser, memberData, inspectionRequestDetail) {
        for (const element of userEmails) {
            if (element.RoleId === 2) {
                await this.sendAdminEmailNotification(element, payload, loginUser, memberData, inspectionRequestDetail);
            }
        }
    },

    async sendAdminEmailNotification(element, payload, loginUser, memberData, inspectionRequestDetail) {
        const name = element.firstName ? `${element.firstName} ${element.lastName}` : 'user';
        const memberRole = await Role.findOne({
            where: { id: memberData.RoleId, isDeleted: false },
        });

        const mailPayload = {
            name,
            email: element.email,
            content: `We would like to inform you that ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has updated a Inspection booking ${inspectionRequestDetail.InspectionId} and waiting for your approval.Kindly review the booking and update the status.`,
        };

        const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
            where: {
                MemberId: +element.MemberId,
                ProjectId: +payload.ProjectId,
                LocationId: +payload.LocationId,
                isDeleted: false,
            },
        });

        if (isMemberFollowLocation) {
            await this.sendNotificationEmail(element, payload, mailPayload, memberRole, loginUser);
        }
    },

    async sendNotificationEmail(element, payload, mailPayload, memberRole, loginUser) {
        const memberNotification = await NotificationPreference.findOne({
            where: {
                MemberId: +element.MemberId,
                ProjectId: +payload.ProjectId,
                isDeleted: false,
            },
            include: [{
                association: 'NotificationPreferenceItem',
                where: { id: 9, isDeleted: false },
            }],
        });

        if (memberNotification?.instant) {
            await MAILER.sendMail(
                mailPayload,
                'notifyPAForReApproval',
                `Inspection Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                `Inspection Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                async (info, err) => {
                    console.log(info, err);
                },
            );
        }

        if (memberNotification?.dailyDigest) {
            await this.createDailyDigestDataApproval({
                RoleId: +element.RoleId,
                MemberId: +element.MemberId,
                ProjectId: +payload.ProjectId,
                ParentCompanyId: +payload.ParentCompanyId,
                loginUser,
                dailyDigestMessage: 'updated a',
                requestType: 'Inspection Request',
                messages: `Inspection Booking (${element.InspectionId} - ${element.description})`,
                messages2: 'and waiting for your approval',
                requestId: element.id,
            });
        }
    },

    async sendGuestUserNotificationsData(payload, inspectionRequestDetail, loginUser) {
        const exist2 = await InspectionRequest.findOne({
            include: [{
                association: 'memberDetails',
                required: false,
                where: { isDeleted: false, isActive: true },
                attributes: ['id'],
                include: [{
                    association: 'Member',
                    attributes: ['id', 'isGuestUser'],
                    include: [{
                        association: 'User',
                        attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                    }],
                }],
            }],
            where: { isDeleted: false, id: +inspectionRequestDetail.id },
        });

        if (exist2?.memberDetails) {
            for (const userMail of exist2.memberDetails) {
                if (userMail.Member.isGuestUser) {
                    const guestMailPayload = {
                        email: userMail.Member.User.email,
                        guestName: userMail.Member.User.firstName,
                        content: `We would like to inform you that ${loginUser.firstName} ${loginUser.lastName} has updated a Inspection booking ${exist2.description}.`,
                    };

                    await MAILER.sendMail(
                        guestMailPayload,
                        'notifyGuestOnEdit',
                        `Inspection Booking updated by ${loginUser.firstName} ${loginUser.lastName}`,
                        `Inspection Booking updated by ${loginUser.firstName} ${loginUser.lastName}`,
                        async (info, err) => {
                            console.log(info, err);
                        },
                    );
                }
            }
        }
    },

    async updateEditInspectionRequestHistory(
        userEditedInspectionRequestData,
        existsInspectionRequestData,
        updatedInspectionRequest,
        history,
        loginUser
    ) {
        await this.logFieldChanges(userEditedInspectionRequestData, existsInspectionRequestData, updatedInspectionRequest, history, loginUser);
        await this.logMemberCompanyDfowChanges(updatedInspectionRequest, existsInspectionRequestData, history, loginUser);
        await this.logEquipmentGateChanges(updatedInspectionRequest, existsInspectionRequestData, history, loginUser);
    },

    async logFieldChanges(userEdited, exists, updated, history, user) {
        const historyObject = history;

        await this.logDescriptionAndDateChanges(userEdited, exists, historyObject, user);
        await this.logOtherFieldChanges(userEdited, exists, updated, historyObject, user);
    },

    async logDescriptionAndDateChanges(userEdited, exists, historyObject, user) {
        if (userEdited.description?.toLowerCase() !== exists.description?.toLowerCase()) {
            historyObject.description = `${user.firstName} ${user.lastName} Updated the Description ${userEdited.description}`;
            InspectionHistory.createInstance(historyObject);
        }

        if (new Date(userEdited.inspectionStart).getTime() !== new Date(exists.inspectionStart).getTime()) {
            historyObject.description = `${user.firstName} ${user.lastName} Updated the Inspection Start Date ${userEdited.inspectionStart}`;
            InspectionHistory.createInstance(historyObject);
        }

        if (new Date(userEdited.inspectionEnd).getTime() !== new Date(exists.inspectionEnd).getTime()) {
            historyObject.description = `${user.firstName} ${user.lastName} Updated the Inspection End Date ${userEdited.inspectionEnd}`;
            InspectionHistory.createInstance(historyObject);
        }
    },

    async logOtherFieldChanges(userEdited, exists, updated, historyObject, user) {
        const createHistory = (condition, message) => {
            if (condition) {
                historyObject.description = message;
                InspectionHistory.createInstance(historyObject);
            }
        };

        const userName = `${user.firstName} ${user.lastName}`;

        createHistory(
            userEdited.LocationId !== exists.LocationId && updated?.location?.locationPath,
            `${userName} Updated the Location, ${updated.location.locationPath}`
        );

        createHistory(
            userEdited.notes?.toLowerCase() !== exists.notes?.toLowerCase(),
            `${userName} ${userEdited.notes ? 'Updated' : 'Removed'} the Notes ${exists.notes || ''}`
        );

        createHistory(
            userEdited.cranePickUpLocation !== exists.cranePickUpLocation,
            `${userName} ${userEdited.cranePickUpLocation ? 'Updated the Picking From' : 'Removed the Picking From'} ${userEdited.cranePickUpLocation || exists.cranePickUpLocation}`
        );

        createHistory(
            userEdited.craneDropOffLocation !== exists.craneDropOffLocation,
            `${userName} ${userEdited.craneDropOffLocation ? 'Updated the Picking To' : 'Removed the Picking To'} ${userEdited.craneDropOffLocation || exists.craneDropOffLocation}`
        );

        createHistory(
            userEdited.vehicleDetails !== exists.vehicleDetails,
            `${userName} ${userEdited.vehicleDetails ? 'Updated the Vehicle Details' : 'Removed the Vehicle Details'} ${exists.vehicleDetails || ''}`
        );

        createHistory(
            userEdited.escort !== exists.escort,
            `${userName} ${userEdited.escort ? 'enabled' : 'disabled'} the Escort`
        );
    },



    async logMemberCompanyDfowChanges(updated, exists, history, user) {
        const historyObject = history;

        // Member details
        await this.compareAndLogDifferences(
            updated.memberDetails,
            exists.memberDetails,
            (el) => `${user.firstName} ${user.lastName} added the member ${el.Member.User.firstName} ${el.Member.User.lastName}`,
            (el) => `${user.firstName} ${user.lastName} updated the member ${el.Member.User.firstName || ''} ${el.Member.User.lastName || ''}`,
            historyObject
        );

        // Company details
        await this.compareAndLogDifferences(
            updated.companyDetails,
            exists.companyDetails,
            (el) => `${user.firstName} ${user.lastName} added the company ${el.Company.companyName}`,
            (el) => `${user.firstName} ${user.lastName} updated the company ${el.Company.companyName}`,
            historyObject
        );

        // DFOW details
        await this.compareAndLogDifferences(
            updated.defineWorkDetails,
            exists.defineWorkDetails,
            (el) => `${user.firstName} ${user.lastName} added the Definable feature of work ${el.DeliverDefineWork.DFOW}`,
            (el) => `${user.firstName} ${user.lastName} updated the Definable feature of work ${el.DeliverDefineWork.DFOW}`,
            historyObject
        );
    },

    async logEquipmentGateChanges(updated, exists, history, user) {
        const historyObject = history;

        // Equipment details
        await this.compareAndLogDifferences(
            updated.equipmentDetails,
            exists.equipmentDetails,
            (el) => `${user.firstName} ${user.lastName} added the Equipment ${el.Equipment.equipmentName}`,
            (el) => `${user.firstName} ${user.lastName} updated the Equipment ${el.Equipment.equipmentName}`,
            historyObject,
            (a, b) => a.Equipment.id === b.Equipment.id
        );

        // Gate details
        await this.compareAndLogDifferences(
            updated.gateDetails,
            exists.gateDetails,
            (el) => `${user.firstName} ${user.lastName} added the Gate ${el.Gate.gateName}`,
            (el) => `${user.firstName} ${user.lastName} updated the Gate ${el.Gate.gateName}`,
            historyObject,
            (a, b) => a.Gate.id === b.Gate.id
        );
    },

    async compareAndLogDifferences(newList, oldList, addMessageFn, removeMessageFn, historyObject, comparator = (a, b) => a.id === b.id) {
        const added = newList.filter(el => !oldList.find(existing => comparator(el, existing)));
        const removed = oldList.filter(el => !newList.find(existing => comparator(el, existing)));

        for (const el of added) {
            historyObject.description = addMessageFn(el);
            await InspectionHistory.createInstance(historyObject);
        }
        for (const el of removed) {
            historyObject.description = removeMessageFn(el);
            await InspectionHistory.createInstance(historyObject);
        }
    },

    async getMemberDetailData(data, memberLocationPreference) {
        const emailArray = [];
        const existAdminData = [];
        if (data.memberData !== undefined) {
            data.memberData.forEach((element) => {
                const index = existAdminData.findIndex(
                    (adminNew) => adminNew.email === element.Member.User.email,
                );
                if (index === -1) {
                    existAdminData.push({ email: element.Member.User.email });
                    emailArray.push({
                        email: element.Member.User.email,
                        firstName: element.Member.User.firstName,
                        lastName: element.Member.User.lastName,
                        UserId: element.Member.User.id,
                        MemberId: element.Member.id,
                        RoleId: element.Member.RoleId,
                    });
                }
            });
        }
        if (data.adminData !== undefined) {
            data.adminData.forEach((element) => {
                const index = existAdminData.findIndex((adminNew) => adminNew.email === element.User.email);
                if (index === -1) {
                    existAdminData.push({ email: element.User.email });
                    emailArray.push({
                        email: element.User.email,
                        firstName: element.User.firstName,
                        lastName: element.User.lastName,
                        UserId: element.User.id,
                        MemberId: element.id,
                        RoleId: element.RoleId,
                    });
                }
            });
        }
        if (memberLocationPreference !== undefined && memberLocationPreference.length > 0) {
            memberLocationPreference.forEach((element) => {
                const index = existAdminData.findIndex(
                    (adminNew) => adminNew.email === element.Member.User.email,
                );
                if (index === -1) {
                    existAdminData.push({ email: element.Member.User.email });
                    emailArray.push({
                        email: element.Member.User.email,
                        firstName: element.Member.User.firstName,
                        lastName: element.Member.User.lastName,
                        UserId: element.Member.User.id,
                        MemberId: element.Member.id,
                        RoleId: element.Member.RoleId,
                    });
                }
            });
        }
        return emailArray;
    },
    // prettier-ignore
    async createDailyDigestData(
        params
    ) {
        const {
            MemberId,
            ProjectId,
            ParentCompanyId,
            loginUser,
            dailyDigestMessage,
            requestType,
            messages,
            requestId, } = params;
        const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
        const encryptedRequestId = cryptr.encrypt(requestId);
        const encryptedMemberId = cryptr.encrypt(MemberId);
        let imageUrl;
        let link;
        let height;
        if (requestType === 'Inspection Request') {
            imageUrl = 'https://d36hblf01wyurt.cloudfront.net/87758081-8ccd-4e4d-a4eb-6257466876da.png';
            link = 'inspection-request';
            height = 'height:18px;';
        }
        if (requestType === 'Crane Request') {
            imageUrl = 'https://d36hblf01wyurt.cloudfront.net/ab400721-520c-4f6f-941c-ac07acc28809.png';
            link = 'crane-request';
            height = 'height:32px;';
        }
        if (requestType === 'Concrete Request') {
            imageUrl = 'https://d36hblf01wyurt.cloudfront.net/c43e4c58-4c4d-44ff-b78c-fe9948ea53eb.png';
            link = 'concrete-request';
            height = 'height:18px;';
        }
        const object = {
            description: `<div>
    <ul style="list-style-type:none;padding:0px;border-bottom:1px dashed #E3E3E3;">
        <li style="display:flex;">
            <img src="${imageUrl}" alt="message-icon" style="${height}">
                <p style="margin:0px;font-size:12px;padding-left:10px;">
                    <a href="#" ta
        rget="" style="text-decoration: none;color:#4470FF;">
            ${loginUser.firstName}  ${loginUser.lastName}
                    </a>
                    ${dailyDigestMessage}
      <a href = "${process.env.BASE_URL}/${link}?requestId=${encryptedRequestId}&memberId=${encryptedMemberId} " style="text - decoration: none; color:#4470FF; " >${messages}</a>
  <span style="color:#707070;">on ${moment().utc().format('MMMM DD')} at ${moment()
                    .utc()
                    .format('hh:mm A zz')}</span>
                </p>
        </li>
    </ul>
</div> `,
            MemberId,
            ProjectId,
            isSent: false,
            isDeleted: false,
            ParentCompanyId,
        };
        await DigestNotification.create(object);
    },
    async sendEmailNotificationToUser(
        history,
        memberDetails,
        loginUser,
        newInspectionData,
        inspectionData,
        memberLocationPreference,
    ) {
        const userEmails = await this.getMemberDetailData(history, memberLocationPreference);
        if (userEmails.length > 0) {
            userEmails.forEach(async (element) => {
                let name;
                if (!element.firstName) {
                    name = 'user';
                } else {
                    name = `${element.firstName} ${element.lastName} `;
                }
                if (+element.MemberId !== +memberDetails.id) {
                    const memberRole = await Role.findOne({
                        where: {
                            id: memberDetails.RoleId,
                            isDeleted: false,
                        },
                    });
                    const time = moment(newInspectionData.inspectionStart).format('MM-DD-YYYY');
                    const mailPayload = {
                        userName: name,
                        email: element.email,
                        InspectionId: newInspectionData.InspectionId,
                        description: newInspectionData.description,
                        timestamp: time,
                        createdTimestamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
                        content: ` ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has created the Inspection booking ${newInspectionData.InspectionId}.Please see below for more details`,
                    };
                    const memberNotification = await NotificationPreference.findOne({
                        where: {
                            MemberId: +element.MemberId,
                            ProjectId: +inspectionData.ProjectId,
                            isDeleted: false,
                        },
                        include: [
                            {
                                association: 'NotificationPreferenceItem',
                                where: {
                                    id: 12,
                                    isDeleted: false,
                                },
                            },
                        ],
                    });
                    if (memberNotification?.instant) {
                        await MAILER.sendMail(
                            mailPayload,
                            'InspectionRequestCreated',
                            `Inspection Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} `,
                            `Inspection Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} `,
                            async (info, err) => {
                                console.log(info, err);
                            },
                        );
                    }
                    if (memberNotification?.dailyDigest) {
                        await this.createDailyDigestData({
                            RoleId: +memberDetails.RoleId,
                            MemberId: +element.MemberId,
                            ProjectId: +inspectionData.ProjectId,
                            ParentCompanyId: +inspectionData.ParentCompanyId,
                            loginUser,
                            dailyDigestMessage: 'created a',
                            requestType: 'Inspection Request',
                            messages: `Inspection Booking(${newInspectionData.InspectionId} - ${newInspectionData.description})`,
                            requestId: newInspectionData.id,
                        });
                    }
                }
            });
        }
        return true;
    },
    async createDailyDigestDataApproval(
        params
    ) {
        const {
            MemberId,
            ProjectId,
            ParentCompanyId,
            loginUser,
            dailyDigestMessage,
            requestType,
            messages,
            messages2,
            requestId, } = params;
        const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
        const encryptedRequestId = cryptr.encrypt(requestId);
        const encryptedMemberId = cryptr.encrypt(MemberId);
        let imageUrl;
        let link;
        let height;
        if (requestType === 'inspection Request') {
            imageUrl = 'https://d36hblf01wyurt.cloudfront.net/87758081-8ccd-4e4d-a4eb-6257466876da.png';
            link = 'inspection-request';
            height = 'height:18px;';
        }
        if (requestType === 'Crane Request') {
            imageUrl = 'https://d36hblf01wyurt.cloudfront.net/ab400721-520c-4f6f-941c-ac07acc28809.png';
            link = 'crane-request';
            height = 'height:32px;';
        }
        if (requestType === 'Concrete Request') {
            imageUrl = 'https://d36hblf01wyurt.cloudfront.net/c43e4c58-4c4d-44ff-b78c-fe9948ea53eb.png';
            link = 'concrete-request';
            height = 'height:18px;';
        }
        const object = {
            description: `<div>
  <ul style="list-style-type:none;padding:0px;border-bottom:1px dashed #E3E3E3;">
    <li style="display:flex;">
      <img src="${imageUrl}" alt="message-icon" style="${height}">
        <p style="margin:0px;font-size:12px;padding-left:10px;">
          <a href="
        #" target="" style="text-decoration: none;color:#4470FF;">
          ${loginUser.firstName}  ${loginUser.lastName}
          </a>
          ${dailyDigestMessage}
      <a href = "${process.env.BASE_URL
                }/ ${link}?requestId = ${encryptedRequestId}& memberId=${encryptedMemberId} " style="text - decoration: none; color:#4470FF; " >
          ${messages}
        </a>
  ${messages2}
<span style="color:#707070;">on ${moment().utc().format('MMMM DD')} at ${moment()
                    .utc()
                    .format('hh:mm A zz')}</span>
        </p>
    </li>
  </ul>
</div> `,
            MemberId,
            ProjectId,
            isSent: false,
            isDeleted: false,
            ParentCompanyId,
        };
        await DigestNotification.create(object);
    },
    async convertTimezoneToUtc(date, timezone, time) {
        const chosenTimezoneinspectionStart = moment.tz(`${date} ${time}`, 'MM/DD/YYYY HH:mm', timezone);
        const utcDate = chosenTimezoneinspectionStart.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
        return utcDate;
    },

    isWithinInclusiveRange(date, start, end) {
        return moment(date).isBetween(moment(start), moment(end), null, '[]') ||
            moment(date).isSame(start) ||
            moment(date).isSame(end);
    },
    maybeApplyApproval(param, context) {
        const { memberDetails, roleDetails, accountRoleDetails, projectSettings } = context;
        if (
            memberDetails.RoleId === roleDetails.id ||
            memberDetails.RoleId === accountRoleDetails.id ||
            memberDetails.isAutoApproveEnabled ||
            projectSettings.isAutoApprovalEnabled
        ) {
            param.status = 'Approved';
            param.approvedBy = memberDetails.id;
            param.approved_at = new Date();
        }
        return param;
    },

    async buildBaseInspectionParam(dateStr, ids, payload, context) {
        const { memberDetails } = context;
        const startUtc = await this.convertTimezoneToUtc(
            dateStr,
            payload.timezone,
            payload.inspectionStartTime,
        );
        const endUtc = await this.convertTimezoneToUtc(
            dateStr,
            payload.timezone,
            payload.inspectionEndTime,
        );

        const param = {
            description: payload.description,
            escort: payload.escort,
            vehicleDetails: payload.vehicleDetails,
            notes: payload.notes,
            InspectionId: ids.nextInspectionId(),
            inspectionStart: startUtc,
            inspectionEnd: endUtc,
            ProjectId: payload.ProjectId,
            createdBy: memberDetails.id,
            isAssociatedWithCraneRequest: payload.isAssociatedWithCraneRequest,
            requestType: payload.requestType,
            cranePickUpLocation: payload.cranePickUpLocation,
            craneDropOffLocation: payload.craneDropOffLocation,
            recurrenceId: context.newRecurrenceId,
            inspectionType: payload.inspectionType,
            LocationId: payload.LocationId,
            OriginationAddress: payload.originationAddress,
            vehicleType: payload.vehicleType
        };

        if (payload.requestType === 'InspectionRequestWithCrane') {
            param.CraneRequestId = ids.nextCraneRequestId();
        }

        return this.maybeApplyApproval(param, context);
    },

    async generateDailyEvents(dates, recurrence, ids, payload, context, rangeStart, rangeEnd) {
        const result = [];
        for (let i = 0; i < dates.length; i += +recurrence.repeatEveryCount) {
            const d = dates[i];
            if (this.isWithinInclusiveRange(d, rangeStart, rangeEnd)) {
                const dateStr = moment(d).format('MM/DD/YYYY');
                result.push(await this.buildBaseInspectionParam(dateStr, ids, payload, context));
            }
        }
        return result;
    },

    async generateWeeklyEvents(dates, recurrence, ids, payload, context, rangeStart, rangeEnd) {
        const selectedDays = recurrence.days || []; // array of weekday names (e.g. "Monday")
        const start = moment(rangeStart).startOf('week');
        const end = moment(rangeEnd).endOf('week');
        const totalDays = [];
        const cursor = start.clone();
        while (cursor.isSameOrBefore(end)) {
            totalDays.push(cursor.clone());
            cursor.add(1, 'day');
        }

        const hopWeeks = +recurrence.repeatEveryCount || 1;
        const result = [];

        for (let wStart = 0; wStart < totalDays.length; wStart += 7 * hopWeeks) {
            const weekSlice = totalDays.slice(wStart, wStart + 7);
            for (const dayMoment of weekSlice) {
                const dayName = dayMoment.format('dddd');
                if (selectedDays.includes(dayName) && this.isWithinInclusiveRange(dayMoment, rangeStart, rangeEnd)) {
                    const dateStr = dayMoment.format('MM/DD/YYYY');
                    result.push(await this.buildBaseInspectionParam(dateStr, ids, payload, context));
                }
            }
        }
        return result;
    },

    async generateMonthlyStyleEvents(recurrence, ids, payload, context, rangeStart, rangeEnd, monthStep) {
        const candidateDates = this.getMonthlyCandidateDates(recurrence, rangeStart, rangeEnd, monthStep);
        const result = [];

        for (const date of candidateDates) {
            const dateStr = date.format('MM/DD/YYYY');
            result.push(await this.buildBaseInspectionParam(dateStr, ids, payload, context));
        }

        return result;
    },

    getMonthlyCandidateDates(recurrence, rangeStart, rangeEnd, monthStep) {
        const dates = [];
        let cursor = moment(rangeStart).clone();
        const end = moment(rangeEnd);

        while (cursor.isSameOrBefore(end)) {
            const currentMonthKey = cursor.format('YYYY-MM');

            if (recurrence.chosenDateOfMonth) {
                // Specific day of the month
                const dayWanted = recurrence.dateOfMonth;
                const candidate = moment(currentMonthKey, 'YYYY-MM').date(dayWanted);
                if (candidate.month() === cursor.month() && this.isWithinInclusiveRange(candidate, rangeStart, rangeEnd)) {
                    dates.push(candidate);
                }
            } else {
                // Pattern like 'First Monday', 'Second Tuesday', 'Last Friday'
                const chosen = this.getPatternDateInMonth(recurrence.monthlyRepeatType, currentMonthKey);
                if (chosen && this.isWithinInclusiveRange(chosen, rangeStart, rangeEnd)) {
                    dates.push(chosen);
                }
            }

            cursor.add(monthStep, 'months');
        }

        return dates;
    },

    getPatternDateInMonth(pattern, currentMonthKey) {
        const [ordinal, weekday] = pattern.split(' ');
        const monthStart = moment(currentMonthKey, 'YYYY-MM').startOf('month');

        let firstWeekday = monthStart.clone().day(weekday.toLowerCase());
        if (firstWeekday.isBefore(monthStart)) {
            firstWeekday.add(7, 'days');
        }

        const weekdayList = [];
        while (firstWeekday.month() === monthStart.month()) {
            weekdayList.push(firstWeekday.clone());
            firstWeekday.add(7, 'days');
        }

        let index = 0;
        const ord = ordinal.toLowerCase();
        if (ord === 'second') index = 1;
        else if (ord === 'third') index = 2;
        else if (ord === 'fourth') index = 3;
        else if (ord === 'last') index = weekdayList.length - 1;

        return weekdayList[index];
    },


    async buildRecurringInspectionEvents(dates, dataInSeries, ids, payload, context) {
        if (!dataInSeries?.recurrence?.recurrence) return [];
        const recurrence = dataInSeries.recurrence;
        const rangeStart = dates[0];
        const rangeEnd = dates[dates.length - 1];

        switch (recurrence.recurrence) {
            case 'Daily':
                return this.generateDailyEvents(dates, recurrence, ids, payload, context, rangeStart, rangeEnd);
            case 'Weekly':
                return this.generateWeeklyEvents(dates, recurrence, ids, payload, context, rangeStart, rangeEnd);
            case 'Monthly':
                return this.generateMonthlyStyleEvents(recurrence, ids, payload, context, rangeStart, rangeEnd, 1);
            case 'Yearly':
                return this.generateMonthlyStyleEvents(recurrence, ids, payload, context, rangeStart, rangeEnd, 12);
            default:
                return [];
        }
    },

    async prepareContext(dataInSeries, payload, dates, loginUser, newRecurrenceId) {
        const memberDetails = await Member.getBy({
            UserId: loginUser.id,
            ProjectId: payload.ProjectId,
            isActive: true,
            isDeleted: false,
        });

        const projectDetails = await Project.getProjectAndSettings({
            isDeleted: false,
            id: +payload.ProjectId,
        });

        // Booking window validation (only if recurrence exists & start date provided)
        if (dataInSeries?.recurrence?.recurrence && payload.recurrence && dates?.length) {
            const startDate = await this.compareinspectionDateWithinspectionWindowDate(
                dates[0],
                payload.inspectionStartTime,
                payload.timezone,
                projectDetails.ProjectSettings.inspectionWindowTime,
                projectDetails.ProjectSettings.inspectionWindowTimeUnit,
            );
            const endDate = await this.compareinspectionDateWithinspectionWindowDate(
                dates[0],
                payload.inspectionEndTime,
                payload.timezone,
                projectDetails.ProjectSettings.inspectionWindowTime,
                projectDetails.ProjectSettings.inspectionWindowTimeUnit,
            );
            if (startDate || endDate) {
                throw new Error(
                    `Bookings can not be submitted within ${projectDetails.ProjectSettings.inspectionWindowTime} ${projectDetails.ProjectSettings.inspectionWindowTimeUnit} prior to the event`,
                );
            }
        }

        // Starting InspectionId
        const lastInspection = await InspectionRequest.findOne({
            where: { ProjectId: memberDetails.ProjectId, isDeleted: false },
            order: [['InspectionId', 'DESC']],
        });
        const startingInspectionId = lastInspection?.InspectionId || 0;

        // Starting CraneRequestId (replicating original logic)
        let lastCrane = await CraneRequest.findOne({
            where: { ProjectId: +memberDetails.ProjectId, isDeleted: false },
            order: [['CraneRequestId', 'DESC']],
        });

        const associated = await InspectionRequest.findOne({
            where: {
                ProjectId: +memberDetails.ProjectId,
                isDeleted: false,
                isAssociatedWithCraneRequest: true,
            },
            order: [['CraneRequestId', 'DESC']],
        });

        if (associated) {
            if (lastCrane) {
                if (associated.CraneRequestId > lastCrane.CraneRequestId) {
                    lastCrane.CraneRequestId = associated.CraneRequestId;
                }
            } else {
                lastCrane = { CraneRequestId: associated.CraneRequestId };
            }
        }
        const startingCraneId = (lastCrane?.CraneRequestId || 0);

        const roleDetails = await Role.getBy('Project Admin');
        const accountRoleDetails = await Role.getBy('Account Admin');

        // Counter closures
        let currentInspectionId = startingInspectionId;
        let currentCraneId = startingCraneId;
        const ids = {
            nextInspectionId() { currentInspectionId += 1; return currentInspectionId; },
            nextCraneRequestId() { currentCraneId += 1; return currentCraneId; }
        };

        return {
            memberDetails,
            projectSettings: projectDetails.ProjectSettings,
            roleDetails,
            accountRoleDetails,
            ids,
            newRecurrenceId,
            convertTimezoneToUtc: this.convertTimezoneToUtc.bind(this),
        };
    },

    async persistInspectionEvents(eventsArray, payload, loginUser, context) {
        const { memberDetails } = context;

        for (const evt of eventsArray) {
            const newInspection = await InspectionRequest.createInstance(evt);
            const updateParam = {
                InspectionId: newInspection.id,
                inspectionCode: newInspection.InspectionId,
                ProjectId: payload.ProjectId,
            };

            // Batch association arrays
            const companies = payload.companies || [];
            const gates = [payload.GateId];
            const equipments = payload.EquipmentId || [];
            const persons = payload.persons || payload.person || []; // adjust if original variable naming differs
            const define = payload.define || [];

            for (const c of companies) {
                await InspectionCompany.createInstance({ ...updateParam, CompanyId: c });
            }
            for (const g of gates) {
                await InspectionGate.createInstance({ ...updateParam, GateId: g });
            }
            for (const eq of equipments) {
                await InspectionEquipment.createInstance({ ...updateParam, EquipmentId: eq });
            }
            for (const p of persons) {
                await InspectionPerson.createInstance({ ...updateParam, MemberId: p });
            }
            for (const d of define) {
                await DeliverDefine.createInstance({ ...updateParam, DeliverDefineWorkId: d });
            }

            // History - create
            await InspectionHistory.createInstance({
                InspectionRequestId: newInspection.id,
                InspectionId: newInspection.InspectionId,
                MemberId: memberDetails.id,
                isDeleted: false,
                ProjectId: payload.ProjectId,
                type: 'create',
                description: `${loginUser.firstName} ${loginUser.lastName} Created Inspection Booking, ${payload.description}.`,
            });

            // History - approve (if applicable)
            if (newInspection.status === 'Approved') {
                await InspectionHistory.createInstance({
                    ProjectId: payload.ProjectId,
                    MemberId: memberDetails.id,
                    InspectionRequestId: newInspection.id,
                    isDeleted: false,
                    type: 'approved',
                    description: `${loginUser.firstName} ${loginUser.lastName} Approved Inspection Booking, ${payload.description}.`,
                });
            }
        }
    },

    async createCopyofInspectionRequest(dataInSeries, payload, dates, loginUser, newRecurrenceId) {
        // 'this' contains original helper methods (compareinspectionDateWithinspectionWindowDate, convertTimezoneToUtc)
        const context = await this.prepareContext(dataInSeries, payload, dates, loginUser, newRecurrenceId);

        if (!dataInSeries?.recurrence?.recurrence) return; // Nothing to do (original guarded everything within recurrence)

        const eventsArray = await this.buildRecurringInspectionEvents(
            dates,
            dataInSeries,
            context.ids,
            payload,
            context
        );

        if (eventsArray.length === 0) return;

        await this.persistInspectionEvents(eventsArray, payload, loginUser, context);
    },

    async checkinspectionConflictsWithAlreadyScheduled(requestsArray, type, gateId) {
        if (requestsArray && requestsArray.length > 0) {
            const inspectionStartDateArr = [];
            const inspectionEndDateArr = [];
            const requestIds = [];
            const recurrenceIds = [];
            requestsArray.forEach((data) => {
                inspectionStartDateArr.push(new Date(data.inspectionStart));
                inspectionEndDateArr.push(new Date(data.inspectionEnd));
                if (type === 'edit') {
                    if (data.id) {
                        requestIds.push(data.id);
                    }
                    if (data.recurrenceId) {
                        recurrenceIds.push(data.recurrenceId);
                    }
                }
            });
            let condition = {
                ProjectId: requestsArray[0].ProjectId,
                status: {
                    [Op.notIn]: ['Delivered', 'Expired'],
                },
                isDeleted: false,
            };
            if (type === 'edit') {
                if (recurrenceIds && recurrenceIds.length > 0) {
                    condition = {
                        ...condition,
                        recurrenceId: {
                            [Op.notIn]: recurrenceIds,
                        },
                    };
                } else {
                    condition = {
                        ...condition,
                        id: {
                            [Op.notIn]: requestIds,
                        },
                    };
                }
            }
            const isinspectionBookingOverlapping = await InspectionRequest.findAll({
                where: {
                    ...condition,
                    [Op.or]: [
                        {
                            [Op.or]: inspectionStartDateArr.map((date) => ({
                                inspectionStart: { [Op.lt]: date },
                                inspectionEnd: { [Op.gt]: date },
                            })),
                        },
                        {
                            [Op.or]: inspectionEndDateArr.map((date) => ({
                                inspectionStart: { [Op.lt]: date },
                                inspectionEnd: { [Op.gt]: date },
                            })),
                        },
                    ],
                },
                include: [
                    {
                        association: 'gateDetails',
                        where: {
                            isDeleted: false,
                            isActive: true,
                            GateId: { [Op.eq]: +gateId },
                        },
                    },
                ],
            });
            const overlappingIds = []
            isinspectionBookingOverlapping.forEach(data => {
                overlappingIds.push(data.id)
            })
            const voidData = await VoidList.findAll({
                where: {
                    InspectionRequestId: {
                        [Op.in]: overlappingIds
                    }
                }
            })
            const checkOverlappingInVoid = isinspectionBookingOverlapping.map(data => data.id);
            const allPresentInVoidList = checkOverlappingInVoid.every(id =>
                voidData.some(voidEntry => voidEntry.InspectionRequestId === id)
            );
            return allPresentInVoidList && allPresentInVoidList.length > 0;
        }
    },
    async checkDoubleBookingAllowedOrNot(eventsArray, projectDetails, type, gateId) {
        if (!projectDetails.ProjectSettings.inspectionAllowOverlappingBooking) {
            const checkBookingOverlapping = await this.checkinspectionConflictsWithAlreadyScheduled(
                eventsArray,
                type,
                gateId,
            );
            if (checkBookingOverlapping) {
                return {
                    error: true,
                    message:
                        'This booking clashes with another booking. Overlapping is disabled by the administrator.',
                };
            }
        }
        if (
            projectDetails.ProjectSettings &&
            !projectDetails.ProjectSettings.inspectionAllowOverlappingCalenderEvents
        ) {
            const checkCalenderEventsOverlapping =
                await concreteRequestService.checkCalenderEventsOverlappingWithBooking(
                    eventsArray,
                    'inspection',
                    type,
                );
            if (checkCalenderEventsOverlapping) {
                return {
                    error: true,
                    message:
                        'This booking clashes with a scheduled calendar event. Overlapping is disabled by the administrator',
                };
            }
        }
        return {
            error: false,
            message: '',
        };
    },
    async createNewRecurrenceEvents(
        inspectionData,
        memberDetails,
        totalDays1,
        roleDetails,
        accountRoleDetails,
        projectDetails,
        done,
    ) {
        if (inspectionData.recurrence === 'Daily') {
            return this.processRecurrenceDailyEvents(
                inspectionData,
                totalDays1,
                memberDetails,
                roleDetails,
                accountRoleDetails,
                projectDetails,
                done
            );
        }
        if (inspectionData.recurrence === 'Weekly') {
            return this.processRecurrenceWeeklyEvents(
                inspectionData,
                memberDetails,
                roleDetails,
                accountRoleDetails,
                projectDetails,
                done
            );
        }
        if (inspectionData.recurrence === 'Monthly') {
            return this.processRecurrenceMonthlyEvents(
                inspectionData,
                memberDetails,
                roleDetails,
                accountRoleDetails,
                projectDetails,
                done
            );
        }
        if (inspectionData.recurrence === 'Yearly') {
            return this.processRecurrenceYearlyEvents(
                inspectionData,
                memberDetails,
                roleDetails,
                accountRoleDetails,
                projectDetails,
                done
            );
        }
    },
    async updateDeliveredStatus(inputData, done) {
        try {
            const inspectionData = await InspectionRequest.findAll({
                where: {
                    isDeleted: false,
                    status: 'Approved',
                    inspectionStatus: {
                        [Op.in]: ['Pass', 'Fail']
                    },
                },
            });
            for (let index = 0; index < inspectionData.length; index++) {
                const element = inspectionData[index];
                await InspectionRequest.update(
                    { status: 'Delivered' },
                    {
                        where: { id: +element.id },
                    },
                );
                if (inspectionData.length === index + 1) {
                    return done(inspectionData, false);
                }
            }
        } catch (e) {
            return done(null, e);
        }
    },

    async createEquipmentMapping(payload) {
        try {
            if (payload) {
                payload.EquipmentId = JSON.stringify(payload.EquipmentId)
                const createMapping = await EquipmentMapping.create(payload);
                if (createMapping) {
                    return true;
                }
            }
        } catch (err) {
            console.log(err, "error in create Mapping")
            return err
        }
    },

    async deleteEquipmentMapping(payload) {
        try {
            if (payload) {
                const condition = {
                    where: {
                        [Op.and]: {
                            GateId: payload.GateId,
                            LocationId: payload.LocationId,
                            InspectionId: payload.InspectionId,
                        },
                    },
                };
                const deleteMapping = await EquipmentMapping.destroy(condition);
                return !!deleteMapping;
            }
        } catch (err) {
            console.log(err, "error in create Mapping")
            return err
        }
    },

    async findEquipmentMapping(payload) {
        try {
            const eventTimeZone = await TimeZone.findOne({
                where: {
                    isDeleted: false,
                    id: +payload.TimeZoneId,
                },
                attributes: [
                    'id',
                    'location',
                    'isDayLightSavingEnabled',
                    'timeZoneOffsetInMinutes',
                    'dayLightSavingTimeInMinutes',
                    'timezone',
                ],
            });
            if (!eventTimeZone) {
                return done(null, { message: 'Provide a valid timezone' });
            }
            const chosenTimezoneStart = moment.tz(
                `${payload.inspectionStart} ${payload.startPicker}`,
                'YYYY MM DD 00:00:00 HH:mm',
                eventTimeZone.timezone,
            );
            const chosenTimezoneEnd = moment.tz(
                `${payload.inspectionEnd} ${payload.endPicker}`,
                'YYYY MM DD 00:00:00 HH:mm',
                eventTimeZone.timezone,
            );
            const Start = chosenTimezoneStart
                .clone()
                .tz('UTC')
                .format('YYYY-MM-DD HH:mm:ssZ');
            const End = chosenTimezoneEnd
                .clone()
                .tz('UTC')
                .format('YYYY-MM-DD HH:mm:ssZ');
            const results = await EquipmentMapping.findAll({
                where: {
                    [Op.and]: [
                        {
                            [Op.or]: [
                                {
                                    startTime: {
                                        [Op.between]: [Start, End],
                                    },
                                },
                                {
                                    endTime: {
                                        [Op.between]: [Start, End],
                                    },
                                },
                            ],
                        },
                        Sequelize.literal(
                            `string_to_array(trim(both '[]' from "EquipmentId"), ',')::int[] && ARRAY[${payload.EquipmentId.join(',')}]::int[]`
                        ),
                    ],
                },
            });

            return results.length === 0;
        } catch (err) {
            console.log(err, "Error in Find Mapping")
        }
    }
};
module.exports = inspectionService;