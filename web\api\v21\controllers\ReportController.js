const status = require('http-status');
const moment = require('moment');

const { deliveryReportService } = require('../services');
const { craneReportService } = require('../services');
const { concreteReportService } = require('../services');
const awsConfig = require('../middlewares/awsConfig');
const puppeteerService = require('../services/puppeteerService');
const { inspectionReportService } = require('../services');

const ReportController = {
  async deliveryRequest(req, res, next) {
    try {
      await deliveryReportService.listDeliveryRequest(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Delivery Booking listed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async craneRequest(req, res, next) {
    try {
      await craneReportService.listCraneRequest(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Crane Booking listed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async concreteRequest(req, res, next) {
    try {
      await concreteReportService.listConcreteRequest(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Concrete Booking listed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async exportDeliveryReport(req, res, next) {
    try {
      await deliveryReportService.exportReport(req, async (response, error) => {
        if (error) {
          next(error);
        } else if (req.body.exportType === 'PDF' || req.body.exportType === 'CSV') {
          res.status(status.OK).json({
            message: 'Delivery Booking exported Successfully.',
            data: response,
          });
        } else if (req.body.exportType === 'EXCEL') {
          if (req.body.saved) {
            res.status(status.OK).json({
              message: 'Report saved Successfully.',
              data: response,
            });
          } else {
            res.setHeader(
              'Content-Type',
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8;responseType= arraybuffer"',
            );
            res.setHeader(
              'Content-Disposition',
              `attachment; filename=${req.body.reportName}.xlsx`,
            );
            await response.xlsx.write(res);
            res.end();
          }
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async exportCraneReport(req, res, next) {
    try {
      await craneReportService.exportReport(req, async (response, error) => {
        if (error) {
          next(error);
        } else if (req.body.exportType === 'PDF' || req.body.exportType === 'CSV') {
          res.status(status.OK).json({
            message: 'Crane Booking exported Successfully.',
            data: response,
          });
        } else if (req.body.exportType === 'EXCEL') {
          if (req.body.saved) {
            res.status(status.OK).json({
              message: 'Report saved Successfully.',
              data: response,
            });
          } else {
            res.setHeader(
              'Content-Type',
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8;responseType= arraybuffer"',
            );
            res.setHeader(
              'Content-Disposition',
              `attachment; filename=${req.body.reportName}.xlsx`,
            );
            await response.xlsx.write(res);
            res.end();
          }
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async exportConcreteReport(req, res, next) {
    try {
      await concreteReportService.exportReport(req, async (response, error) => {
        if (error) {
          next(error);
        } else if (req.body.exportType === 'PDF' || req.body.exportType === 'CSV') {
          res.status(status.OK).json({
            message: 'Crane Booking exported Successfully.',
            data: response,
          });
        } else if (req.body.exportType === 'EXCEL') {
          if (req.body.saved) {
            res.status(status.OK).json({
              message: 'Report saved Successfully.',
              data: response,
            });
          } else {
            res.setHeader(
              'Content-Type',
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8;responseType= arraybuffer"',
            );
            res.setHeader(
              'Content-Disposition',
              `attachment; filename=${req.body.reportName}.xlsx`,
            );
            await response.xlsx.write(res);
            res.end();
          }
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async weeklyCalendarRequest(req, res, next) {
    try {
      const response = await deliveryReportService.getTotalRequestList(req, next);
      res.status(status.OK).json({
        message: 'Booking listed Successfully.',
        data: response,
      });
    } catch (e) {
      next(e);
    }
  },
  async exportWeeklyCalendarRequest(req, res, next) {
    try {
      if (req.body.exportType === 'EXCEL') {
        deliveryReportService.exportWeeklyCalendarReport(req, async (response, error) => {
          if (error) {
            next(error);
          } else if (req.body.exportType === 'EXCEL') {
            if (req.body.saved) {
              res.status(status.OK).json({
                message: 'Report saved Successfully.',
                data: response,
              });
            } else {
              res.setHeader(
                'Content-Type',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8;responseType= arraybuffer"',
              );
              res.setHeader(
                'Content-Disposition',
                `attachment; filename=${req.body.reportName}.xlsx`,
              );
              await response.xlsx.write(res);
              res.end();
            }
          }
        });
      } else {
        const incomeData = req.body;
        const { params } = req;
        const loginUser = req.user;
        const startTime = moment(incomeData.eventStartTime, 'HH:mm:ss');
        const endTime = moment(incomeData.eventEndTime, 'HH:mm:ss');
        const duration = moment.duration(endTime.diff(startTime));
        const hourDifference = duration.asHours();
        if (hourDifference > 12) {
          return res.status(status.OK).json({
            message:
              'Currently, we are not supporting more than 12 hours time difference for PDF export. please change the start and end time in the filter section.',
            data: [],
          });
        }

        const template = JSON.parse(JSON.stringify(incomeData.templateType))
          .map((d) => d.id)
          .join(',');
        const url = `${process.env.BASE_URL}/fc/${params.ProjectId}?user=${loginUser.id}&start=${incomeData.startDate !== '' ? incomeData.startDate : incomeData.start
          }&end=${incomeData.endDate !== '' ? incomeData.endDate : incomeData.end}&parent=${incomeData.ParentCompanyId
          }&company=${incomeData.companyFilter}&start_time=${incomeData.startTime}&end_time=${incomeData.endTime
          }&event_start=${incomeData.eventStartTime}&event_end=${incomeData.eventEndTime}&equip=${incomeData.equipmentFilter
          }&gate=${incomeData.gateFilter}&member=${incomeData.memberFilter}&define=${incomeData.memberFilter
          }${incomeData.statusFilter == '' ? '&status=' : `&status=${incomeData.statusFilter}`
          }&timezone=${incomeData.timezone}&dst=${incomeData.isDST}&template=${template}&location=${incomeData.locationFilter}`;
        const { timezone } = incomeData;
        const result = await puppeteerService.generatePdfByURL(url, timezone, next);
        if (Buffer.isBuffer(result)) {
          awsConfig.reportUpload(
            result,
            req.body.reportName,
            req.body.exportType,
            async (result, error1) => {
              if (!error1) {
                if (req.body.saved) {
                  req.body.reportType = 'weekly-calender';
                  const savedData = await deliveryReportService.createSavedReports(req, result);
                  if (savedData) {
                    res.status(status.OK).json({
                      message: 'Report saved Successfully.',
                      data: result,
                    });
                  }
                } else {
                  res.status(status.OK).json({
                    message: 'Weekly calendar exported Successfully.',
                    data: result,
                  });
                }
              } else {
                next(error1);
              }
            },
          );
        } else {
          res.status(status.OK).json({
            message: 'No data found',
            data: [],
          });
        }
      }
    } catch (e) {
      next(e);
    }
  },
  async heatMapdeliveryRequest(req, res, next) {
    try {
      await deliveryReportService.heatMapListDeliveryRequest(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Success.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async exportHeatMapRequest(req, res, next) {
    try {
      await deliveryReportService.exportHeatMapReport(req, async (response, error) => {
        if (error) {
          next(error);
        } else if (req.body.exportType === 'PDF') {
          if (response.hasOwnProperty('no_data')) {
            return res.status(status.OK).json({
              message: 'No data found.',
              data: [],
            });
          }
          res.status(status.OK).json({
            message: 'Heat map exported Successfully.',
            data: response,
          });
        } else if (req.body.exportType === 'EXCEL') {
          res.setHeader(
            'Content-Type',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8;responseType= arraybuffer"',
          );
          res.setHeader('Content-Disposition', `attachment; filename=${req.body.reportName}.xlsx`);
          await response.xlsx.write(res);
          res.end();
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async deleteSchedulerReport(req, res, next) {
    try {
      const deleteReport = await deliveryReportService.deleteSchedulerReport(req.query);
      if (deleteReport) {
        res.status(status.OK).json({
          message: 'Scheduled report deleted successfully.',
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async inspectionRequest(req, res, next) {
    try {
      await inspectionReportService.listInspectionRequest(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Inspection Booking listed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },

  async exportInspectionReport(req, res, next) {
    try {
      await inspectionReportService.exportReport(req, async (response, error) => {
        if (error) {
          next(error);
        } else if (req.body.exportType === 'PDF' || req.body.exportType === 'CSV') {
          res.status(status.OK).json({
            message: 'Inspection Booking exported Successfully.',
            data: response,
          });
        } else if (req.body.exportType === 'EXCEL') {
          if (req.body.saved) {
            res.status(status.OK).json({
              message: 'Report saved Successfully.',
              data: response,
            });
          } else {
            res.setHeader(
              'Content-Type',
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8;responseType= arraybuffer"',
            );
            res.setHeader(
              'Content-Disposition',
              `attachment; filename=${req.body.reportName}.xlsx`,
            );
            await response.xlsx.write(res);
            res.end();
          }
        }
      });
    } catch (e) {
      next(e);
    }
  },
};
module.exports = ReportController;
