/* eslint-disable prettier/prettier */
const moment = require('moment');
const fs = require('fs');
const awsConfig = require('../middlewares/awsConfig');
const { Project, Company } = require('../models');
const puppeteerService = require('./puppeteerService');

const pdfInspectionReportService = {
    // 1. Extract headers and determine selected flags
    extractHeaderSelectionAndBuildTableHeader(req) {
        const { selectedHeaders } = req.body;
        const header = [];
        const selectedFlags = {
            isIdSelected: false,
            isDescriptionSelected: false,
            isDateSelected: false,
            isStatusSelected: false,
            isinspectionStatusSelected: false,
            isinspectionTypeSelected: false,
            isApprovedBySelected: false,
            isEquipmentSelected: false,
            isDfowSelected: false,
            isGateSelected: false,
            isCompanySelected: false,
            isPersonSelected: false,
            isLocationSelected: false
        };

        selectedHeaders.forEach((item) => {
            if (item.isActive) {
                selectedFlags[`is${item.key.charAt(0).toUpperCase()}${item.key.slice(1)}Selected`] = true;
                header.push(`<th style="text-align:center">${item.title}</th>`);
            }
        });

        return { selectedFlags, header };
    },

    // 2. Build table rows using selected flags
    buildTableRows(data, selectedFlags, timezoneoffset) {
        const rows = [];
        const td = (value) => `<td style="padding:5px;color:#5B5B5B;font-weight:600;text-align:center;font-size:12px;font-family:'Cairo',sans-serif">${value}</td>`;
        const wrapList = (list) => Array.isArray(list) ? list.map(val => `<p>${val}</p>`).join('') : '-';

        data.forEach(item => {
            const row = this.buildTableRow(item, selectedFlags, timezoneoffset, td, wrapList);
            rows.push(`<tr style="border-bottom:1px solid #e0e0e0;font-size:12px">${row}</tr>`);
        });

        return rows;
    },

    buildTableRow(item, selectedFlags, timezoneoffset, td, wrapList) {
        const deliveryStart = moment(item.deliveryStart).add(Number(timezoneoffset), 'minutes');
        const deliveryEnd = moment(item.deliveryEnd).add(Number(timezoneoffset), 'minutes');

        const cellBuilders = [
            { flag: 'isIdSelected', value: () => item.InspectionId },
            { flag: 'isDescriptionSelected', value: () => item.description },
            { flag: 'isDateSelected', value: () => `${deliveryStart.format('MM/DD/YYYY hh:mm a')} - ${deliveryEnd.format('hh:mm a')}` },
            { flag: 'isStatusSelected', value: () => item.status === 'Delivered' ? 'Completed' : item.status },
            { flag: 'isinspectionStatusSelected', value: () => item.inspectionStatus || '-' },
            { flag: 'isinspectionTypeSelected', value: () => item.inspectionType || '-' },
            { flag: 'isApprovedBySelected', value: () => this.getApproverName(item.approverDetails) },
            { flag: 'isEquipmentSelected', value: () => wrapList(item.equipmentDetails?.map(e => e.Equipment.equipmentName)) },
            { flag: 'isDfowSelected', value: () => wrapList(item.defineWorkDetails?.map(d => d.DeliverDefineWork.DFOW)) },
            { flag: 'isGateSelected', value: () => item.gateDetails?.[0]?.Gate?.gateName || '-' },
            { flag: 'isCompanySelected', value: () => wrapList(item.companyDetails?.map(c => c.Company.companyName)) },
            { flag: 'isPersonSelected', value: () => wrapList(item.memberDetails?.map(m => `${m.Member.User.firstName} ${m.Member.User.lastName}`)) },
            { flag: 'isLocationSelected', value: () => item.location?.locationPath || '-' }
        ];

        return cellBuilders
            .map(builder => selectedFlags[builder.flag] ? td(builder.value()) : '')
            .join('');
    },

    getApproverName(approverDetails) {
        return approverDetails ? `${approverDetails.User.firstName} ${approverDetails.User.lastName}` : '-';
    },

    // 3. Generate and fill the PDF template
    generatePdfTemplate(templatePath, projectData, companyData, loginUser, req, header, content) {
        let template = fs.readFileSync(templatePath, 'utf-8');

        return template
            .replace('$projectName', projectData.projectName)
            .replace('$companyName', companyData.companyName)
            .replace('$generatedDate', req.body.generatedDate)
            .replace('$generatedBy', `${loginUser.firstName} ${loginUser.lastName}`)
            .replace('$reportType', 'Inspection')
            .replace('$header', header.join(''))
            .replace('$data', content.join(''))
            .replace(/,/g, '');
    },

    async pdfFormatOfInspectionRequest(params, loginUser, data, req, done) {
        try {
            const { timezoneoffset } = req.headers;
            const { selectedFlags, header } = this.extractHeaderSelectionAndBuildTableHeader(req);

            const projectData = await Project.findOne({
                where: { isDeleted: false, id: +params.ProjectId },
                attributes: ['projectName']
            });

            const companyData = await Company.findOne({
                where: { isDeleted: false, ParentCompanyId: +req.body.ParentCompanyId, isParent: true },
                attributes: ['companyName']
            });

            const rows = this.buildTableRows(data, selectedFlags, timezoneoffset);
            const pdftemplate = this.generatePdfTemplate(
                '/usr/src/web/api/v21/views/mail-templates/deliveryReport.html',
                projectData,
                companyData,
                loginUser,
                req,
                header,
                rows
            );

            const pdfBuffer = await puppeteerService.generatePdfBuffer(pdftemplate);
            if (pdfBuffer) {
                awsConfig.reportUpload(pdfBuffer, req.body.reportName, req.body.exportType, (result, error) => {
                    if (!error) done(result, false);
                    else done(false, { message: 'Upload failed' });
                });
            } else {
                done(false, { message: 'Cannot export the document' });
            }
        } catch (err) {
            done(false, { message: 'Unexpected error during PDF generation', error: err.message });
        }
    }


};
module.exports = pdfInspectionReportService;
