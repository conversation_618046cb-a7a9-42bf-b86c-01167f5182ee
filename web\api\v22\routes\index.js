const express = require('express');

const { Router } = express;
const apiRouter = Router();
const swaggerUi = require('swagger-ui-express');
const swaggerDocument = require('../swagger/swagger');

const adminRoute = require('./adminRoute');
const userRoute = require('./userRoute');
const authRoute = require('./authRoute');
const stripeRoute = require('./stripeRoute');
const equipmentRoute = require('./equipmentRoute');
const equipmentLogRoute = require('./equipmentLogRoute');
const wasteLogRoute = require('./wasteLogRoute');
const HaulingLogRoute = require('./haulingRoute');
const utilitiesRoute = require('./utilitiesRoute');
const gateRoute = require('./gateRoute');
const companyRoute = require('./companyRoute');
const memberRoute = require('./memberRoute');
const projectRoute = require('./projectRoute');
const deliveryRoute = require('./deliveryRoute');
const inspectionRoute = require('./inspectionRoute');
const overRideRoute = require('./overRideRoute');
const restrictMailRoute = require('./restrictMailRoute');
const addressRoute = require('./addressRoute');
const defineRoute = require('./defineRoute');
const voidRoute = require('./voidRoute');
const attachementRoute = require('./attachementRoute');
const historyRoute = require('./historyRoute');
const commentRoute = require('./commentRoute');
const calendarRoute = require('./calendarRoute');
const notificationRoute = require('./notificationRoute');
const dashboardRoute = require('./dashboardRoute');
const deviceTokenRoute = require('./deviceTokenRoute');
const billingRoute = require('./billingRoute');
const accountRoute = require('./accountRoute');
const craneRequestRoute = require('./craneRequestRoute');
const craneRequestAttachmentRoute = require('./craneRequestAttachmentRoute');
const craneRequestCommentRoute = require('./craneRequestCommentRoute');
const craneRequestHistoryRoute = require('./craneRequestHistoryRoute');
const calendarSettingsRoute = require('./calendarSettingsRoute');
const timezoneRoute = require('./timezoneRoute');
const notificationPreferenceRoute = require('./notificationPreferenceRoute');
const concreteRequestRoute = require('./concreteRequestRoute');
const concreteRequestAttachmentRoute = require('./concreteRequestAttachmentRoute');
const concreteRequestCommentRoute = require('./concreteRequestCommentRoute');
const concreteRequestHistoryRoute = require('./concreteRequestHistoryRoute');
const reportRoute = require('./reportRoute');
const projectSettingsRoute = require('./projectSettingsRoute');
const locationRoute = require('./locationRoute');
const guestUserRoute = require('./guestUserRoute');
const bookingTemplatesRoute = require('./bookingTemplatesRoute');
const carbonEmissionRoute = require('./carbonEmissionRoute');
const monitoringRoute = require('./monitoringRoute');


const options = {
  customCss: `
  .swagger-ui .topbar {
    display: none;
  }
  .swagger-ui .models {
    display: none;
  }`,
};
apiRouter.use('/docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument, options));

apiRouter.use('/auth', authRoute.router);
apiRouter.use('/user', userRoute.router);
apiRouter.use('/admin', adminRoute.router);
apiRouter.use('/payment', stripeRoute.router);
apiRouter.use('/equipment', equipmentRoute.router);
apiRouter.use('/equipmentLog', equipmentLogRoute.router);
apiRouter.use('/wasteLog', wasteLogRoute.router);
apiRouter.use('/haulingLog', HaulingLogRoute.router);
apiRouter.use('/utilities', utilitiesRoute.router);
apiRouter.use('/gates', gateRoute.router);
apiRouter.use('/company', companyRoute.router);
apiRouter.use('/member', memberRoute.router);
apiRouter.use('/project', projectRoute.router);
apiRouter.use('/delivery', deliveryRoute.router);
apiRouter.use('/inspection', inspectionRoute.router);
apiRouter.use('/overRide', overRideRoute.router);
apiRouter.use('/restrictMail', restrictMailRoute.router);
apiRouter.use('/address', addressRoute.router);

apiRouter.use('/definable', defineRoute.router);
apiRouter.use('/void', voidRoute.router);
apiRouter.use('/attachement', attachementRoute.router);
apiRouter.use('/comment', commentRoute.router);
apiRouter.use('/history', historyRoute.router);
apiRouter.use('/calendar', calendarRoute.router);
apiRouter.use('/notification', notificationRoute.router);
apiRouter.use('/dashboard', dashboardRoute.router);
apiRouter.use('/deviceToken', deviceTokenRoute.router);
apiRouter.use('/billing', billingRoute.router);
apiRouter.use('/account', accountRoute.router);
apiRouter.use('/crane_request', craneRequestRoute.router);
apiRouter.use('/crane_request_attachment', craneRequestAttachmentRoute.router);
apiRouter.use('/crane_request_comment', craneRequestCommentRoute.router);
apiRouter.use('/crane_request_history', craneRequestHistoryRoute.router);
apiRouter.use('/calendar_settings', calendarSettingsRoute.router);
apiRouter.use('/timezone_list', timezoneRoute.router);
apiRouter.use('/notification_preference', notificationPreferenceRoute.router);
apiRouter.use('/concrete_request', concreteRequestRoute.router);
apiRouter.use('/concrete_request_attachment', concreteRequestAttachmentRoute.router);
apiRouter.use('/concrete_request_comment', concreteRequestCommentRoute.router);
apiRouter.use('/concrete_request_history', concreteRequestHistoryRoute.router);
apiRouter.use('/reports', reportRoute.router);
apiRouter.use('/project_settings', projectSettingsRoute.router);
apiRouter.use('/location', locationRoute.router);
apiRouter.use('/guest_user', guestUserRoute.router);
apiRouter.use('/templates', bookingTemplatesRoute.router);
apiRouter.use('/carbon_emission', carbonEmissionRoute.router);
apiRouter.use('/monitoring', monitoringRoute.router);


module.exports = apiRouter;
