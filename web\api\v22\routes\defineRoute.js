const { Router } = require('express');
const { validate } = require('express-validation');
const multer = require('multer');
const passportConfig = require('../config/passport');
const { DefineController } = require('../controllers');
const { defineValidation } = require('../middlewares/validations');
const checkAdmin = require('../middlewares/checkAdmin');

const upload = multer({ dest: 'uploads/' });

const defineRoute = {
  get router() {
    const router = Router();
    router.post(
      '/create_definable/:ProjectId/?:ParentCompanyId',
      upload.single('definable'),
      validate(defineValidation.createDefine, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      DefineController.createDefinable,
    );
    router.post(
      '/get_definable/:ProjectId/:pageSize/:pageNo/:sort/:export/:sortByField',
      validate(defineValidation.getDefinable, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      DefineController.getDefinable,
    );
    router.get(
      '/export_definable/:ProjectId/:sort/?:ParentCompanyId',
      validate(defineValidation.exportDefinable, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      DefineController.exportDefinable,
    );
    router.post(
      '/update_definable/:ProjectId',
      validate(defineValidation.updateDefinable, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      DefineController.updateDefinable,
    );
    router.post(
      '/delete_definable/',
      validate(defineValidation.deleteDefinable, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      DefineController.deleteDefinable,
    );
    router.post(
      '/add_definable/',
      validate(defineValidation.addDefinable, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      DefineController.addDefinable,
    );
    router.post(
      '/sample_dfow_excel_download/:ProjectId/',
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdmin,
      DefineController.sampleExcelDownload,
    );
    return router;
  },
};
module.exports = defineRoute;
