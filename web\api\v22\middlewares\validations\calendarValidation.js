const Joi = require('joi');

const calendarValidation = {
  getEventNDR: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      void: Joi.number().required(),
    }),
    body: Joi.object({
      companyFilter: Joi.number(),
      descriptionFilter: Joi.optional().allow(''),
      memberFilter: Joi.number(),
      gateFilter: Joi.number(),
      equipmentFilter: Joi.number().optional().allow(null),
      ParentCompanyId: Joi.number(),
      statusFilter: Joi.optional().allow('', null),
      locationFilter: Joi.string().optional().allow('', null),
      dateFilter: Joi.optional().allow('', null),
      pickFrom: Joi.optional().allow('', null),
      pickTo: Joi.optional().allow('', null),
      search: Joi.optional().allow('', null),
      filterCount: Joi.number().required(),
      start: Joi.date(),
      end: Joi.date(),
      calendarView: Joi.string().required(),
    }),
  },
  getEventNDRcalender: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      void: Joi.number().required(),
    }),
    body: Joi.object({
      companyFilter: Joi.number(),
      descriptionFilter: Joi.optional().allow(''),
      memberFilter: Joi.number(),
      gateFilter: Joi.number(),
      equipmentFilter: Joi.number().optional().allow(null),
      ParentCompanyId: Joi.number(),
      statusFilter: Joi.optional().allow('', null),
      dateFilter: Joi.optional().allow('', null),
      pickFrom: Joi.optional().allow('', null),
      pickTo: Joi.optional().allow('', null),
      search: Joi.optional().allow('', null),
      filterCount: Joi.number().required(),
      start: Joi.date(),
      end: Joi.date(),
      currentViewMonth: Joi.string().required(),
    }),
  },
  getDeliveryRequestWithCrane: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      void: Joi.number().required(),
    }),
    body: Joi.object({
      descriptionFilter: Joi.optional().allow('', null),
      companyFilter: Joi.number().optional(),
      memberFilter: Joi.number(),
      gateFilter: Joi.number().allow('', null),
      equipmentFilter: Joi.string().allow('', null),
      ParentCompanyId: Joi.number(),
      statusFilter: Joi.optional().allow(''),
      locationFilter: Joi.string().optional().allow('', null),
      dateFilter: Joi.optional().allow(''),
      search: Joi.optional().allow(''),
      pickFrom: Joi.optional().allow('', null),
      pickTo: Joi.optional().allow('', null),
      filterCount: Joi.number().required(),
      start: Joi.date(),
      end: Joi.date(),
      calendarView: Joi.string().required(),
    }),
  },
  getEventcranecalender: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      void: Joi.number().required(),
    }),
    body: Joi.object({
      descriptionFilter: Joi.optional().allow('', null),
      companyFilter: Joi.number().optional(),
      memberFilter: Joi.number(),
      gateFilter: Joi.number().allow('', null),
      equipmentFilter: Joi.string().allow('', null),
      ParentCompanyId: Joi.number(),
      statusFilter: Joi.optional().allow(''),
      dateFilter: Joi.optional().allow(''),
      search: Joi.optional().allow(''),
      pickFrom: Joi.optional().allow('', null),
      pickTo: Joi.optional().allow('', null),
      filterCount: Joi.number().required(),
      start: Joi.date(),
      end: Joi.date(),
      currentViewMonth: Joi.string().required(),
    }),
  },
};
module.exports = calendarValidation;
