const status = require('http-status');
const Cryptr = require('cryptr');

const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
const { Member, ProjectSettings } = require('../models');

const { inspectionService, projectService } = require('../services');
const exportService = require('../services/exportService');

const inspectionController = {
    async newRequest(req, res, next) {
        try {
            // const checkMapping = await inspectionService.findEquipmentMapping(req.body);
            // if (checkMapping) {
            await inspectionService.newRequest(req, (response, error) => {
                if (error) {
                    next(error);
                } else {
                    res.status(status.CREATED).json({
                        message: 'Inspection Booking Created Successfully.',
                        data: response,
                    });
                }
            });
            // } else {
            //     res.status(status.CONFLICT).json({ message: 'Selected Equipment is Already Booked with Same Date and Time.Please change Date or Time to Proceed Furthur' });
            // }
        } catch (e) {
            next(e);
        }
    },
    async editRequest(req, res, next) {
        try {
            await inspectionService.editRequest(req, (response, error) => {
                if (error) {
                    next(error);
                } else {
                    res.status(status.CREATED).json({
                        message: 'Inspection Booking Updated Successfully.',
                        data: response,
                    });
                }
            });
        } catch (e) {
            next(e);
        }
    },
    async listNDR(req, res, next) {
        try {
            await inspectionService.listNDR(req, async (response, error) => {
                if (error) {
                    next(error);
                } else {
                    let response1;
                    let response2;
                    if (req.params.ProjectId !== '') {
                        response1 = await ProjectSettings.getCalendarStatusColor(req.params.ProjectId);
                        response2 = await ProjectSettings.getCalendarCard(req.params.ProjectId);
                    }
                    inspectionService.lastinspection(req, (lastDetail, error1) => {
                        if (!error1) {
                            res.status(status.OK).json({
                                message: 'Inspection Booking listed Successfully.',
                                data: response,
                                lastId: lastDetail,
                                statusData: response1,
                                cardData: response2,
                            });
                        } else {
                            next(error1);
                        }
                    });
                }
            });
        } catch (e) {
            next(e);
        }
    },
    async getNDRData(req, res, next) {
        try {
            await inspectionService.getNDRData(req, (response, error) => {
                if (error) {
                    next(error);
                } else {
                    res.status(status.OK).json({
                        message: 'Inspection Booking listed Successfully.',
                        data: response,
                    });
                }
            });
        } catch (e) {
            next(e);
        }
    },
    async getMemberData(req, res, next) {
        try {
            await inspectionService.getMemberData(req, (response, error) => {
                if (error) {
                    next(error);
                } else {
                    res.status(status.OK).json({
                        message: 'Member listed Successfully.',
                        data: response,
                    });
                }
            });
        } catch (e) {
            next(e);
        }
    },
    async updateNDRStatus(req, res, next) {
        try {
            await inspectionService.updateNDRStatus(req, (response, error) => {
                if (error) {
                    next(error);
                } else {
                    res.status(status.OK).json({
                        message: `${req.body.status} Successfully.`,
                        data: response,
                    });
                }
            });
        } catch (e) {
            next(e);
        }
    },
    async updateDeliveredStatus(req, res, next) {
        try {
            await inspectionService.updateDeliveredStatus(req, (response, error) => {
                if (error) {
                    next(error);
                } else {
                    res.status(status.OK).json({
                        message: `Updated Successfully.`,
                        data: response,
                    });
                }
            });
        } catch (e) {
            next(e);
        }
    },
    async sampleBulkinspectionRequestTemplate(req, res, next) {
        try {
            const data = {
                ProjectId: req.params.ProjectId,
                ParentCompanyId: req.params.ParentCompanyId,
            };
            req.data = data;
            const Workbook = await exportService.sampleinspectionRequestTemplate(req);
            const projectDetail = await projectService.getProjectDetails(req);
            const fileName = `${projectDetail.projectName}_${projectDetail.id}_${new Date().getTime()}`;
            if (Workbook) {
                res.setHeader(
                    'Content-Type',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8;responseType= arraybuffer"',
                );
                res.setHeader('Content-Disposition', `attachment; filename=${fileName}.xlsx`);
                await Workbook.xlsx.write(res);
                res.end();
            } else {
                res.status(422).json({ message: 'cannot export document', status: 422 });
            }
        } catch (e) {
            next(e);
        }
    },
    async bulkUploadinspectionRequest(req, res, next) {
        try {
            await inspectionService.bulkinspectionRequestUpload(req, (response, error) => {
                if (error) {
                    next(error);
                } else {
                    res.status(status.CREATED).json({
                        message: 'Inspection Bookings uploaded successfully.',
                        data: response,
                    });
                }
            });
        } catch (e) {
            next(e);
        }
    },
    async deleteQueuedNdr(req, res, next) {
        try {
            await inspectionService.deleteQueuedNdr(req, (response, error) => {
                if (error) {
                    next(error);
                } else {
                    res.status(status.OK).json({
                        message: 'Queued Inspection Booking Deleted Successfully.',
                        data: response,
                    });
                }
            });
        } catch (e) {
            next(e);
        }
    },
    async editMultipleinspectionRequest(req, res, next) {
        try {
            const editinspectionRequests = await inspectionService.editMultipleInspectionRequest(req);
            if (editinspectionRequests.success) {
                res.status(200).json({
                    message: 'Inspection Booking Updated Successfully.',
                    data: editinspectionRequests.data,
                });
            } else if (!editinspectionRequests.success) {
                res.status(422).json({ message: editinspectionRequests.message });
            } else {
                res.status(500).json({ message: editinspectionRequests.message });
            }
        } catch (e) {
            next(e);
        }
    },
    async getLastinspectionRequestId(req, res, next) {
        try {
            await inspectionService.lastinspection(req, (lastDetail, error1) => {
                if (!error1) {
                    res.status(status.OK).json({
                        message: 'Inspection Booking Last Id Viewed Successfully.',
                        lastId: lastDetail,
                    });
                } else {
                    next(error1);
                }
            });
        } catch (e) {
            next(e);
        }
    },
    async decryption(req, res, next) {
        try {
            const encrpytedRequestId = req.body.encryptedRequestId;
            const encrpytedMemberId = req.body.encryptedMemberId;
            const decryptedRequestId = cryptr.decrypt(encrpytedRequestId);
            const decryptedMemberId = cryptr.decrypt(encrpytedMemberId);
            const memberData = await Member.findOne({
                where: {
                    ProjectId: req.body.ProjectId,
                    ParentCompanyId: req.body.ParentCompanyId,
                    UserId: req.user.id,
                    isDeleted: false,
                },
            });
            if (
                +decryptedMemberId > 0 &&
                +decryptedRequestId > 0 &&
                +memberData.id === +decryptedMemberId
            ) {
                res.status(status.OK).json({
                    message: 'success',
                    data: { decryptedRequestId },
                });
            } else {
                res.status(status.OK).json({
                    message: 'success',
                    data: null,
                });
            }
        } catch (e) {
            next(e);
        }
    },
    async setReadAllNotification(req, res, next) {
        try {
            await inspectionService.ReadAllnotification(req, (response, error) => {
                if (error) {
                    next(error);
                } else {
                    res.status(status.CREATED).json({
                        message: 'Notification Read Successfully.',
                        data: response,
                    });
                }
            });
        } catch (e) {
            next(e);
        }
    },
    async markAllNotification(req, res, next) {
        try {
            const notificationRead = await inspectionService.Markallnotification(req);
            if (notificationRead.status === 200) {
                res.status(status.OK).json({
                    status: 200,
                    message: 'Notification Read Successfully.',
                    data: notificationRead.data,
                });
            } else {
                res.status(status.UNPROCESSABLE_ENTITY).json({
                    message: 'cannot Read Notification',
                    data: [],
                });
            }
        } catch (e) {
            next(e);
        }
    },
};

module.exports = inspectionController;
