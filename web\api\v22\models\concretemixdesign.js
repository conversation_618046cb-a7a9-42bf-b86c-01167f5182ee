module.exports = (sequelize, DataTypes) => {
  const ConcreteMixDesign = sequelize.define(
    'ConcreteMixDesign',
    {
      mixDesign: DataTypes.STRING,
      mixDesignAutoId: DataTypes.INTEGER,
      createdBy: {
        type: DataTypes.INTEGER,
        references: {
          model: 'Users', // name of Target model
          key: 'id', // key in Target model that we're referencing
        },
      },
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ProjectId: {
        type: DataTypes.INTEGER,
      },
      isDeleted: DataTypes.BOOLEAN,
    },
    {},
  );
  ConcreteMixDesign.associate = (models) => {
    ConcreteMixDesign.belongsTo(models.User, {
      as: 'userDetails',
      foreignKey: 'createdBy',
    });

    ConcreteMixDesign.belongsTo(models.Project);

    return ConcreteMixDesign;
  };
  ConcreteMixDesign.getConcreteMixDesigns = async (attr) => {
    const mixDesgins = await ConcreteMixDesign.findOne({
      include: [
        { association: 'userDetails', attributes: { exclude: ['password', 'resetPasswordToken'] } },
        'Project',
      ],
      where: { ...attr },
      order: [['id', 'DESC']],
    });
    return mixDesgins;
  };
  ConcreteMixDesign.getAll = async (attr, limit, offset, searchCondition, sort, sortColumn) => {
    let mixDesgin;
    const sortByFieldName = sortColumn || 'id';
    const sortByColumnType = sort || 'DESC';
    if (limit === 0) {
      mixDesgin = await ConcreteMixDesign.findAll({
        where: { ...attr },
        order: [[`${sortByFieldName}`, `${sortByColumnType}`]],
      });
    } else {
      mixDesgin = await ConcreteMixDesign.findAndCountAll({
        where: { ...attr, ...searchCondition },
        attributes: ['mixDesgin', 'id', 'mixDesginAutoId'],
        limit,
        offset,
        order: [[`${sortByFieldName}`, `${sortByColumnType}`]],
      });
    }
    return mixDesgin;
  };
  ConcreteMixDesign.updateInstance = async (id, args) => {
    const mixDesgin = await ConcreteMixDesign.update(args, { where: { id } });
    return mixDesgin;
  };

  ConcreteMixDesign.createConcreteMixDesign = async (mixDesginData) => {
    const newConcreteMixDesignData = await ConcreteMixDesign.create(mixDesginData);
    return newConcreteMixDesignData;
  };
  return ConcreteMixDesign;
};
