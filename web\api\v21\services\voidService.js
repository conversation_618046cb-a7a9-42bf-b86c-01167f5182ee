const { Sequelize, Enterprise } = require('../models');
let { <PERSON><PERSON><PERSON><PERSON>, Member, User, DeliveryRequest } = require('../models');
let {
  CraneRequest,
  ConcreteRequest,
  DeliverHistory,
  CraneRequestHistory,
  ConcreteRequestHistory,
  InspectionRequest,
  InspectionHistory
} = require('../models');
const helper = require('../helpers/domainHelper');
const craneRequestService = require('./craneRequestService');
const concreteRequestService = require('./concreteRequestService');

const { Op } = Sequelize;
let publicUser;
let publicMember;
const voidService = {
  // Method to inject dependencies for testing
  _setModelsForTesting(models) {
    if (models.VoidList) VoidList = models.VoidList;
    if (models.Member) Member = models.Member;
    if (models.User) User = models.User;
    if (models.DeliveryRequest) DeliveryRequest = models.DeliveryRequest;
    if (models.CraneRequest) CraneRequest = models.CraneRequest;
    if (models.ConcreteRequest) ConcreteRequest = models.ConcreteRequest;
    if (models.DeliverHistory) DeliverHistory = models.DeliverHistory;
    if (models.CraneRequestHistory) CraneRequestHistory = models.CraneRequestHistory;
    if (models.ConcreteRequestHistory) ConcreteRequestHistory = models.ConcreteRequestHistory;
    if (models.InspectionRequest) InspectionRequest = models.InspectionRequest;
    if (models.InspectionHistory) InspectionHistory = models.InspectionHistory;
  },

  async createVoidList(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const voidData = inputData.body;
      const loginUser = inputData.user;
      const memberData = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: voidData.ProjectId,
          isDeleted: false,
        }),
      });
      if (memberData) {
        const newDeliveryRequest = await DeliveryRequest.findOne({
          where: { id: voidData.DeliveryRequestId },
        });
        if (newDeliveryRequest) {
          voidData.MemberId = memberData.id;
          const existVoid = await VoidList.findOne({
            where: Sequelize.and({
              DeliveryRequestId: voidData.DeliveryRequestId,
            }),
          });
          if (!existVoid) {
            const newVoidData = await VoidList.createInstance(voidData);
            if (newVoidData) {
              const object = {
                ProjectId: voidData.ProjectId,
                MemberId: memberData.id,
                DeliveryRequestId: newDeliveryRequest.id,
                isDeleted: false,
                type: 'void',
                description: `${loginUser.firstName} ${loginUser.lastName} Voided the Delivery Booking, ${newDeliveryRequest.description}`,
              };
              await DeliverHistory.createInstance(object);
            }
            done(newVoidData, false);
          } else {
            done(null, { message: 'Delivery Booking already is in void list.' });
          }
        } else {
          done(null, { message: 'Delivery Booking Id Does not exist.' });
        }
      } else {
        done(null, { message: 'Project Id/Member Does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },

  async createInspectionVoidList(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const voidData = inputData.body;
      const loginUser = inputData.user;
      const memberData = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: voidData.ProjectId,
          isDeleted: false,
        }),
      });
      if (memberData) {
        const newDeliveryRequest = await InspectionRequest.findOne({
          where: { id: voidData.InspectionRequestId },
        });
        if (newDeliveryRequest) {
          voidData.MemberId = memberData.id;
          const existVoid = await VoidList.findOne({
            where: Sequelize.and({
              InspectionRequestId: voidData.InspectionRequestId,
            }),
          });
          if (!existVoid) {
            const newVoidData = await VoidList.createInstance(voidData);
            if (newVoidData) {
              const object = {
                ProjectId: voidData.ProjectId,
                MemberId: memberData.id,
                InspectionRequestId: newDeliveryRequest.id,
                isDeleted: false,
                type: 'void',
                description: `${loginUser.firstName} ${loginUser.lastName} Voided the Inspection Booking, ${newDeliveryRequest.description}`,
              };
              await InspectionHistory.createInstance(object);
            }
            done(newVoidData, false);
          } else {
            done(null, { message: 'Inspection Booking already is in void list.' });
          }
        } else {
          done(null, { message: 'Inspection Booking Id Does not exist.' });
        }
      } else {
        done(null, { message: 'Project Id/Member Does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    const { domainName: initialDomainName } = inputData.user;
    const ParentCompanyId = this.getParentCompanyId(inputData);

    const domainName = await this.resolveDomainName(initialDomainName, ParentCompanyId, inputData);
    const modelObj = await helper.getDynamicModel(domainName);

    this.updateModelReferences(modelObj);
    await this.updateUserIfNeeded(inputData, domainName);

    return null; // ProjectId was never used
  },

  getParentCompanyId(inputData) {
    return inputData.body.ParentCompanyId || inputData.params.ParentCompanyId;
  },

  async resolveDomainName(initialDomainName, ParentCompanyId, inputData) {
    if (initialDomainName) {
      const domainEnterpriseValue = await Enterprise.findOne({
        where: { name: initialDomainName.toLowerCase() },
      });
      if (domainEnterpriseValue) {
        return initialDomainName.toLowerCase();
      }
    }

    if (!initialDomainName && this.isValidParentCompanyId(ParentCompanyId)) {
      return await this.getDomainFromParentCompany(ParentCompanyId, inputData);
    }

    return '';
  },

  isValidParentCompanyId(ParentCompanyId) {
    return ParentCompanyId !== undefined && ParentCompanyId !== 'undefined';
  },

  async getDomainFromParentCompany(ParentCompanyId, inputData) {
    const userData = await this.findUserByEmail(inputData.user.email);
    if (!userData) {
      return await this.getDomainFromEnterprise(ParentCompanyId);
    }

    const memberData = await this.findMemberByUserId(userData.id);
    if (!memberData) {
      return await this.getDomainFromEnterprise(ParentCompanyId);
    }

    if (memberData.isAccount) {
      return await this.getDomainFromMemberEnterprise(memberData);
    }

    return await this.getDomainFromEnterprise(ParentCompanyId);
  },

  async findUserByEmail(email) {
    if (!email) return null;
    return publicUser.findOne({ where: { email } });
  },

  async findMemberByUserId(userId) {
    return publicMember.findOne({
      where: {
        UserId: userId,
        RoleId: { [Op.ne]: 4 },
        isDeleted: false
      },
    });
  },

  async getDomainFromMemberEnterprise(memberData) {
    const enterpriseValue = await Enterprise.findOne({
      where: {
        id: memberData.EnterpriseId,
        status: 'completed'
      },
    });
    return enterpriseValue ? enterpriseValue.name.toLowerCase() : '';
  },

  async getDomainFromEnterprise(ParentCompanyId) {
    const enterpriseValue = await Enterprise.findOne({
      where: {
        ParentCompanyId,
        status: 'completed'
      },
    });
    return enterpriseValue ? enterpriseValue.name.toLowerCase() : '';
  },

  updateModelReferences(modelObj) {
    VoidList = modelObj.VoidList;
    Member = modelObj.Member;
    DeliveryRequest = modelObj.DeliveryRequest;
    User = modelObj.User;
  },

  async updateUserIfNeeded(inputData, domainName) {
    if (domainName) {
      const newUser = await User.findOne({
        where: { email: inputData.user.email }
      });
      if (newUser) {
        inputData.user = newUser;
      }
    }
  },
  async removeVoidList(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { reqData, loginUser } = inputData;

      const memberData = await this.findMember(loginUser.id, reqData.ProjectId);
      if (!memberData) {
        return done(null, { message: 'Void not available' });
      }

      if (reqData.isSelectAll) {
        await this.handleSelectAllRemoval(reqData, memberData, loginUser, done);
      } else {
        await this.handleSelectiveRemoval(reqData, memberData, loginUser, done);
      }
    } catch (e) {
      done(null, e);
    }
  },

  async handleSelectAllRemoval(reqData, memberData, loginUser, done) {
    const voidedList = await this.findVoidedList(reqData.ProjectId);
    if (voidedList?.length > 0) {
      await this.processVoidedList(voidedList, memberData, loginUser);
    }

    const removeData = await VoidList.destroy({
      where: { ProjectId: reqData.ProjectId },
    });
    done(removeData, false);
  },

  async findVoidedList(projectId) {
    return VoidList.findAll({
      where: {
        ProjectId: projectId,
        isDeleted: false,
      },
    });
  },

  async processVoidedList(voidedList, memberData, loginUser) {
    for (const element of voidedList) {
      await this.processVoidedElement(element, memberData, loginUser);
    }
  },

  async processVoidedElement(element, memberData, loginUser) {
    if (this.isDeliveryRequest(element)) {
      await this.deliveryRequestVoidHistory(element, memberData, loginUser);
    }
    if (this.isCraneRequest(element)) {
      await this.craneRequestVoidHistory(element, memberData, loginUser);
    }
    if (this.isConcreteRequest(element)) {
      await this.concreteRequestVoidHistory(element, memberData, loginUser);
    }
  },

  isDeliveryRequest(element) {
    return element.DeliveryRequestId && !element.CraneRequestId && !element.ConcreteRequestId;
  },

  isCraneRequest(element) {
    return !element.DeliveryRequestId && element.CraneRequestId && !element.ConcreteRequestId;
  },

  isConcreteRequest(element) {
    return !element.DeliveryRequestId && !element.CraneRequestId && element.ConcreteRequestId;
  },

  async handleSelectiveRemoval(reqData, memberData, loginUser, done) {
    const existVoid = await this.findVoidById(reqData.id);
    if (!existVoid) {
      return done(null, { message: 'Some Void not available' });
    }

    await this.processExistingVoid(existVoid, memberData, loginUser);

    const removeData = await VoidList.destroy({
      where: { id: { [Op.in]: reqData.id } },
    });
    done(removeData, false);
  },

  async findVoidById(id) {
    return VoidList.findOne({
      where: { id: { [Op.in]: id } },
    });
  },

  async processExistingVoid(existVoid, memberData, loginUser) {
    if (existVoid?.CraneRequestId) {
      await this.craneRequestVoidHistory(existVoid, memberData, loginUser);
    }
    if (existVoid?.ConcreteRequestId) {
      await this.concreteRequestVoidHistory(existVoid, memberData, loginUser);
    }
    if (existVoid?.DeliveryRequestId) {
      await this.deliveryRequestVoidHistory(existVoid, memberData, loginUser);
    }
  },

  async createCraneRequestVoid(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const voidData = inputData.body;
      voidData.isDeliveryRequest = false;
      const loginUser = inputData.user;
      const memberData = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: voidData.ProjectId,
          isDeleted: false,
        }),
      });
      if (memberData) {
        const newCraneRequest = await CraneRequest.findOne({
          where: { id: voidData.CraneRequestId },
        });
        if (newCraneRequest) {
          voidData.MemberId = memberData.id;
          const existVoid = await VoidList.findOne({
            where: Sequelize.and({
              CraneRequestId: voidData.CraneRequestId,
            }),
          });
          if (!existVoid) {
            const newVoidData = await VoidList.createInstance(voidData);
            if (newVoidData) {
              const object = {
                ProjectId: voidData.ProjectId,
                MemberId: memberData.id,
                CraneRequestId: newCraneRequest.id,
                isDeleted: false,
                type: 'void',
                description: `${loginUser.firstName} ${loginUser.lastName} Voided the Crane Booking, ${newCraneRequest.description}`,
              };
              await CraneRequestHistory.createInstance(object);
            }
            done(newVoidData, false);
          } else {
            done(null, { message: 'Crane Booking already is in void list.' });
          }
        } else {
          done(null, { message: 'Crane Booking Id Does not exist.' });
        }
      } else {
        done(null, { message: 'Project Id/Member Does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createConcreteRequestVoid(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const voidData = inputData.body;
      voidData.isDeliveryRequest = false;
      const loginUser = inputData.user;
      const memberData = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: voidData.ProjectId,
          isDeleted: false,
        }),
      });
      if (memberData) {
        const newConcreteRequest = await ConcreteRequest.findOne({
          where: { id: voidData.ConcreteRequestId, isDeleted: false },
        });
        if (newConcreteRequest) {
          voidData.MemberId = memberData.id;
          const existVoid = await VoidList.findOne({
            where: Sequelize.and({
              ConcreteRequestId: voidData.ConcreteRequestId,
            }),
          });
          if (!existVoid) {
            const newVoidData = await VoidList.createInstance(voidData);
            if (newVoidData) {
              const object = {
                ProjectId: voidData.ProjectId,
                MemberId: memberData.id,
                ConcreteRequestId: newConcreteRequest.id,
                isDeleted: false,
                type: 'void',
                description: `${loginUser.firstName} ${loginUser.lastName} Voided the Concrete Booking, ${newConcreteRequest.description}`,
              };
              await ConcreteRequestHistory.createInstance(object);
            }
            done(newVoidData, false);
          } else {
            done(null, { message: 'Concrete Booking already is in void list.' });
          }
        } else {
          done(null, { message: 'Concrete Booking Id Does not exist.' });
        }
      } else {
        done(null, { message: 'Project Id/Member Does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getVoidList(req, done) {
    try {
      const { params } = req;
      await this.getDynamicModel(req);
      const offset = (+params.pageNo - 1) * +params.pageSize;
      if (
        req.body.statusFilter === 'Tentative' ||
        req.body.statusFilter === 'Pump Confirmed' ||
        req.body.locationFilter ||
        req.body.mixDesignFilter
      ) {
        req.body.concreteSupplierFilter = req.body.companyFilter;
        console.log("First condition=====")
        await concreteRequestService.listConcreteRequest(req, async (response1, error2) => {
          if (!error2) {
            const voidlist = [];
            voidlist.push(...response1.rows);
            done({ count: voidlist.length, rows: voidlist.slice(offset, offset + +params.pageSize) }, false);
          }
        });
      } else {

        console.log("else part=========")
        await craneRequestService.getVoidRequest(req, async (response, error1) => {
          if (!error1) {
            const voidlist = [];
            voidlist.push(...response);
            if (
              +req.body.filterCount > 0 &&
              (req.body.gateFilter ||
                req.body.equipmentFilter ||
                req.body.statusFilter === 'Declined' ||
                req.body.status === 'Delivered' ||
                req.body.status === 'Pending')
            ) {
              done({ count: voidlist.length, rows: voidlist.slice(offset, offset + +params.pageSize) }, false);
            } else {
              req.body.concreteSupplierFilter = req.body.companyFilter;
              await concreteRequestService.listConcreteRequest(req, async (response1, error2) => {
                if (!error2) {
                  voidlist.push(...response1.rows);
                  if (req.body.sort === 'ASC') {
                    voidlist.sort(function (a, b) {
                      const fieldA = a[req.body.sortByField];
                      const fieldB = b[req.body.sortByField];

                      if (fieldA > fieldB) return 1;
                      if (fieldB > fieldA) return -1;
                      return 0;
                    });
                  } else {
                    voidlist.sort(function (a, b) {
                      const fieldA = a[req.body.sortByField];
                      const fieldB = b[req.body.sortByField];

                      if (fieldB > fieldA) return 1;
                      if (fieldA > fieldB) return -1;
                      return 0;
                    });
                  }
                  done({ count: voidlist.length, rows: voidlist.slice(offset, offset + +params.pageSize) }, false);
                }
              });
            }
          }
        });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createMultipleVoidList(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { voidData, loginUser } = inputData;
      const memberData = await this.findMember(loginUser.id, voidData.ProjectId);

      if (!memberData) {
        return done(null, { message: 'Project Id/Member Does not exist.' });
      }

      for (const deliveryRequestId of voidData.deliveryRequestIds) {
        if (!deliveryRequestId) continue;

        const result = await this.processDeliveryRequest(deliveryRequestId, voidData, memberData);
        if (result.error) {
          return done(null, { message: result.error });
        }
      }
    } catch (e) {
      done(null, e);
    }
  },

  async findMember(userId, projectId) {
    return Member.findOne({
      where: Sequelize.and({
        UserId: userId,
        ProjectId: projectId,
        isDeleted: false,
      }),
    });
  },

  async processDeliveryRequest(deliveryRequestId, voidData, memberData) {
    const newDeliveryRequest = await DeliveryRequest.findOne({
      where: { id: deliveryRequestId },
    });

    if (!newDeliveryRequest) {
      return { error: 'Delivery Booking Id Does not exist.' };
    }

    const existVoid = await VoidList.findOne({
      where: Sequelize.and({
        DeliveryRequestId: deliveryRequestId,
      }),
    });

    if (existVoid) {
      return { error: 'Delivery Booking already is in void list.' };
    }

    const voidcreate = {
      DeliveryRequestId: deliveryRequestId,
      ProjectId: voidData.ProjectId,
      ParentCompanyId: voidData.ParentCompanyId,
    };

    await VoidList.createInstance(voidcreate);
    return { success: true };
  },
  async deliveryRequestVoidHistory(existVoid, memberData, loginUser) {
    const deliveryRequest = await DeliveryRequest.findOne({
      where: {
        id: existVoid.DeliveryRequestId,
      },
    });
    const object = {
      ProjectId: existVoid.ProjectId,
      MemberId: memberData.id,
      DeliveryRequestId: existVoid.DeliveryRequestId,
      isDeleted: false,
      type: 'restore',
      description: `${loginUser.firstName} ${loginUser.lastName} Restored the Delivery Booking, ${deliveryRequest.description}`,
    };
    await DeliverHistory.createInstance(object);
  },
  async craneRequestVoidHistory(existVoid, memberData, loginUser) {
    const craneRequest = await CraneRequest.findOne({
      where: {
        id: existVoid.CraneRequestId,
      },
    });
    const object = {
      ProjectId: existVoid.ProjectId,
      MemberId: memberData.id,
      CraneRequestId: existVoid.CraneRequestId,
      isDeleted: false,
      type: 'restore',
      description: `${loginUser.firstName} ${loginUser.lastName} Restored the Crane Booking, ${craneRequest.description}`,
    };
    await CraneRequestHistory.createInstance(object);
  },
  async concreteRequestVoidHistory(existVoid, memberData, loginUser) {
    const concreteRequest = await ConcreteRequest.findOne({
      where: {
        id: existVoid.ConcreteRequestId,
      },
    });
    const object = {
      ProjectId: existVoid.ProjectId,
      MemberId: memberData.id,
      ConcreteRequestId: existVoid.ConcreteRequestId,
      isDeleted: false,
      type: 'restore',
      description: `${loginUser.firstName} ${loginUser.lastName} Restored the Concrete Booking, ${concreteRequest.description}`,
    };
    await ConcreteRequestHistory.createInstance(object);
  },
};
module.exports = voidService;
