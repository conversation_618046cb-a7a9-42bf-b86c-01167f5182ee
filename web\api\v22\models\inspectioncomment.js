module.exports = (sequelize, DataTypes) => {
  const InspectionComment = sequelize.define(
    'InspectionComment',
    {
      ProjectId: DataTypes.INTEGER,
      MemberId: DataTypes.INTEGER,
      InspectionRequestId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      isDeleted: DataTypes.BOOLEAN,
      comment: DataTypes.STRING,
    },
    {},
  );
  InspectionComment.associate = (models) => {
    // associations can be defined here
    InspectionComment.belongsTo(models.Member);
    InspectionComment.belongsTo(models.InspectionRequest);
  };

  InspectionComment.getAll = async (attr) => {
    const newInspectionComment = await InspectionComment.findAll({
      where: { ...attr },
    });
    return newInspectionComment;
  };
  InspectionComment.createInstance = async (paramData) => {
    const newInspectionComment = await InspectionComment.create(paramData);
    return newInspectionComment;
  };
  return InspectionComment;
};
