/* eslint-disable no-restricted-syntax */
/* eslint-disable no-loop-func */
/* eslint-disable no-await-in-loop */
/* eslint-disable no-new */
const moment = require('moment');
const Moment = require('moment');
const momenttz = require('moment-timezone');
const MomentRange = require('moment-range');
const httpStatus = require('http-status');

const momentRange = MomentRange.extendMoment(Moment);
const ExcelJS = require('exceljs');
const { Worker } = require('worker_threads');
const { stringify } = require('flatted');
const path = require('path');
const Cryptr = require('cryptr');
const MAILER = require('../mailer');

const bulkNdrProcess = path.join(__dirname, './bulkNdrProcess.js');
const ApiError = require('../helpers/apiError');
const {
  Sequelize,
  Enterprise,
  NotificationPreference,
  NotificationPreferenceItem,
  DigestNotification,
  TimeZone,
  CraneRequest,
  ProjectSettings,
  RequestRecurrenceSeries,
  CalendarSetting,
  LocationNotificationPreferences,
  Locations,
  EquipmentMapping
} = require('../models');
let {
  DeliveryRequest,
  Member,
  DeliveryPerson,
  DeliverGate,
  DeliverEquipment,
  DeliverCompany,
  Role,
  Gates,
  Equipments,
  DeliverDefineWork,
  Company,
  Project,
  DeliverDefine,
  DeliverHistory,
  DeliveryPersonNotification,
  VoidList,
  User,
  Notification,
} = require('../models');
const helper = require('../helpers/domainHelper');
const notificationHelper = require('../helpers/notificationHelper');
const pushNotification = require('../config/fcm');
const calendarSettingsService = require('./calendarSettingsService');
const voidService = require('./voidService');
const concreteRequestService = require('./concreteRequestService');

let publicUser;
let publicMember;
const { Op } = Sequelize;

const deliveryService = {
  // Helper function to validate timezone
  async validateTimeZone(timeZoneId) {
    const eventTimeZone = await TimeZone.findOne({
      where: {
        isDeleted: false,
        id: +timeZoneId,
      },
      attributes: [
        'id',
        'location',
        'isDayLightSavingEnabled',
        'timeZoneOffsetInMinutes',
        'dayLightSavingTimeInMinutes',
        'timezone',
      ],
    });
    return eventTimeZone;
  },

  // Helper function to validate delivery times
  validateDeliveryTimes(deliveryData) {
    if (deliveryData.startPicker === deliveryData.endPicker) {
      return { error: true, message: 'Delivery Start time and End time should not be the same' };
    }
    if (deliveryData.startPicker > deliveryData.endPicker) {
      return { error: true, message: 'Please enter From Time lesser than To Time' };
    }
    return { error: false };
  },

  // Helper function to validate delivery window dates
  async validateDeliveryWindowDates(deliveryData, eventTimeZone, projectDetails) {
    let startDate;
    let endDate;
    if (deliveryData.recurrence) {
      startDate = await this.compareDeliveryDateWithDeliveryWindowDate(
        deliveryData.deliveryStart,
        deliveryData.startPicker,
        eventTimeZone.timezone,
        projectDetails.ProjectSettings.deliveryWindowTime,
        projectDetails.ProjectSettings.deliveryWindowTimeUnit,
      );
      endDate = await this.compareDeliveryDateWithDeliveryWindowDate(
        deliveryData.deliveryEnd,
        deliveryData.endPicker,
        eventTimeZone.timezone,
        projectDetails.ProjectSettings.deliveryWindowTime,
        projectDetails.ProjectSettings.deliveryWindowTimeUnit,
      );
    }

    if (startDate || endDate) {
      if (projectDetails.ProjectSettings.deliveryWindowTime === 0) {
        if (deliveryData.recurrence === 'Does Not Repeat') {
          return { error: true, message: 'Please enter Future Date/Time' };
        }
        return { error: true, message: 'Please enter Future Start or End Date/Time' };
      }
      return {
        error: true,
        message: `Bookings can not be submitted within ${projectDetails.ProjectSettings.deliveryWindowTime} ${projectDetails.ProjectSettings.deliveryWindowTimeUnit} prior to the event`,
      };
    }
    return { error: false };
  },

  // Helper function to get next delivery ID
  async getNextDeliveryId(projectId) {
    // Validate projectId to prevent NaN database errors
    const validProjectId = parseInt(projectId);
    if (isNaN(validProjectId)) {
      throw new Error(`Invalid projectId: ${projectId}`);
    }

    const lastIdValue = await DeliveryRequest.findOne({
      where: { ProjectId: validProjectId, isDeleted: false },
      order: [['DeliveryId', 'DESC']],
    });
    let id = 0;
    const newValue = JSON.parse(JSON.stringify(lastIdValue));
    if (newValue && newValue.DeliveryId !== null && newValue.DeliveryId !== undefined) {
      const lastDeliveryId = parseInt(newValue.DeliveryId);
      if (!isNaN(lastDeliveryId)) {
        id = lastDeliveryId;
      }
    }
    return id;
  },

  // Helper function to get next crane request ID for delivery
  async getNextCraneRequestIdForDelivery(projectId) {
    // Validate projectId to prevent NaN database errors
    const validProjectId = parseInt(projectId);
    if (isNaN(validProjectId)) {
      throw new Error(`Invalid projectId: ${projectId}`);
    }

    let lastData = {};
    lastData = await CraneRequest.findOne({
      where: { ProjectId: validProjectId, isDeleted: false },
      order: [['CraneRequestId', 'DESC']],
    });
    const deliveryRequestList = await DeliveryRequest.findOne({
      where: {
        ProjectId: validProjectId,
        isDeleted: false,
        isAssociatedWithCraneRequest: true,
      },
      order: [['CraneRequestId', 'DESC']],
    });
    if (deliveryRequestList) {
      if (lastData) {
        if (deliveryRequestList.CraneRequestId > lastData.CraneRequestId) {
          lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
        }
      } else {
        lastData = {};
        lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
      }
    }
    if (lastData) {
      const data = lastData.CraneRequestId;
      lastData.CraneRequestId = 0;
      lastData.CraneRequestId = data + 1;
    } else {
      lastData = {};
      lastData.CraneRequestId = 1;
    }
    let craneId = 0;
    const newId = JSON.parse(JSON.stringify(lastData));
    if (newId && newId.CraneRequestId !== null && newId.CraneRequestId !== undefined) {
      const validCraneId = parseInt(newId.CraneRequestId);
      if (!isNaN(validCraneId)) {
        craneId = validCraneId;
      }
    }
    return craneId;
  },

  // Helper function to create delivery parameters
  createDeliveryParam(deliveryData, id, deliveryStart, deliveryEnd, memberDetails, craneId, recurrenceId) {
    // Validate integer fields to prevent NaN database errors
    const validDeliveryId = parseInt(id);
    const validProjectId = parseInt(deliveryData.ProjectId);
    const validCreatedBy = parseInt(memberDetails.id);
    const validLocationId = parseInt(deliveryData.LocationId);
    const validRecurrenceId = parseInt(recurrenceId);
    const validCraneId = parseInt(craneId);

    if (isNaN(validDeliveryId)) {
      throw new Error(`Invalid DeliveryId: ${id}`);
    }
    if (isNaN(validProjectId)) {
      throw new Error(`Invalid ProjectId: ${deliveryData.ProjectId}`);
    }
    if (isNaN(validCreatedBy)) {
      throw new Error(`Invalid createdBy (memberDetails.id): ${memberDetails.id}`);
    }
    if (isNaN(validLocationId)) {
      throw new Error(`Invalid LocationId: ${deliveryData.LocationId}`);
    }
    if (isNaN(validRecurrenceId)) {
      throw new Error(`Invalid recurrenceId: ${recurrenceId}`);
    }

    return {
      description: deliveryData.description,
      escort: deliveryData.escort,
      vehicleDetails: deliveryData.vehicleDetails,
      notes: deliveryData.notes,
      DeliveryId: validDeliveryId,
      deliveryStart,
      deliveryEnd,
      ProjectId: validProjectId,
      createdBy: validCreatedBy,
      isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
      requestType: deliveryData.requestType,
      cranePickUpLocation: deliveryData.cranePickUpLocation,
      craneDropOffLocation: deliveryData.craneDropOffLocation,
      CraneRequestId: deliveryData.requestType === 'deliveryRequestWithCrane' ? (isNaN(validCraneId) ? null : validCraneId) : null,
      recurrenceId: validRecurrenceId,
      LocationId: validLocationId,
      OriginationAddress: deliveryData.originationAddress,
      vehicleType: deliveryData.vehicleType,
    };
  },

  // Helper function to set approval status for delivery
  setDeliveryApprovalStatus(deliveryParam, memberDetails, roleDetails, accountRoleDetails, projectDetails) {
    if (
      memberDetails.RoleId === roleDetails.id ||
      memberDetails.RoleId === accountRoleDetails.id ||
      memberDetails.isAutoApproveEnabled ||
      projectDetails.ProjectSettings.isAutoApprovalEnabled
    ) {
      deliveryParam.status = 'Approved';
      deliveryParam.approvedBy = memberDetails.id;
      deliveryParam.approved_at = new Date();
    }
    return deliveryParam;
  },

  // Helper function to process daily delivery recurrence
  async processDailyDeliveryRecurrence(params) {
    const { deliveryData, user, eventTimeZone, totalDays, startId, startCraneId, memberDetails, roleDetails, accountRoleDetails, projectDetails } = params;
    const startTime = deliveryData.startPicker;
    const endTime = deliveryData.endPicker;
    let dailyIndex = 0;
    let id = startId;
    let craneId = startCraneId;
    const eventsArray = [];

    const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
      deliveryData,
      user,
      deliveryData.requestType,
      eventTimeZone.timezone,
    );

    while (dailyIndex < totalDays.length) {
      const data = totalDays[dailyIndex];
      if (
        moment(data).isBetween(
          moment(deliveryData.deliveryStart),
          moment(deliveryData.deliveryEnd),
          null,
          '[]',
        ) ||
        moment(data).isSame(deliveryData.deliveryStart) ||
        moment(data).isSame(deliveryData.deliveryEnd)
      ) {
        id += 1;
        craneId += 1;
        const date = moment(data).format('MM/DD/YYYY');
        const chosenTimezoneDeliveryStart = moment.tz(
          `${date} ${startTime}`,
          'MM/DD/YYYY HH:mm',
          eventTimeZone.timezone,
        );
        const chosenTimezoneDeliveryEnd = moment.tz(
          `${date} ${endTime}`,
          'MM/DD/YYYY HH:mm',
          eventTimeZone.timezone,
        );
        const deliveryStart = chosenTimezoneDeliveryStart
          .clone()
          .tz('UTC')
          .format('YYYY-MM-DD HH:mm:ssZ');
        const deliveryEnd = chosenTimezoneDeliveryEnd
          .clone()
          .tz('UTC')
          .format('YYYY-MM-DD HH:mm:ssZ');

        let deliveryParam = this.createDeliveryParam(
          deliveryData,
          id,
          deliveryStart,
          deliveryEnd,
          memberDetails,
          craneId,
          recurrenceId
        );

        deliveryParam = this.setDeliveryApprovalStatus(
          deliveryParam,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectDetails
        );

        eventsArray.push(deliveryParam);
        dailyIndex += +deliveryData.repeatEveryCount;
      }
    }

    if (eventsArray && eventsArray.length > 0) {
      const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
        eventsArray,
        projectDetails,
        'add',
        deliveryData.GateId,
      );
      if (isOverlapping?.error) {
        return {
          error: true,
          message: isOverlapping.message,
        };
      }
    }

    return {
      error: false,
      eventsArray,
      lastId: id,
      lastCraneId: craneId
    };
  },

  // Helper function to process weekly delivery recurrence
  async generateWeeklyDeliveryEvents(params, recurrenceId) {
    const {
      deliveryData,
      eventTimeZone,
      startId,
      startCraneId,
      memberDetails,
      roleDetails,
      accountRoleDetails,
      projectDetails
    } = params;

    const startTime = deliveryData.startPicker;
    const endTime = deliveryData.endPicker;
    const startDayWeek = moment(deliveryData.deliveryStart).startOf('week');
    const endDayWeek = moment(deliveryData.deliveryEnd).endOf('week');
    const range1 = momentRange.range(moment(startDayWeek), moment(endDayWeek));
    const totalDaysOfRecurrence = Array.from(range1.by('day'));
    let id = startId;
    let craneId = startCraneId;
    const eventsArray = [];

    let count;
    let weekIncrement;
    if (+deliveryData.repeatEveryCount > 1) {
      count = +deliveryData.repeatEveryCount - 1;
      weekIncrement = 7;
    } else {
      count = 1;
      weekIncrement = 0;
    }

    for (let indexba = 0; indexba < totalDaysOfRecurrence.length; indexba += weekIncrement * count) {
      const totalLength = indexba + 6;
      for (let indexb = indexba; indexb <= totalLength; indexb += 1) {
        const data = totalDaysOfRecurrence[indexb];
        if (
          data &&
          !moment(data).isBefore(deliveryData.deliveryStart) &&
          !moment(data).isAfter(deliveryData.deliveryEnd)
        ) {
          const day = moment(data).format('dddd');
          const indexVal = deliveryData.days.includes(day);
          if (indexVal) {
            id += 1;
            craneId += 1;
            const date = moment(data).format('MM/DD/YYYY');
            const chosenTimezoneDeliveryStart = moment.tz(
              `${date} ${startTime}`,
              'MM/DD/YYYY HH:mm',
              eventTimeZone.timezone,
            );
            const chosenTimezoneDeliveryEnd = moment.tz(
              `${date} ${endTime}`,
              'MM/DD/YYYY HH:mm',
              eventTimeZone.timezone,
            );
            const deliveryStart = chosenTimezoneDeliveryStart
              .clone()
              .tz('UTC')
              .format('YYYY-MM-DD HH:mm:ssZ');
            const deliveryEnd = chosenTimezoneDeliveryEnd
              .clone()
              .tz('UTC')
              .format('YYYY-MM-DD HH:mm:ssZ');

            let deliveryParam = this.createDeliveryParam(
              deliveryData,
              id,
              deliveryStart,
              deliveryEnd,
              memberDetails,
              craneId,
              recurrenceId
            );

            deliveryParam = this.setDeliveryApprovalStatus(
              deliveryParam,
              memberDetails,
              roleDetails,
              accountRoleDetails,
              projectDetails
            );

            eventsArray.push(deliveryParam);
          }
        }
      }
    }

    return { eventsArray, lastId: id, lastCraneId: craneId };
  },

  async processWeeklyDeliveryRecurrence(params) {
    const { deliveryData, user, eventTimeZone, projectDetails } = params;

    const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
      deliveryData,
      user,
      deliveryData.requestType,
      eventTimeZone.timezone,
    );

    const { eventsArray, lastId, lastCraneId } = await this.generateWeeklyDeliveryEvents(
      params,
      recurrenceId
    );

    if (eventsArray && eventsArray.length > 0) {
      const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
        eventsArray,
        projectDetails,
        'add',
        deliveryData.GateId,
      );
      if (isOverlapping?.error) {
        return {
          error: true,
          message: isOverlapping.message,
        };
      }
    }

    return {
      error: false,
      eventsArray,
      lastId,
      lastCraneId
    };
  },


  // Helper function to process monthly delivery recurrence
  async processMonthlyDeliveryRecurrence(params) {
    const { deliveryData, user, eventTimeZone, startId, startCraneId, memberDetails, roleDetails, accountRoleDetails, projectDetails } = params;
    const startTime = deliveryData.startPicker;
    const endTime = deliveryData.endPicker;
    let id = startId;
    let craneId = startCraneId;
    const eventsArray = [];

    const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
      deliveryData,
      user,
      deliveryData.requestType,
      eventTimeZone.timezone,
    );

    let startDate1 = moment(deliveryData.deliveryStart);
    const endDate1 = moment(deliveryData.deliveryEnd).endOf('month');
    const allMonthsInPeriod = [];

    while (startDate1.isBefore(endDate1)) {
      allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
      startDate1 = startDate1.add(1, 'month');
    }

    let k = 0;
    while (k < allMonthsInPeriod.length + 1) {
      const currentMonthDates = Array.from(
        { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
        (_, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
      );

      if (deliveryData.chosenDateOfMonth) {
        const result = await this.processMonthlyDateDeliveryRecurrence({
          deliveryData,
          currentMonthDates,
          startTime,
          endTime,
          eventTimeZone,
          id,
          craneId,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectDetails,
          recurrenceId
        });
        if (result.deliveryParam) {
          eventsArray.push(result.deliveryParam);
          id = result.id;
          craneId = result.craneId;
        }
      } else if (allMonthsInPeriod[k]) {
        const result = await this.processMonthlyWeekdayDeliveryRecurrence({
          deliveryData,
          monthPeriod: allMonthsInPeriod[k],
          startTime,
          endTime,
          eventTimeZone,
          id,
          craneId,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectDetails,
          recurrenceId
        });
        if (result.deliveryParam) {
          eventsArray.push(result.deliveryParam);
          id = result.id;
          craneId = result.craneId;
        }
      }
      k += +deliveryData.repeatEveryCount;
    }

    if (eventsArray && eventsArray.length > 0) {
      const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
        eventsArray,
        projectDetails,
        'add',
        deliveryData.GateId,
      );
      if (isOverlapping?.error) {
        return {
          error: true,
          message: isOverlapping.message,
        };
      }
    }

    return {
      error: false,
      eventsArray,
      lastId: id,
      lastCraneId: craneId
    };
  },

  // Helper function to process monthly date delivery recurrence
  async processMonthlyDateDeliveryRecurrence(params) {
    let { deliveryData, currentMonthDates, startTime, endTime, eventTimeZone, id, craneId, memberDetails, roleDetails, accountRoleDetails, projectDetails, recurrenceId } = params;
    const getDate = currentMonthDates.filter(
      (value) => moment(value).format('DD') === deliveryData.dateOfMonth,
    );
    if (getDate.length === 1) {
      if (
        moment(getDate[0]).isBetween(
          moment(deliveryData.deliveryStart),
          moment(deliveryData.deliveryEnd),
          null,
          '[]',
        ) ||
        moment(getDate[0]).isSame(deliveryData.deliveryStart) ||
        moment(getDate[0]).isSame(deliveryData.deliveryEnd)
      ) {
        id += 1;
        craneId += 1;
        const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
        const chosenTimezoneDeliveryStart = moment.tz(
          `${date} ${startTime}`,
          'MM/DD/YYYY HH:mm',
          eventTimeZone.timezone,
        );
        const chosenTimezoneDeliveryEnd = moment.tz(
          `${date} ${endTime}`,
          'MM/DD/YYYY HH:mm',
          eventTimeZone.timezone,
        );
        const deliveryStart = chosenTimezoneDeliveryStart
          .clone()
          .tz('UTC')
          .format('YYYY-MM-DD HH:mm:ssZ');
        const deliveryEnd = chosenTimezoneDeliveryEnd
          .clone()
          .tz('UTC')
          .format('YYYY-MM-DD HH:mm:ssZ');

        let deliveryParam = this.createDeliveryParam(
          deliveryData,
          id,
          deliveryStart,
          deliveryEnd,
          memberDetails,
          craneId,
          recurrenceId
        );

        deliveryParam = this.setDeliveryApprovalStatus(
          deliveryParam,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectDetails
        );

        return { deliveryParam, id, craneId };
      }
    }
    return { deliveryParam: null, id, craneId };
  },

  // Helper function to process monthly weekday delivery recurrence
  async processMonthlyWeekdayDeliveryRecurrence(params) {
    let { deliveryData, monthPeriod, startTime, endTime, eventTimeZone, id, craneId, memberDetails, roleDetails, accountRoleDetails, projectDetails, recurrenceId } = params
    const dayOfMonth = deliveryData.monthlyRepeatType;
    const week = dayOfMonth.split(' ')[0].toLowerCase();
    const day = dayOfMonth.split(' ')[1].toLowerCase();
    const chosenDay = moment(monthPeriod, 'YYYY-MM')
      .startOf('month')
      .day(day);
    const getAllDays = [];
    if (chosenDay.date() > 7) chosenDay.add(7, 'd');
    const month = chosenDay.month();
    while (month === chosenDay.month()) {
      getAllDays.push(chosenDay.format('YYYY-MM-DD'));
      chosenDay.add(7, 'd');
    }
    let i = 0;
    if (week === 'second') {
      i += 1;
    } else if (week === 'third') {
      i += 2;
    } else if (week === 'fourth') {
      i += 3;
    } else if (week === 'last') {
      i = getAllDays.length - 1;
    }
    const finalDay = getAllDays[i];
    if (
      moment(finalDay).isBetween(
        moment(deliveryData.deliveryStart),
        moment(deliveryData.deliveryEnd),
        null,
        '[]',
      ) ||
      moment(finalDay).isSame(deliveryData.deliveryStart) ||
      moment(finalDay).isSame(deliveryData.deliveryEnd)
    ) {
      id += 1;
      craneId += 1;
      const date = moment(finalDay).format('MM/DD/YYYY');
      const chosenTimezoneDeliveryStart = moment.tz(
        `${date} ${startTime}`,
        'MM/DD/YYYY HH:mm',
        eventTimeZone.timezone,
      );
      const chosenTimezoneDeliveryEnd = moment.tz(
        `${date} ${endTime}`,
        'MM/DD/YYYY HH:mm',
        eventTimeZone.timezone,
      );
      const deliveryStart = chosenTimezoneDeliveryStart
        .clone()
        .tz('UTC')
        .format('YYYY-MM-DD HH:mm:ssZ');
      const deliveryEnd = chosenTimezoneDeliveryEnd
        .clone()
        .tz('UTC')
        .format('YYYY-MM-DD HH:mm:ssZ');

      let deliveryParam = this.createDeliveryParam(
        deliveryData,
        id,
        deliveryStart,
        deliveryEnd,
        memberDetails,
        craneId,
        recurrenceId
      );

      deliveryParam = this.setDeliveryApprovalStatus(
        deliveryParam,
        memberDetails,
        roleDetails,
        accountRoleDetails,
        projectDetails
      );

      return { deliveryParam, id, craneId };
    }
    return { deliveryParam: null, id, craneId };
  },

  // Helper function to process yearly delivery recurrence
  async processYearlyDeliveryRecurrence(params) {
    const { deliveryData, user, eventTimeZone, startId, startCraneId, memberDetails, roleDetails, accountRoleDetails, projectDetails } = params;
    const startTime = deliveryData.startPicker;
    const endTime = deliveryData.endPicker;
    let id = startId;
    let craneId = startCraneId;
    const eventsArray = [];

    const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
      deliveryData,
      user,
      deliveryData.requestType,
      eventTimeZone.timezone,
    );

    let startDate1 = moment(deliveryData.deliveryStart);
    const endDate1 = moment(deliveryData.deliveryEnd).endOf('month');
    const allMonthsInPeriod = [];

    while (startDate1.isBefore(endDate1)) {
      allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
      startDate1 = startDate1.add(12, 'month');
    }

    for (let k = 0; k < allMonthsInPeriod.length + 1; k += 1) {
      const currentMonthDates = Array.from(
        { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
        (_, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
      );

      if (deliveryData.chosenDateOfMonth) {
        const result = await this.processMonthlyDateDeliveryRecurrence({
          deliveryData,
          currentMonthDates,
          startTime,
          endTime,
          eventTimeZone,
          id,
          craneId,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectDetails,
          recurrenceId
        }
        );
        if (result.deliveryParam) {
          eventsArray.push(result.deliveryParam);
          id = result.id;
          craneId = result.craneId;
        }
      } else if (allMonthsInPeriod[k]) {
        const result = await this.processMonthlyWeekdayDeliveryRecurrence({
          deliveryData,
          monthPeriod: allMonthsInPeriod[k],
          startTime,
          endTime,
          eventTimeZone,
          id,
          craneId,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectDetails,
          recurrenceId
        }
        );
        if (result.deliveryParam) {
          eventsArray.push(result.deliveryParam);
          id = result.id;
          craneId = result.craneId;
        }
      }
    }

    if (eventsArray && eventsArray.length > 0) {
      const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
        eventsArray,
        projectDetails,
        'add',
        deliveryData.GateId,
      );
      if (isOverlapping?.error) {
        return {
          error: true,
          message: isOverlapping.message,
        };
      }
    }

    return {
      error: false,
      eventsArray,
      lastId: id,
      lastCraneId: craneId
    };
  },

  // Helper function to process no-repeat delivery
  async processNoRepeatDelivery(params) {
    const { deliveryData, user, eventTimeZone, startId, startCraneId, memberDetails, roleDetails, accountRoleDetails, projectDetails } = params;
    let id = startId + 1;
    let craneId = startCraneId + 1;
    const eventsArray = [];

    const chosenTimezoneDeliveryStart = moment.tz(
      `${deliveryData.deliveryStart} ${deliveryData.startPicker}`,
      'YYYY MM DD 00:00:00 HH:mm',
      eventTimeZone.timezone,
    );
    const chosenTimezoneDeliveryEnd = moment.tz(
      `${deliveryData.deliveryEnd} ${deliveryData.endPicker}`,
      'YYYY MM DD 00:00:00 HH:mm',
      eventTimeZone.timezone,
    );
    const deliveryStart = chosenTimezoneDeliveryStart
      .clone()
      .tz('UTC')
      .format('YYYY-MM-DD HH:mm:ssZ');
    const deliveryEnd = chosenTimezoneDeliveryEnd
      .clone()
      .tz('UTC')
      .format('YYYY-MM-DD HH:mm:ssZ');

    const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
      deliveryData,
      user,
      deliveryData.requestType,
      eventTimeZone.timezone,
    );

    let deliveryParam = this.createDeliveryParam(
      deliveryData,
      id,
      deliveryStart,
      deliveryEnd,
      memberDetails,
      craneId,
      recurrenceId
    );

    // Add GateId for no-repeat delivery
    deliveryParam.GateId = deliveryData.GateId;

    deliveryParam = this.setDeliveryApprovalStatus(
      deliveryParam,
      memberDetails,
      roleDetails,
      accountRoleDetails,
      projectDetails
    );

    eventsArray.push(deliveryParam);

    if (eventsArray && eventsArray.length > 0) {
      const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
        eventsArray,
        projectDetails,
        'add',
        deliveryData.GateId,
      );
      if (isOverlapping?.error) {
        return {
          error: true,
          message: isOverlapping.message,
        };
      }
    }

    return {
      error: false,
      eventsArray,
      lastId: id,
      lastCraneId: craneId
    };
  },

  async newRequest(inputData, done) {
    try {
      // Step 1: Validate and prepare data
      const { error, eventTimeZone, deliveryData, loginUser, projectDetails, memberDetails } =
        await this.validateAndPrepare(inputData);
      if (error) return done(null, { message: error });

      // Step 2: Process delivery recurrence and create events
      const { error: eventError, newDeliverData } =
        await this.processDeliveries(inputData, deliveryData, loginUser, eventTimeZone, projectDetails, memberDetails);
      if (eventError) return done(null, { message: eventError });

      // Step 3: Notifications and post-processing
      const result = await this.handleNotifications(
        newDeliverData,
        deliveryData,
        loginUser,
        memberDetails,
        projectDetails
      );
      return done(result, false);

    } catch (e) {
      return done(null, e);
    }
  },

  async validateAndPrepare(inputData) {
    await this.getDynamicModel(inputData);
    const eventTimeZone = await this.validateTimeZone(inputData.body.TimeZoneId);
    if (!eventTimeZone) return { error: 'Provide a valid timezone' };

    const deliveryData = inputData.body;
    const loginUser = inputData.user;

    const projectDetails = await Project.getProjectAndSettings({
      isDeleted: false,
      id: +deliveryData.ProjectId,
    });

    if (!projectDetails?.ProjectSettings) return { error: 'Project does not exist.' };

    const timeValidation = this.validateDeliveryTimes(deliveryData);
    if (timeValidation.error) return { error: timeValidation.message };

    const windowValidation = await this.validateDeliveryWindowDates(deliveryData, eventTimeZone, projectDetails);
    if (windowValidation.error) return { error: windowValidation.message };

    // Validate user membership
    const memberDetails = await Member.getBy({
      UserId: loginUser.id,
      ProjectId: deliveryData.ProjectId,
      isActive: true,
      isDeleted: false,
    });
    if (!memberDetails) return { error: 'You are not allowed to create Delivery Booking for this project.' };

    return { eventTimeZone, deliveryData, loginUser, projectDetails, memberDetails };
  },

  async processDeliveries(inputData, deliveryData, loginUser, eventTimeZone, projectDetails, memberDetails) {
    const eventsArray = [];
    let id = await this.getNextDeliveryId(memberDetails.ProjectId);
    let craneId = await this.getNextCraneRequestIdForDelivery(memberDetails.ProjectId);

    const roleDetails = await Role.getBy('Project Admin');
    const accountRoleDetails = await Role.getBy('Account Admin');

    const recurrenceHandlers = {
      Daily: this.processDailyDeliveryRecurrence,
      Weekly: this.processWeeklyDeliveryRecurrence,
      Monthly: this.processMonthlyDeliveryRecurrence,
      Yearly: this.processYearlyDeliveryRecurrence,
      'Does Not Repeat': this.processNoRepeatDelivery,
    };

    const recurrenceFn = recurrenceHandlers[deliveryData.recurrence];
    if (!recurrenceFn) return { error: 'Invalid recurrence type.' };

    const result = await recurrenceFn.call(this, {
      deliveryData,
      user: inputData.user,
      eventTimeZone,
      startId: id,
      startCraneId: craneId,
      memberDetails,
      roleDetails,
      accountRoleDetails,
      projectDetails,
    });

    if (result.error) return { error: result.message };

    eventsArray.push(...result.eventsArray);

    // Create delivery requests for events
    let newDeliverData = {};
    if (eventsArray.length > 0) {
      for (const event of eventsArray) {
        newDeliverData = await DeliveryRequest.createInstance(event);
        await this.mapEquipmentAndEntities(deliveryData, newDeliverData, memberDetails, loginUser);
      }
    } else {
      return { error: 'Bookings will not be created for the scheduled date/time' };
    }

    return { eventsArray, newDeliverData };
  },

  async mapEquipmentAndEntities(deliveryData, newDeliverData, memberDetails, loginUser) {
    const { companies, persons, define } = deliveryData;
    const gates = [deliveryData.GateId];
    const equipments = deliveryData.EquipmentId;
    const updateParam = {
      DeliveryId: newDeliverData.id,
      DeliveryCode: newDeliverData.DeliveryId,
      ProjectId: deliveryData.ProjectId,
      createdBy: memberDetails.id,
      isDeleted: false,
    };

    // Create company mappings
    for (const element of companies) {
      await DeliverCompany.createInstance({ ...updateParam, CompanyId: element });
    }

    // Create gate mappings
    for (const element of gates) {
      await DeliverGate.createInstance({ ...updateParam, GateId: element });
    }

    // Create equipment mappings
    for (const element of equipments) {
      await DeliverEquipment.createInstance({ ...updateParam, EquipmentId: element });
    }

    // Create person mappings
    for (const element of persons) {
      await DeliveryPerson.createInstance({ ...updateParam, MemberId: element });
    }

    // Create define work mappings
    for (const element of define) {
      await DeliverDefine.createInstance({ ...updateParam, DeliverDefineWorkId: element });
    }
  },

  async createDeliveryHistory(newDeliverData, deliveryData, loginUser, memberDetails) {
    const history = {
      DeliveryRequestId: newDeliverData.id,
      DeliveryId: newDeliverData.DeliveryId,
      MemberId: memberDetails.id,
      ProjectId: deliveryData.ProjectId,
      isDeleted: false,
      type: 'create',
      description: `${loginUser.firstName} ${loginUser.lastName} Created Delivery Booking, ${deliveryData.description}.`,
    };
    await DeliverHistory.createInstance(history);

    // If the delivery is auto-approved, create an approval history entry as well
    if (newDeliverData.status === 'Approved') {
      const approvedHistory = {
        ProjectId: deliveryData.ProjectId,
        MemberId: memberDetails.id,
        DeliveryRequestId: newDeliverData.id,
        isDeleted: false,
        type: 'approved',
        description: `${loginUser.firstName} ${loginUser.lastName} Approved Delivery Booking, ${deliveryData.description}.`,
      };
      await DeliverHistory.createInstance(approvedHistory);
    }

    return history;
  },

  async processNotifications(newDeliverData, deliveryData, loginUser, memberDetails, projectDetails, history) {
    // Get location and notification preferences
    const locationChosen = await Locations.findOne({
      where: {
        ProjectId: deliveryData.ProjectId,
        id: deliveryData.LocationId,
      },
    });

    // Create notification object
    const notification = {
      DeliveryRequestId: newDeliverData.id,
      DeliveryId: newDeliverData.DeliveryId,
      MemberId: memberDetails.id,
      ProjectId: deliveryData.ProjectId,
      title: `Delivery Booking Created by ${loginUser.firstName} ${loginUser.lastName}`,
      requestType: 'deliveryRequest',
    };

    // Create notification instance
    const newNotification = await Notification.createInstance(notification);

    // Get member location preferences
    const memberLocationPreference = await LocationNotificationPreferences.findAll({
      where: {
        ProjectId: deliveryData.ProjectId,
        LocationId: deliveryData.LocationId,
        isDeleted: false,
      },
    });

    // Send notifications if needed
    if (memberLocationPreference && memberLocationPreference.length > 0) {
      await pushNotification.sendMemberLocationPreferencePushNotification(
        memberLocationPreference,
        newDeliverData.DeliveryRequestId,
        history.locationFollowDescription || `${loginUser.firstName} ${loginUser.lastName} Created Delivery Booking, ${deliveryData.description}. Location: ${locationChosen?.locationPath || 'Unknown'}.`,
        'deliveryRequest',
        deliveryData.ProjectId,
        newNotification.id,
        loginUser.id,
        memberDetails.id,
        'create'
      );
    }

    return { success: true, history, notification: newNotification };
  },

  async handleNotifications(newDeliverData, deliveryData, loginUser, memberDetails, projectDetails) {
    if (!newDeliverData || Object.keys(newDeliverData).length === 0) {
      return { message: 'Bookings will not be created for the scheduled date/time' };
    }

    const history = await this.createDeliveryHistory(newDeliverData, deliveryData, loginUser, memberDetails);
    const notificationResult = await this.processNotifications(
      newDeliverData,
      deliveryData,
      loginUser,
      memberDetails,
      projectDetails,
      history
    );

    return notificationResult;
  },

  async compareDeliveryDateWithDeliveryWindowDate(
    dateStr,
    timeStr,
    timezoneStr,
    deliveryWindowTime,
    deliveryWindowTimeUnit,
  ) {
    const datetimeStr = `${moment(dateStr).format('YYYY-MM-DD')}T${timeStr}`;
    const datetime = moment.tz(datetimeStr, timezoneStr);
    const currentDatetime = moment
      .tz(timezoneStr)
      .add(deliveryWindowTime, deliveryWindowTimeUnit)
      .startOf('minute');
    return datetime.isSameOrBefore(currentDatetime);
  },
  async ReadAllnotification(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const loginUser = inputData.user;
      const memberDetails = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          isActive: true,
          isDeleted: false,
          ProjectId: inputData.query.ProjectId,
        }),
      });
      await DeliveryPersonNotification.update(
        { seen: true },
        {
          where: {
            ProjectId: inputData.query.ProjectId,
            MemberId: memberDetails.id,
            isDeleted: false,
          },
        },
      );
      const condition = {
        seen: false,
        ProjectId: inputData.query.ProjectId,
      };
      const count = await DeliveryPersonNotification.getUnSeenCount(condition, loginUser);
      done(count.length, false);
    } catch (e) {
      return done(null, e);
    }
  },

  async getDomainFromEnterprise(domainName) {
    if (!domainName) return { domainName: '', enterpriseValue: null };

    const domainEnterpriseValue = await Enterprise.findOne({
      where: { name: domainName.toLowerCase() },
    });

    return domainEnterpriseValue
      ? { domainName: domainName.toLowerCase(), enterpriseValue: domainEnterpriseValue }
      : { domainName: '', enterpriseValue: null };
  },

  async getDomainFromParentCompany(inputData, ParentCompanyId) {
    let { email } = inputData.user;
    let enterpriseValue = null;
    let domainName = '';

    if (!email) return { domainName, enterpriseValue };

    const userData = await publicUser.findOne({ where: { email } });
    if (!userData) return { domainName, enterpriseValue };

    const memberData = await publicMember.findOne({
      where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
    });

    if (memberData) {
      enterpriseValue = await Enterprise.findOne({
        where: memberData.isAccount
          ? { id: memberData.EnterpriseId, status: 'completed' }
          : { ParentCompanyId, status: 'completed' },
      });
    } else {
      enterpriseValue = await Enterprise.findOne({
        where: { ParentCompanyId, status: 'completed' },
      });
    }

    if (enterpriseValue) {
      domainName = enterpriseValue.name.toLowerCase();
    }

    return { domainName, enterpriseValue };
  },

  async resolveDomainName(inputData) {
    let { domainName } = inputData.user;
    let enterpriseValue;

    const ParentCompanyId =
      inputData.body.ParentCompanyId ?? inputData.params.ParentCompanyId;

    // First try to get domain from the Enterprise table
    const domainResult = await this.getDomainFromEnterprise(domainName);
    domainName = domainResult.domainName;
    enterpriseValue = domainResult.enterpriseValue;

    // If no domainName and valid ParentCompanyId, try with parent company
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const parentResult = await this.getDomainFromParentCompany(inputData, ParentCompanyId);
      domainName = parentResult.domainName;
      enterpriseValue = parentResult.enterpriseValue;
    }

    return { domainName, enterpriseValue };
  },


  async assignModels(domainName) {
    const modelObj = await helper.getDynamicModel(domainName);
    ({
      DeliveryRequest,
      Member,
      DeliveryPerson,
      DeliverGate,
      DeliverEquipment,
      DeliverCompany,
      Role,
      Gates,
      Equipments,
      DeliverDefineWork,
      Company,
      Project,
      User,
      DeliverDefine,
      DeliverHistory,
      VoidList,
      DeliveryPersonNotification,
      Notification,
    } = modelObj);
    return { User, Project };
  },

  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    const { domainName, enterpriseValue } = await this.resolveDomainName(inputData);
    const { User, Project } = await this.assignModels(domainName);

    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      inputData.user = newUser;
    }

    return Project?.id || null;
  },


  async Markallnotification(inputData) {
    try {
      await this.getDynamicModel(inputData);
      const loginUser = inputData.user;
      const { params } = inputData;

      const memberDetails = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          isActive: true,
          isDeleted: false,
          ProjectId: params.ProjectId,
        }),
      });
      await DeliveryPersonNotification.update(
        { seen: true },
        {
          where: {
            ProjectId: params.ProjectId,
            MemberId: memberDetails.id,
            isDeleted: false,
          },
        },
      );
      const condition = {
        seen: false,
        ProjectId: params.ProjectId,
      };
      const count = await DeliveryPersonNotification.getUnSeenCount(condition, loginUser);
      return { status: 200, data: count.length };
    } catch (e) {
      console.log(e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async checkInputDatas(inputData, done) {
    await this.getDynamicModel(inputData);
    const deliveryData = inputData.body;
    const { companies, persons, define } = deliveryData;
    const gates = [deliveryData.GateId];
    const equipments = deliveryData.EquipmentId;
    const inputProjectId = deliveryData.ProjectId;
    const memberList = await Member.count({
      where: { id: { [Op.in]: persons }, ProjectId: inputProjectId, isDeleted: false },
    });
    const gateList = await Gates.count({
      where: { id: { [Op.in]: gates }, ProjectId: inputProjectId, isDeleted: false },
    });
    const equipmentList = await Equipments.count({
      where: { id: { [Op.in]: equipments }, ProjectId: inputProjectId, isDeleted: false },
    });
    const defineList = await DeliverDefineWork.count({
      where: { id: { [Op.in]: define }, ProjectId: inputProjectId, isDeleted: false },
    });
    const companyList = await Company.count({
      where: {
        [Op.or]: [
          {
            id: { [Op.in]: companies },
            ProjectId: +inputProjectId,
            isDeleted: false,
          },
          {
            id: {
              [Op.in]: companies,
            },
            isParent: true,
            ParentCompanyId: +deliveryData.ParentCompanyId,
            isDeleted: false,
          },
        ],
      },
    });
    if (deliveryData.persons && deliveryData.persons.length > 0 && memberList !== persons.length) {
      return done(null, { message: 'Some Member is not in the project' });
    }
    if (deliveryData.GateId && gateList !== gates.length) {
      return done(null, { message: 'Mentioned Gate is not in the project' });
    }
    if ((deliveryData.EquipmentId && equipments[0] != 0) && equipmentList !== equipments.length) {
      return done(null, { message: 'Mentioned Equipment is not in this project' });
    }
    if (
      deliveryData.companies &&
      deliveryData.companies.length > 0 &&
      companyList !== companies.length
    ) {
      return done(null, { message: 'Some Company is not in the project' });
    }
    if (deliveryData.define && deliveryData.define.length > 0 && defineList !== define.length) {
      return done(null, { message: 'Some Definable Feature of Work is not in the project' });
    }
    return done(true, false);
  },
  async editRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const deliveryData = inputData.body;
      deliveryData.deliveryStart = await this.convertToISO(deliveryData.deliveryStart, deliveryData.deliveryStartTime);
      deliveryData.deliveryEnd = await this.convertToISO(deliveryData.deliveryEnd, deliveryData.deliveryEndTime);
      const loginUser = inputData.user;
      const { recurrenceId } = deliveryData;
      let editSeriesRequests;
      let newRecurrenceId;
      let previousRecordInSeries = [];
      const projectSettingDetails = await Project.getProjectAndSettings({
        isDeleted: false,
        id: +deliveryData.ProjectId,
      });
      const memberDetails = await Member.getBy({
        UserId: loginUser.id,
        ProjectId: deliveryData.ProjectId,
        isActive: true,
        isDeleted: false,
      });
      const range = momentRange.range(
        moment(deliveryData.deliveryStart),
        moment(deliveryData.recurrenceEndDate).add(1, 'day'),
      );
      const totalDays = Array.from(range.by('day'));
      const roleDetails = await Role.getBy('Project Admin');
      const accountRoleDetails = await Role.getBy('Account Admin');
      const startTime = deliveryData.deliveryStartTime;
      const endTime = deliveryData.deliveryEndTime;
      const date1 = moment(deliveryData.deliveryStart).format('MM/DD/YYYY');
      const date2 = moment(deliveryData.recurrenceEndDate).format('MM/DD/YYYY');
      const chosenTimezoneDeliveryStart = moment.tz(
        `${date1} ${startTime}`,
        'MM/DD/YYYY HH:mm',
        deliveryData.timezone,
      );
      const chosenTimezoneDeliveryEnd = moment.tz(
        `${date2} ${endTime}`,
        'MM/DD/YYYY HH:mm',
        deliveryData.timezone,
      );
      const deliveryStartUTC = chosenTimezoneDeliveryStart.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
      const recurrenceEndUTC = chosenTimezoneDeliveryEnd.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
      const editDeliveryData = await DeliveryRequest.findOne({ where: { id: deliveryData.id } });

      if (!projectSettingDetails) return done(null, { message: 'Project settings not found' });

      // Handle series options
      if (deliveryData.seriesOption === 1) {
        const error = await this._handleSeriesOptionOne(deliveryData, deliveryStartUTC, projectSettingDetails, done);
        if (error) return;
      }
      if (deliveryData.seriesOption === 2) {
        const error = await this._handleSeriesOptionTwo({ deliveryData, editDeliveryData, recurrenceId, memberDetails, totalDays, roleDetails, accountRoleDetails, projectSettingDetails }, done);
        if (error) return;
      }

      // Handle event updates
      ({ editSeriesRequests, previousRecordInSeries, newRecurrenceId } = await this._getEditSeriesRequests(deliveryData, editDeliveryData, recurrenceId));
      if (!editSeriesRequests?.length) return done(null, { message: 'No series requests found' });

      await this._updateRecurrenceSeries(editSeriesRequests, previousRecordInSeries, deliveryData, deliveryStartUTC, recurrenceEndUTC, newRecurrenceId);

      // Update each request in the series
      let notificationsCreated = false;
      for (const seriesData of editSeriesRequests) {
        const error = await this._updateSingleRequest({
          seriesData,
          inputData,
          deliveryData,
          loginUser,
          memberDetails,
          roleDetails,
          accountRoleDetails,
          projectSettingDetails,
          newRecurrenceId,
          skipNotifications: notificationsCreated // Skip notifications for subsequent requests
        },
          done
        );
        if (error) return;
        notificationsCreated = true; // Mark notifications as created after first request
      }
    } catch (e) {
      return done(null, e);
    }
  },

  // --- Helper methods for editRequest ---

  async _handleSeriesOptionOne(deliveryData, deliveryStartUTC, projectSettingDetails, done) {
    const requestArray = [{
      ProjectId: deliveryData.ProjectId,
      deliveryStart: deliveryStartUTC,
      deliveryEnd: deliveryData.deliveryEnd,
      id: deliveryData.id,
    }];
    const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
      requestArray,
      projectSettingDetails,
      'edit',
      deliveryData.GateId,
    );
    if (isOverlapping?.error) {
      done(null, { message: isOverlapping.message });
      return true;
    }
    return false;
  },

  async _handleSeriesOptionTwo(params, done) {
    const { deliveryData, memberDetails, totalDays, roleDetails, accountRoleDetails, projectSettingDetails } = params;

    if (deliveryData.recurrenceEdited) {
      await this.createNewRecurrenceEvents(
        deliveryData,
        memberDetails,
        totalDays,
        roleDetails,
        accountRoleDetails,
        projectSettingDetails,
        async (checkResponse, checkError) => {
          if (checkError) {
            done(null, checkError);
            return true;
          }
        }
      );
    }
    return false;
  },

  async _getEditSeriesRequests(deliveryData, editDeliveryData, recurrenceId) {
    let editSeriesRequests = [];
    let previousRecordInSeries = [];
    let newRecurrenceId = null;
    if (deliveryData.seriesOption === 1) {
      editSeriesRequests = await DeliveryRequest.findAll({
        where: [Sequelize.and({ id: deliveryData.id, isDeleted: false })],
      });
      if (editSeriesRequests?.[0] && deliveryData.recurrenceId) {
        previousRecordInSeries = await DeliveryRequest.findAll({
          where: [Sequelize.and({ recurrenceId, deliveryStart: { [Op.lt]: editDeliveryData.deliveryStart }, isDeleted: false })],
          order: [['id', 'DESC']],
        });
      }
    } else if (deliveryData.seriesOption === 2) {
      editSeriesRequests = await DeliveryRequest.findAll({
        where: [Sequelize.and({ recurrenceId, deliveryStart: { [Op.gte]: editDeliveryData.deliveryStart }, isDeleted: false })],
      });
      previousRecordInSeries = await DeliveryRequest.findAll({
        where: [Sequelize.and({ recurrenceId, deliveryStart: { [Op.lt]: editDeliveryData.deliveryStart }, isDeleted: false })],
        order: [['id', 'DESC']],
      });
    }
    return { editSeriesRequests, previousRecordInSeries, newRecurrenceId };
  },

  async _updateRecurrenceSeries(editSeriesRequests, previousRecordInSeries, deliveryData, deliveryStartUTC, recurrenceEndUTC, newRecurrenceId) {
    if (!editSeriesRequests?.[0]) return;

    const requestDataInstance = await DeliveryRequest.getSingleDeliveryRequestData({ id: editSeriesRequests[0].id });
    const requestData = requestDataInstance.toJSON();

    if (!requestData?.recurrence) return;

    // Common recurrence updates
    requestData.recurrence.ParentCompanyId = deliveryData.ParentCompanyId;
    requestData.recurrence.ProjectId = deliveryData.ProjectId;

    if (deliveryData.seriesOption === 1) {
      this._handleSeriesOptionOneData(requestData, deliveryData);
    } else if (deliveryData.seriesOption === 2) {
      await this._handleSeriesOptionTwoData(
        editSeriesRequests,
        previousRecordInSeries,
        deliveryData,
        deliveryStartUTC,
        recurrenceEndUTC,
        newRecurrenceId
      );
    }
  },

  _handleSeriesOptionOneData(requestData, deliveryData) {
    requestData.recurrence.deliveryStart = deliveryData.recurrenceSeriesStartDate;
    requestData.recurrence.deliveryEnd = deliveryData.recurrenceSeriesEndDate;
  },

  async _handleSeriesOptionTwoData(editSeriesRequests, previousRecordInSeries, deliveryData, deliveryStartUTC, recurrenceEndUTC, newRecurrenceId) {
    // Basic recurrence date updates
    requestData.recurrence.deliveryStart = deliveryData.recurrenceSeriesStartDate;
    requestData.recurrence.deliveryEnd = recurrenceEndUTC;

    if (previousRecordInSeries && previousRecordInSeries.length > 0) {
      // Insert new recurrence series
      const recurrenceData = { ...deliveryData, recurrenceStartDate: deliveryStartUTC, recurrenceEndDate: recurrenceEndUTC };
      newRecurrenceId = await concreteRequestService.insertEditRecurrenceSeries(recurrenceData, loginUser, deliveryData.requestType);

      // Update existing requests with new recurrenceId
      for (const request of editSeriesRequests) {
        await DeliveryRequest.update({ recurrenceId: +newRecurrenceId }, { where: { id: request.id } });
      }

      // Update previous recurrence end date
      const previosData = previousRecordInSeries;
      const recurrenceEditData = {
        recurrenceEndDate: await this.convertTimezoneToUtc(
          moment(previosData[0].deliveryEnd).format('MM/DD/YYYY'),
          deliveryData.timezone,
          '00:00'
        ),
      };
      await RequestRecurrenceSeries.update(recurrenceEditData, { where: { id: previosData[0].recurrenceId } });
    } else {
      // Update recurrence series data directly
      const data = {
        recurrenceEndDate: recurrenceEndUTC,
        recurrence: deliveryData.recurrence,
        repeatEveryCount: deliveryData.repeatEveryCount,
        repeatEveryType: deliveryData.repeatEveryType,
        days: deliveryData.days,
        dateOfMonth: deliveryData.dateOfMonth,
        chosenDateOfMonth: deliveryData.chosenDateOfMonth,
        monthlyRepeatType: deliveryData.monthlyRepeatType,
        requestType: deliveryData.requestType,
        chosenDateOfMonthValue: deliveryData.chosenDateOfMonthValue,
      };
      await RequestRecurrenceSeries.update(data, { where: { id: deliveryData.recurrenceId } });
    }
  },


  async _updateSingleRequest(
    params,
    done
  ) {
    const { seriesData,
      inputData,
      deliveryData,
      loginUser,
      memberDetails,
      roleDetails,
      accountRoleDetails,
      projectSettingDetails,
      newRecurrenceId,
      skipNotifications = false,
    } = params;
    // Find the delivery request and related data
    const idDetails = await DeliveryRequest.findOne({
      where: [Sequelize.and({ id: seriesData.id })],
    });
    if (!idDetails) {
      done(null, { message: 'Delivery Booking id is not available' });
      return true;
    }
    const existsDeliveryRequest = await DeliveryRequest.getSingleDeliveryRequestData({ id: +idDetails.id });
    if (!existsDeliveryRequest) {
      done(null, { message: 'Delivery Booking not found' });
      return true;
    }
    // Validate and update input data
    return await this._validateAndUpdateInputData({
      inputData,
      loginUser,
      idDetails,
      deliveryData,
      memberDetails,
      roleDetails,
      accountRoleDetails,
      projectSettingDetails,
      newRecurrenceId,
      existsDeliveryRequest
    },
      done
    );
  },

  _validateAndUpdateInputData(params, done) {
    const {
      inputData, loginUser, idDetails, deliveryData,
      memberDetails, roleDetails, accountRoleDetails,
      projectSettingDetails, newRecurrenceId, existsDeliveryRequest
    } = params;

    return new Promise((resolve) => {
      this.checkInputDatas(inputData, async (checkResponse, checkError) => {
        if (checkError) {
          done(null, checkError);
          return resolve(true);
        }

        // Update related entities and handle notifications/history
        await this._updateRelatedEntitiesAndNotifications(
          {
            idDetails,
            deliveryData,
            loginUser,
            memberDetails,
            roleDetails,
            accountRoleDetails,
            projectSettingDetails,
            newRecurrenceId,
            existsDeliveryRequest,
            skipNotifications,
          },
          done
        );
        resolve(false);
      });
    });
  },


  async _updateRelatedEntitiesAndNotifications(
    params,
    done
  ) {
    const { idDetails,
      deliveryData,
      loginUser,
      memberDetails,
      roleDetails,
      accountRoleDetails,
      projectSettingDetails,
      newRecurrenceId,
      existsDeliveryRequest,
      skipNotifications = false,
    } = params;
    // 1. Update the delivery request and related entities (companies, gates, equipment, persons, define)
    await this._updateDeliveryRequestAndRelatedEntities(
      idDetails,
      deliveryData,
      memberDetails,
      projectSettingDetails,
      newRecurrenceId
    );
    // 2. Handle notifications and history
    if (!skipNotifications) {
      await this._handleNotificationsAndHistory(
        idDetails,
        deliveryData,
        loginUser,
        memberDetails,
        existsDeliveryRequest,
        projectSettingDetails,
        done
      );
    }
    // 3. Handle status transitions and NDR logic
    await this._handleStatusTransitions({
      idDetails,
      deliveryData,
      loginUser,
      memberData: memberDetails,
      roleDetails,
      accountRoleDetails,
      projectSettingDetails,
      existsDeliveryRequest
    },
      done
    );
  },

  // Placeholder for the next helpers to be implemented
  async _prepareDeliverParams(idDetails, deliveryData, memberData, projectSettingDetails, newRecurrenceId) {
    let craneId = 0;

    if (!idDetails.CraneRequestId && deliveryData.requestType === 'deliveryRequestWithCrane') {
      craneId = await this._generateCraneRequestId(deliveryData.ProjectId);
    }

    const DeliverParam = {
      description: deliveryData.description,
      escort: deliveryData.escort,
      vehicleDetails: deliveryData.vehicleDetails,
      notes: deliveryData.notes,
      isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
      requestType: deliveryData.requestType,
      cranePickUpLocation: deliveryData.cranePickUpLocation,
      craneDropOffLocation: deliveryData.craneDropOffLocation,
      recurrenceId: deliveryData.seriesOption !== 1 ? newRecurrenceId : deliveryData.recurrenceId,
      LocationId: deliveryData.LocationId,
      OriginationAddress: deliveryData.originationAddress,
      vehicleType: deliveryData.vehicleType,
    };

    if (!idDetails.CraneRequestId && deliveryData.requestType === 'deliveryRequestWithCrane') {
      DeliverParam.CraneRequestId = craneId;
    }

    // Handle seriesOption
    await this._setDeliveryTimes(DeliverParam, idDetails, deliveryData);

    // Approval logic
    if (
      ((memberData.RoleId === 2 || memberData.RoleId === 1) && idDetails.status === 'Approved') ||
      memberData.isAutoApproveEnabled ||
      projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
    ) {
      DeliverParam.status = 'Approved';
      DeliverParam.approvedBy = memberData.id;
      DeliverParam.approved_at = new Date();
    }

    return DeliverParam;
  },

  async _generateCraneRequestId(projectId) {
    let lastData = await CraneRequest.findOne({
      where: { ProjectId: +projectId, isDeleted: false },
      order: [['CraneRequestId', 'DESC']],
    });

    const deliveryRequestList = await DeliveryRequest.findOne({
      where: {
        ProjectId: +projectId,
        isDeleted: false,
        isAssociatedWithCraneRequest: true,
      },
      order: [['CraneRequestId', 'DESC']],
    });

    if (deliveryRequestList) {
      if (lastData) {
        if (deliveryRequestList.CraneRequestId > lastData.CraneRequestId) {
          lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
        }
      } else {
        lastData = {};
        lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
      }
    }

    if (lastData) {
      lastData.CraneRequestId = (lastData.CraneRequestId || 0) + 1;
    } else {
      lastData = { CraneRequestId: 1 };
    }

    return lastData.CraneRequestId;
  },

  async _setDeliveryTimes(DeliverParam, idDetails, deliveryData) {
    if (deliveryData.seriesOption === 1) {
      DeliverParam.deliveryStart = deliveryData.deliveryStart;
      DeliverParam.deliveryEnd = deliveryData.deliveryEnd;
    }

    if (deliveryData.seriesOption === 2) {
      const utcDeliveryStartTimestamp = moment.utc(idDetails.deliveryStart);
      const localStartTimestamp = utcDeliveryStartTimestamp.tz(deliveryData.timezone);
      const utcDeliveryEndTimestamp = moment.utc(idDetails.deliveryEnd);
      const localEndTimestamp = utcDeliveryEndTimestamp.tz(deliveryData.timezone);

      DeliverParam.deliveryStart = await this.convertTimezoneToUtc(
        moment(localStartTimestamp).format('MM/DD/YYYY'),
        deliveryData.timezone,
        deliveryData.deliveryStartTime
      );
      DeliverParam.deliveryEnd = await this.convertTimezoneToUtc(
        moment(localEndTimestamp).format('MM/DD/YYYY'),
        deliveryData.timezone,
        deliveryData.deliveryEndTime
      );
    }
  },

  async _updateDeliveryRequestAndRelatedEntities(idDetails, deliveryData, memberData, projectSettingDetails, newRecurrenceId) {
    const DeliverParam = await this._prepareDeliverParams(
      idDetails, deliveryData, memberData, projectSettingDetails, newRecurrenceId
    );

    await DeliveryRequest.update(DeliverParam, { where: { id: idDetails.id } });

    // Update related entities
    await this._updateRelatedCompanies(idDetails, deliveryData);
    await this._updateRelatedGates(idDetails, deliveryData);
    await this._updateRelatedEquipment(idDetails, deliveryData);
    await this._updateRelatedPersons(idDetails, deliveryData);
    await this._updateRelatedDefine(idDetails, deliveryData);
  },


  async _handleNotificationsAndHistory(idDetails, deliveryData, loginUser, memberData, existsDeliveryRequest, projectSettingDetails, done) {
    const locationChosen = await Locations.findOne({
      where: {
        ProjectId: deliveryData.ProjectId,
        id: deliveryData.LocationId,
      },
    });
    const history = {
      DeliveryRequestId: idDetails.id,
      DeliveryId: idDetails.DeliveryId,
      MemberId: memberData.id,
      type: 'edit',
      description: `${loginUser.firstName} ${loginUser.lastName} Updated the Delivery Booking, ${deliveryData.description}`,
      locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Updated the Delivery Booking, ${deliveryData.description}. Location: ${locationChosen.locationPath}.`,
      firstName: loginUser.firstName,
      profilePic: loginUser.profilePic,
      createdAt: new Date(),
      ProjectId: deliveryData.ProjectId,
    };
    const projectDetails = await Project.findByPk(deliveryData.ProjectId);
    history.projectName = projectDetails.projectName;

    const notification = {
      ProjectId: idDetails.ProjectId,
      title: `Delivery Booking Updated by ${loginUser.firstName} ${loginUser.lastName}`,
      requestType: 'deliveryRequest',
    };
    if (existsDeliveryRequest?.recurrence?.recurrence) {
      notification.recurrenceType = `${existsDeliveryRequest.recurrence.recurrence} From ${moment(existsDeliveryRequest.recurrence.recurrenceStartDate).format('MM/DD/YYYY')} to ${moment(existsDeliveryRequest.recurrence.recurrenceEndDate).format('MM/DD/YYYY')}`;
    }
    const newNotification = await Notification.createInstance(notification);

    const memberLocationPreference = await LocationNotificationPreferences.findAll({
      where: {
        ProjectId: deliveryData.ProjectId,
        LocationId: deliveryData.LocationId,
        follow: true,
      },
      include: [
        {
          association: 'Member',
          attributes: ['id', 'RoleId'],
          where: {
            [Op.and]: [{ id: { [Op.ne]: memberData.id } }],
          },
          include: [
            {
              association: 'User',
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      ],
    });

    const locationFollowMembers = [];
    memberLocationPreference.forEach(async (element) => {
      locationFollowMembers.push(element.Member.id);
    });

    const adminData = await Member.findAll({
      where: {
        [Op.and]: [
          { ProjectId: deliveryData.ProjectId },
          { isDeleted: false },
          { id: { [Op.in]: deliveryData.persons } },
          { id: { [Op.ne]: newNotification.MemberId } },
          { id: { [Op.notIn]: locationFollowMembers } },
        ],
      },
      include: [
        {
          association: 'User',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
      ],
      attributes: ['id', 'RoleId'],
    });

    if (memberLocationPreference && memberLocationPreference.length > 0) {
      await pushNotification.sendMemberLocationPreferencePushNotification(
        memberLocationPreference,
        deliveryData.DeliveryRequestId,
        history.locationFollowDescription,
        deliveryData.requestType,
        deliveryData.ProjectId,
        deliveryData.id,
        5,
      );
      await notificationHelper.createMemberDeliveryLocationInAppNotification(
        DeliveryPersonNotification,
        deliveryData.ProjectId,
        newNotification.id,
        memberLocationPreference,
        5,
      );
    }

    history.adminData = adminData;
    if (memberLocationPreference && memberLocationPreference.length > 0) {
      history.memberData = [];
      history.memberData.push(...memberLocationPreference);
    }

    await notificationHelper.createDeliveryPersonNotification(
      adminData,
      [],
      projectDetails,
      newNotification,
      DeliveryPersonNotification,
      memberData,
      loginUser,
      5,
      'updated a',
      'Delivery Request',
      `delivery Booking (${idDetails.DeliveryId} - ${idDetails.description})`,
      idDetails.id,
    );

    const checkMemberNotification = await NotificationPreference.findAll({
      where: {
        ProjectId: deliveryData.ProjectId,
        isDeleted: false,
      },
      attributes: [
        'id',
        'MemberId',
        'ProjectId',
        'ParentCompanyId',
        'NotificationPreferenceItemId',
        'instant',
        'dailyDigest',
      ],
      include: [
        {
          association: 'NotificationPreferenceItem',
          where: {
            id: 5,
            isDeleted: false,
          },
          attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
        },
      ],
    });

    history.notificationPreference = checkMemberNotification;
    const updatedDeliveryRequest = await DeliveryRequest.getSingleDeliveryRequestData({
      id: +idDetails.id,
    });

    if (
      updatedDeliveryRequest.status === 'Approved' &&
      idDetails.status !== 'Approved' &&
      idDetails.isQueued === false
    ) {
      const object = {
        ProjectId: deliveryData.ProjectId,
        MemberId: memberData.id,
        DeliveryRequestId: updatedDeliveryRequest.id,
        isDeleted: false,
        type: 'approved',
        description: history.description,
      };
      await DeliverHistory.createInstance(object);
    }

    if (!deliveryData.ndrStatus) {
      await this.updateEditDeliveryRequestHistory(
        deliveryData,
        existsDeliveryRequest,
        updatedDeliveryRequest,
        history,
        loginUser,
      );
      await pushNotification.sendDeviceToken(history, 5, deliveryData.ProjectId);
    }
  },

  async _handleStatusTransitions(params, done) {
    const { idDetails, deliveryData, loginUser, memberData, roleDetails, accountRoleDetails, projectSettingDetails, existsDeliveryRequest } = params;
    const editedNDR = await DeliveryRequest.getNDRData({
      id: idDetails.id,
    });

    if (editedNDR.isQueued === true) {
      if (
        editedNDR.description &&
        editedNDR.deliveryStart &&
        editedNDR.deliveryEnd &&
        editedNDR.memberDetails.length > 0 &&
        editedNDR.companyDetails.length > 0 &&
        editedNDR.gateDetails.length > 0 &&
        editedNDR.equipmentDetails.length > 0 &&
        editedNDR.escort !== null
      ) {
        await DeliveryRequest.update(
          { isAllDetailsFilled: true },
          { where: { id: editedNDR.id } },
        );

        if (deliveryData.updateQueuedRequest === 1) {
          const queuedRequestPayload = { isQueued: false };
          if (
            editedNDR.isQueued === true &&
            (memberData.RoleId === roleDetails.id ||
              memberData.RoleId === accountRoleDetails.id ||
              memberData.isAutoApproveEnabled ||
              projectSettingDetails.ProjectSettings.isAutoApprovalEnabled)
          ) {
            queuedRequestPayload.status = 'Approved';
            queuedRequestPayload.approvedBy = memberData.id;
            queuedRequestPayload.approved_at = new Date();
            const historyUpdateObject = {
              ProjectId: +deliveryData.ProjectId,
              MemberId: memberData.id,
              DeliveryRequestId: editedNDR.id,
              isDeleted: false,
              type: 'approved',
              description: `${loginUser.firstName} ${loginUser.lastName} Approved Delivery Booking, ${editedNDR.description}.`,
            };
            await DeliverHistory.createInstance(historyUpdateObject);
          }
          await DeliveryRequest.update(queuedRequestPayload, {
            where: { id: editedNDR.id },
          });
        }
      }
    }

    if (!deliveryData.ndrStatus) {
      await this._handleStatusChanges({ editedNDR, existsDeliveryRequest, deliveryData, loginUser, memberData, projectSettingDetails });
    }

    if (+memberData.RoleId === 4 || +memberData.RoleId === 3) {
      await this._sendApprovalEmails(deliveryData, loginUser, memberData, idDetails, done);
    } else {
      return done({}, false);
    }
  },

  async _handleStatusChanges(params) {
    const { editedNDR, existsDeliveryRequest, deliveryData, loginUser, memberData, projectSettingDetails } = params;

    const tagsUpdated = this._checkTagsUpdated(editedNDR, existsDeliveryRequest);
    const fieldsChanged = this._checkFieldsChanged(editedNDR, existsDeliveryRequest, tagsUpdated);
    const deliveryDateTimeChanged = this._checkDeliveryDateTimeChanged(editedNDR, existsDeliveryRequest);

    // Handle status transitions based on current status
    await this._updateStatusBasedOnCurrentStatus({
      currentStatus: existsDeliveryRequest.status,
      editedNDRId: editedNDR.id,
      fieldsChanged,
      deliveryDateTimeChanged,
      memberData,
      projectSettingDetails,
      deliveryData,
      loginUser
    });
  },

  _checkTagsUpdated(editedNDR, existsDeliveryRequest) {
    const collectionsToCheck = [
      { key: 'defineWorkDetails', idPath: (el) => el.id },
      { key: 'gateDetails', idPath: (el) => el.Gate.id },
      { key: 'equipmentDetails', idPath: (el) => el.Equipment.id },
      { key: 'companyDetails', idPath: (el) => el.Company.id },
      { key: 'memberDetails', idPath: (el) => el.Member.id },
    ];

    for (const { key, idPath } of collectionsToCheck) {
      if (editedNDR[key]?.length > 0 && existsDeliveryRequest[key]?.length > 0) {
        const added = editedNDR[key].filter(
          (el) => !existsDeliveryRequest[key].find((element) => idPath(element) === idPath(el))
        );
        const deleted = existsDeliveryRequest[key].filter(
          (el) => !editedNDR[key].find((element) => idPath(element) === idPath(el))
        );
        if (added.length > 0 || deleted.length > 0) {
          return true;
        }
      }
    }

    return false;
  },

  _checkFieldsChanged(editedNDR, existsDeliveryRequest, tagsUpdated) {
    return (
      existsDeliveryRequest.description !== editedNDR.description ||
      existsDeliveryRequest.CraneRequestId !== editedNDR.CraneRequestId ||
      existsDeliveryRequest.LocationId !== editedNDR.LocationId ||
      existsDeliveryRequest.requestType !== editedNDR.requestType ||
      existsDeliveryRequest.vehicleDetails !== editedNDR.vehicleDetails ||
      existsDeliveryRequest.notes !== editedNDR.notes ||
      existsDeliveryRequest.isAssociatedWithCraneRequest !== editedNDR.isAssociatedWithCraneRequest ||
      existsDeliveryRequest.escort !== editedNDR.escort ||
      existsDeliveryRequest.craneDropOffLocation !== editedNDR.craneDropOffLocation ||
      existsDeliveryRequest.cranePickUpLocation !== editedNDR.cranePickUpLocation ||
      tagsUpdated ||
      existsDeliveryRequest.recurrence !== editedNDR.recurrence ||
      existsDeliveryRequest.chosenDateOfMonth !== editedNDR.chosenDateOfMonth ||
      existsDeliveryRequest.dateOfMonth !== editedNDR.dateOfMonth ||
      existsDeliveryRequest.monthlyRepeatType !== editedNDR.monthlyRepeatType ||
      existsDeliveryRequest.days !== editedNDR.days ||
      existsDeliveryRequest.repeatEveryType !== editedNDR.repeatEveryType ||
      existsDeliveryRequest.repeatEveryCount !== editedNDR.repeatEveryCount
    );
  },

  _checkDeliveryDateTimeChanged(editedNDR, existsDeliveryRequest) {
    return (
      moment(existsDeliveryRequest.deliveryStart).format('h:mm a') !== moment(editedNDR.deliveryStart).format('h:mm a') ||
      moment(existsDeliveryRequest.deliveryEnd).format('h:mm a') !== moment(editedNDR.deliveryEnd).format('h:mm a')
    );
  },


  async _updateStatusBasedOnCurrentStatus(params) {
    const {
      currentStatus,
      editedNDRId,
      fieldsChanged,
      deliveryDateTimeChanged,
      memberData,
      projectSettingDetails,
      deliveryData,
      loginUser,
    } = params;

    const changed = Boolean(fieldsChanged || deliveryDateTimeChanged);
    const isApproverRole = memberData.RoleId === 2;
    const autoApproveEnabled =
      memberData.isAutoApproveEnabled ||
      projectSettingDetails.ProjectSettings.isAutoApprovalEnabled;

    const ctx = {
      currentStatus,
      editedNDRId,
      changed,
      fieldsChanged,
      deliveryDateTimeChanged,
      isApproverRole,
      autoApproveEnabled,
      memberData,
      projectSettingDetails,
      deliveryData,
      loginUser,
    };

    await this._applyPendingRules(ctx);
    await this._applyApprovalRules(ctx);
  },


  async _applyPendingRules(ctx) {
    const {
      currentStatus,
      changed,
      isApproverRole,
      autoApproveEnabled,
      editedNDRId,
    } = ctx;

    // Only certain current statuses ever downgrade to Pending.
    const canDowngradeStatuses = ['Delivered', 'Approved', 'Expired', 'Declined'];

    if (
      changed &&
      !isApproverRole &&
      !autoApproveEnabled &&
      canDowngradeStatuses.includes(currentStatus)
    ) {
      await DeliveryRequest.update(
        { status: 'Pending' },
        { where: { id: editedNDRId } }
      );
    }
  },


  async _applyApprovalRules(ctx) {
    const {
      currentStatus,
      changed,
      isApproverRole,
      autoApproveEnabled,
      editedNDRId,
      memberData,
      deliveryData,
      loginUser,
    } = ctx;

    // Decide if approval logic should run at all for this status.
    let shouldApprove = false;
    let includeApprovalMeta = false; // whether to set approvedBy & approved_at
    let historyDescriptionPrefix = null; // null => no history entry

    switch (currentStatus) {
      case 'Delivered':
        // Original: only if fieldsChanged AND RoleId === 2
        shouldApprove = changed && isApproverRole;

        break;

      case 'Approved':
        // Original: if (changed && approver) OR autoApproveEnabled
        shouldApprove = (changed && isApproverRole) || autoApproveEnabled;

        break;

      case 'Expired':
      case 'Declined':
        // Original: ((changed && approver) OR autoApproveEnabled) => with meta + history
        shouldApprove = (changed && isApproverRole) || autoApproveEnabled;
        includeApprovalMeta = true;
        historyDescriptionPrefix = 'Approved the Delivery Booking';
        break;

      case 'Pending':
        // Original had two overlapping blocks:
        // 1) (changed && !approver && autoApprove) -> Approved (no meta)
        // 2) ((changed && approver) || autoApprove) -> Approved (with meta + history)
        // Effective simplified logic:
        shouldApprove = (changed && isApproverRole) || autoApproveEnabled;
        includeApprovalMeta = shouldApprove; // when we approve from Pending we set meta
        historyDescriptionPrefix = 'Approved the Delivery Request';
        break;

      default:
        // Other statuses not handled
        break;
    }

    if (!shouldApprove) return;

    const updatePayload = { status: 'Approved' };

    if (includeApprovalMeta) {
      Object.assign(updatePayload, {
        approvedBy: memberData.id,
        approved_at: new Date(),
      });
    }

    const isStatusDataUpdated = await DeliveryRequest.update(
      updatePayload,
      { where: { id: editedNDRId } }
    );

    if (
      isStatusDataUpdated &&
      historyDescriptionPrefix // only when we wanted history
    ) {
      const historyObj = {
        ProjectId: deliveryData.ProjectId,
        MemberId: memberData.id,
        DeliveryRequestId: editedNDRId,
        isDeleted: false,
        type: 'approved',
        description: `${loginUser.firstName} ${loginUser.lastName} ${historyDescriptionPrefix}, ${deliveryData.description}`,
      };
      await DeliverHistory.createInstance(historyObj);
    }
  },


  async _sendApprovalEmails(deliveryData, loginUser, memberData, idDetails, done) {
    try {
      const memberLocationPreference = await this._getMemberLocationPreferences(deliveryData, memberData);
      const userEmails = await this.getMemberDetailData({}, memberLocationPreference);

      if (userEmails.length > 0) {
        await this._sendApprovalEmailsToMembers(userEmails, deliveryData, loginUser, memberData, idDetails);
        await this._notifyGuestUsers(deliveryData, loginUser);
        return done({}, false);
      } else {
        return done({}, false);
      }
    } catch (error) {
      console.error('Error in _sendApprovalEmails:', error);
      return done(error, true);
    }
  },

  async _getMemberLocationPreferences(deliveryData, memberData) {
    return await LocationNotificationPreferences.findAll({
      where: {
        ProjectId: deliveryData.ProjectId,
        LocationId: deliveryData.LocationId,
        follow: true,
      },
      include: [
        {
          association: 'Member',
          attributes: ['id', 'RoleId'],
          where: {
            [Op.and]: [{ id: { [Op.ne]: memberData.id } }],
          },
          include: [
            {
              association: 'User',
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      ],
    });
  },


  async _sendApprovalEmailsToMembers(userEmails, deliveryData, loginUser, memberData, idDetails) {
    for (const element of userEmails) {
      if (element.RoleId !== 2) continue;

      const name = element.firstName ? `${element.firstName} ${element.lastName}` : 'user';
      const memberRole = await Role.findOne({
        where: { id: memberData.RoleId, isDeleted: false },
      });

      const mailPayload = {
        name,
        email: element.email,
        content: `We would like to inform you that ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has updated a delivery booking ${idDetails.DeliveryId} and waiting for your approval. Kindly review the booking and update the status.`,
      };

      const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
        where: {
          MemberId: +element.MemberId,
          ProjectId: +deliveryData.ProjectId,
          LocationId: +deliveryData.LocationId,
          isDeleted: false,
        },
      });

      if (!isMemberFollowLocation) continue;

      const memberNotification = await NotificationPreference.findOne({
        where: { MemberId: +element.MemberId, ProjectId: +deliveryData.ProjectId, isDeleted: false },
        include: [
          {
            association: 'NotificationPreferenceItem',
            where: { id: 9, isDeleted: false },
          },
        ],
      });

      if (memberNotification?.instant) {
        await MAILER.sendMail(
          mailPayload,
          'notifyPAForReApproval',
          `Delivery Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
          `Delivery Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
          async (info, err) => console.log(info, err),
        );
      }

      if (memberNotification?.dailyDigest) {
        await this.createDailyDigestDataApproval({
          RoleId: +memberData.RoleId,
          MemberId: +element.MemberId,
          ProjectId: +deliveryData.ProjectId,
          ParentCompanyId: +deliveryData.ParentCompanyId,
          loginUser,
          dailyDigestMessage: 'updated a',
          requestType: 'Delivery Request',
          messages: `delivery Booking (${idDetails.DeliveryId} - ${idDetails.description})`,
          messages2: 'and waiting for your approval',
          requestId: idDetails.id,
        });
      }
    }
  },

  async _notifyGuestUsers(deliveryData, loginUser) {
    const exist2 = await DeliveryRequest.findOne({
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'isGuestUser'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
      ],
      where: { isDeleted: false, id: +deliveryData.id },
    });

    if (!exist2?.memberDetails) return;

    for (const memberDetail of exist2.memberDetails) {
      const responsibleGuestUser = memberDetail.Member.isGuestUser;
      if (!responsibleGuestUser) continue;

      const guestMailPayload = {
        email: responsibleGuestUser.User.email,
        guestName: responsibleGuestUser.User.firstName,
        content: `We would like to inform you that ${loginUser.firstName} ${loginUser.lastName} has updated a delivery booking ${exist2.description}.`,
      };

      await MAILER.sendMail(
        guestMailPayload,
        'notifyGuestOnEdit',
        `Delivery Booking updated by ${loginUser.firstName} ${loginUser.lastName}`,
        `Delivery Booking updated by ${loginUser.firstName} ${loginUser.lastName}`,
        async (info, err) => console.log(info, err),
      );
    }
  },


  async convertToISO(input, deliveryTime) {
    const formatRegex = /^\d{4} \d{2} \d{2} \d{2}:\d{2}:\d{2}$/;
    const timeRegex = /^\d{2}:\d{2}$/;

    if (formatRegex.test(input) && timeRegex.test(deliveryTime)) {
      // Extract date parts from input
      const [year, month, day] = input.split(/[\s:]/).map(Number);

      // Extract hours and minutes from deliveryEndTime
      const [deliveryHour, deliveryMinute] = deliveryTime.split(":").map(Number);

      // Create a Date object in UTC with deliveryEndTime applied
      const date = new Date(Date.UTC(year, month - 1, day, deliveryHour, deliveryMinute, 0));

      // Convert to ISO format
      return date.toISOString(); // Outputs in "YYYY-MM-DDTHH:mm:ss.SSSZ"
    } else {
      return input; // Return null if either format is invalid
    }
  },
  async updateCompanyHistory(addedCompany, deletedCompany, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Company`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Company`;
    addedCompany.forEach(async (element, i) => {
      const newCompanyData = await Company.findOne({
        where: { id: element.CompanyId },
      });
      if (i === 0) {
        if (i === addedCompany.length - 1) {
          addDesc += ` ${newCompanyData.companyName}`;
          newHistory.description = addDesc;
          DeliverHistory.createInstance(newHistory);
        } else {
          addDesc += ` ${newCompanyData.companyName}`;
        }
      } else if (i === addedCompany.length - 1) {
        addDesc += `,${newCompanyData.companyName}`;
        newHistory.description = addDesc;
        DeliverHistory.createInstance(newHistory);
      } else {
        addDesc += `,${newCompanyData.companyName}`;
      }
    });
    deletedCompany.forEach(async (element, i) => {
      const newCompanyData = await Company.findOne({
        where: { id: element.CompanyId },
      });
      if (i === 0) {
        if (i === deletedCompany.length - 1) {
          deleteDesc += ` ${newCompanyData.companyName}`;
          newHistory.description = deleteDesc;
          DeliverHistory.createInstance(newHistory);
        } else {
          deleteDesc += ` ${newCompanyData.companyName}`;
        }
      } else if (i === deletedCompany.length - 1) {
        deleteDesc += `,${newCompanyData.companyName}`;
        newHistory.description = deleteDesc;
        DeliverHistory.createInstance(newHistory);
      } else {
        deleteDesc += `,${newCompanyData.companyName}`;
      }
    });
  },
  async updateGateHistory(addedGate, deletedGate, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Gate`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Gate`;
    addedGate.forEach(async (element, i) => {
      const newGateData = await Gates.findOne({
        where: { id: element.GateId },
      });
      if (i === 0) {
        if (i === addedGate.length - 1) {
          addDesc += ` ${newGateData.gateName}`;
          newHistory.description = addDesc;
          DeliverHistory.createInstance(newHistory);
        } else {
          addDesc += ` ${newGateData.gateName}`;
        }
      } else if (i === addedGate.length - 1) {
        addDesc += `,${newGateData.gateName}`;
        newHistory.description = addDesc;
        DeliverHistory.createInstance(newHistory);
      } else {
        addDesc += `,${newGateData.gateName}`;
      }
    });
    deletedGate.forEach(async (element, i) => {
      const newGateData = await Gates.findOne({
        where: { id: element.GateId },
      });
      if (i === 0) {
        if (i === deletedGate.length - 1) {
          deleteDesc += ` ${newGateData.gateName}`;
          newHistory.description = deleteDesc;
          DeliverHistory.createInstance(newHistory);
        } else {
          deleteDesc += ` ${newGateData.gateName}`;
        }
      } else if (i === deletedGate.length - 1) {
        deleteDesc += `,${newGateData.gateName}`;
        newHistory.description = deleteDesc;
        DeliverHistory.createInstance(newHistory);
      } else {
        deleteDesc += `,${newGateData.gateName}`;
      }
    });
  },
  async updateEquipmentHistory(addedEquipment, deletedEquipment, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Equipment`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Equipment`;
    addedEquipment.forEach(async (element, i) => {
      const newEquipmentData = await Equipments.findOne({
        where: { id: element.EquipmentId },
      });
      if (i === 0) {
        if (i === addedEquipment.length - 1) {
          addDesc += ` ${newEquipmentData.equipmentName}`;
          newHistory.description = addDesc;
          DeliverHistory.createInstance(newHistory);
        } else {
          addDesc += ` ${newEquipmentData.equipmentName}`;
        }
      } else if (i === addedEquipment.length - 1) {
        addDesc += `,${newEquipmentData.equipmentName}`;
        newHistory.description = addDesc;
        DeliverHistory.createInstance(newHistory);
      } else {
        addDesc += `,${newEquipmentData.equipmentName}`;
      }
    });
    deletedEquipment.forEach(async (element, i) => {
      const newEquipmentData = await Equipments.findOne({
        where: { id: element.EquipmentId },
      });
      if (i === 0) {
        if (i === deletedEquipment.length - 1) {
          deleteDesc += ` ${newEquipmentData.equipmentName}`;
          newHistory.description = deleteDesc;
          DeliverHistory.createInstance(newHistory);
        } else {
          deleteDesc += ` ${newEquipmentData.equipmentName}`;
        }
      } else if (i === deletedEquipment.length - 1) {
        deleteDesc += `,${newEquipmentData.equipmentName}`;
        newHistory.description = deleteDesc;
        DeliverHistory.createInstance(newHistory);
      } else {
        deleteDesc += `,${newEquipmentData.equipmentName}`;
      }
    });
  },
  async updatePersonHistory(addedPerson, deletedPerson, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Member`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Member`;
    addedPerson.forEach(async (element, i) => {
      const newMemberData = await Member.findOne({
        where: { id: element.MemberId, isDeleted: false },
        include: [
          {
            association: 'User',
            attributes: ['firstName', 'lastName'],
          },
        ],
      });
      if (i === 0) {
        if (i === addedPerson.length - 1) {
          if (newMemberData.User.firstName) {
            addDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
          }
          newHistory.description = addDesc;
          DeliverHistory.createInstance(newHistory);
        } else if (newMemberData.User.firstName) {
          addDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
      } else if (i === addedPerson.length - 1) {
        if (newMemberData.User.firstName) {
          addDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
        newHistory.description = addDesc;
        DeliverHistory.createInstance(newHistory);
      } else if (newMemberData.User.firstName) {
        addDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
      }
    });
    deletedPerson.forEach(async (element, i) => {
      const newMemberData = await Member.findOne({
        where: { id: element.MemberId, isDeleted: false },
        include: [
          {
            association: 'User',
            attributes: ['firstName', 'lastName'],
          },
        ],
      });
      if (i === 0) {
        if (i === deletedPerson.length - 1) {
          if (newMemberData.User.firstName) {
            deleteDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
          }
          newHistory.description = deleteDesc;
          DeliverHistory.createInstance(newHistory);
        } else if (newMemberData.User.firstName) {
          deleteDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
      } else if (i === deletedPerson.length - 1) {
        if (newMemberData.User.firstName) {
          deleteDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
        newHistory.description = deleteDesc;
        DeliverHistory.createInstance(newHistory);
      } else if (newMemberData.User.firstName) {
        deleteDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
      }
    });
  },
  async updateDefineHistory(addedDefine, deletedDefine, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the DFOW`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the DFOW`;
    addedDefine.forEach(async (element, i) => {
      const newDefineData = await DeliverDefineWork.findOne({
        where: { id: element.DeliverDefineWorkId },
      });
      if (i === 0) {
        if (i === addedDefine.length - 1) {
          addDesc += ` ${newDefineData.DFOW}`;
          newHistory.description = addDesc;
          DeliverHistory.createInstance(newHistory);
        } else {
          addDesc += ` ${newDefineData.DFOW}`;
        }
      } else if (i === addedDefine.length - 1) {
        addDesc += `,${newDefineData.DFOW}`;
        newHistory.description = addDesc;
        DeliverHistory.createInstance(newHistory);
      } else {
        addDesc += `,${newDefineData.DFOW}`;
      }
    });
    deletedDefine.forEach(async (element, i) => {
      const newDefineData = await DeliverDefineWork.findOne({
        where: { id: element.DeliverDefineWorkId },
      });
      if (i === 0) {
        if (i === deletedDefine.length - 1) {
          deleteDesc += ` ${newDefineData.DFOW}`;
          newHistory.description = deleteDesc;
          DeliverHistory.createInstance(newHistory);
        } else {
          deleteDesc += ` ${newDefineData.DFOW}`;
        }
      } else if (i === deletedDefine.length - 1) {
        deleteDesc += `,${newDefineData.DFOW}`;
        newHistory.description = deleteDesc;
        DeliverHistory.createInstance(newHistory);
      } else {
        deleteDesc += `,${newDefineData.DFOW}`;
      }
    });
  },
  async lastDelivery(inputData, done) {
    try {
      const { params } = inputData;
      let data;
      const lastData = await DeliveryRequest.findOne({
        where: { ProjectId: params.ProjectId, isDeleted: false },
        order: [['DeliveryId', 'DESC']],
      });
      if (lastData) {
        data = lastData.DeliveryId + 1;
      } else {
        data = 1;
      }
      done({ DeliveryId: data }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async updateValues(condition, done) {
    try {
      await DeliverCompany.update({ isDeleted: true }, { where: condition });
      await DeliveryPerson.update({ isDeleted: true }, { where: condition });
      await DeliverGate.update({ isDeleted: true }, { where: condition });
      await DeliverEquipment.update({ isDeleted: true }, { where: condition });
      await DeliverDefine.update({ isDeleted: true }, { where: condition });
      done({ status: 'ok' }, false);
    } catch (e) {
      done(null, e);
    }
  },
  // Helper function to build search condition for listNDR
  buildSearchCondition(incomeData, params) {
    const searchDefault = [
      {
        '$approverDetails.User.firstName$': {
          [Sequelize.Op.iLike]: `%${incomeData.search}%`,
        },
      },
      {
        '$equipmentDetails.Equipment.equipmentName$': {
          [Sequelize.Op.iLike]: `%${incomeData.search}%`,
        },
      },
      {
        '$location.locationPath$': {
          [Sequelize.Op.iLike]: `%${incomeData.search}%`,
        },
      },
      {
        description: {
          [Sequelize.Op.iLike]: `%${incomeData.search}%`,
        },
      },
      {
        cranePickUpLocation: {
          [Sequelize.Op.iLike]: `%${incomeData.search}%`,
        },
      },
      {
        craneDropOffLocation: {
          [Sequelize.Op.iLike]: `%${incomeData.search}%`,
        },
      },
    ];

    if (!Number.isNaN(+incomeData.search)) {
      return {
        [Op.and]: [
          {
            [Op.or]: [
              searchDefault,
              {
                [Op.and]: [
                  {
                    DeliveryId: +incomeData.search,
                    isDeleted: false,
                    ProjectId: +params.ProjectId,
                  },
                ],
              },
            ],
          },
        ],
      };
    } else {
      return {
        [Op.and]: [
          {
            [Op.or]: searchDefault,
          },
        ],
      };
    }
  },

  // Helper function to build query condition for listNDR
  buildQueryCondition(params, incomeData, voidDelivery, inputData) {
    const condition = {
      ProjectId: +params.ProjectId,
      isQueued: incomeData.queuedNdr,
      isDeleted: false,
    };

    // Handle void condition
    if (params.void === '0' || params.void === 0) {
      condition['$DeliveryRequest.id$'] = {
        [Op.and]: [{ [Op.notIn]: voidDelivery }],
      };
    } else {
      condition['$DeliveryRequest.id$'] = {
        [Op.and]: [{ [Op.in]: voidDelivery }],
      };
    }

    // Apply filters
    if (incomeData.descriptionFilter) {
      condition.description = {
        [Sequelize.Op.iLike]: `%${incomeData.descriptionFilter}%`,
      };
    }

    if (incomeData.pickFrom) {
      condition.cranePickUpLocation = {
        [Sequelize.Op.iLike]: `%${incomeData.pickFrom}%`,
      };
    }

    if (incomeData.pickTo) {
      condition.craneDropOffLocation = {
        [Sequelize.Op.iLike]: `%${incomeData.pickTo}%`,
      };
    }

    if (incomeData.equipmentFilter || incomeData.equipmentFilter == 0) {
      condition['$equipmentDetails.Equipment.id$'] = incomeData.equipmentFilter;
    }

    if (incomeData.locationFilter) {
      condition['$location.locationPath$'] = incomeData.locationFilter;
    }

    if (incomeData.statusFilter) {
      condition.status = incomeData.statusFilter;
    }

    if (incomeData.dateFilter) {
      const startDateTime = moment(incomeData.dateFilter, 'YYYY-MM-DD')
        .startOf('day')
        .utcOffset(Number(inputData.headers.timezoneoffset), true);
      const endDateTime = moment(incomeData.dateFilter, 'YYYY-MM-DD')
        .endOf('day')
        .utcOffset(Number(inputData.headers.timezoneoffset), true);
      condition.deliveryStart = {
        [Op.between]: [moment(startDateTime), moment(endDateTime)],
      };
    }

    if (incomeData.upcoming) {
      condition.deliveryStart = {
        [Op.gt]: new Date(),
      };
    }

    return condition;
  },

  // Helper function to sort data for listNDR
  sortData(data, sortByField, sortDirection) {
    if (sortDirection === 'ASC') {
      return data.sort((a, b) => {
        let result;
        if (a[sortByField] > b[sortByField]) {
          result = 1;
        } else if (b[sortByField] > a[sortByField]) {
          result = -1;
        } else {
          result = 0;
        }
        return result;
      });
    } else {
      return data.sort((a, b) => {
        let result;
        if (b[sortByField] > a[sortByField]) {
          result = 1;
        } else if (a[sortByField] > b[sortByField]) {
          result = -1;
        } else {
          result = 0;
        }
        return result;
      });
    }
  },

  // Refactored listNDR function with reduced complexity
  async listNDR(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;
      let { sort } = inputData.body;
      let { sortByField } = inputData.body;
      let order;
      let searchCondition = {};

      if (!(params.void === '1' || params.void === 1 || params.void === '0' || params.void === 0)) {
        return done(null, { message: 'Please enter void as 1 or 0' });
      }

      // Get member details
      const memberDetails = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: params.ProjectId,
          isDeleted: false,
          isActive: true,
        }),
      });

      if (!memberDetails) {
        return done(null, { message: 'Project Id/Member does not exist' });
      }

      // Get void delivery list
      const voidDelivery = [];
      const voidList = await VoidList.findAll({
        where: {
          ProjectId: params.ProjectId,
          isDeliveryRequest: true,
          DeliveryRequestId: { [Op.ne]: null },
        },
      });

      voidList.forEach(element => {
        voidDelivery.push(element.DeliveryRequestId);
      });

      const offset = (+params.pageNo - 1) * +params.pageSize;

      // Build query condition
      const condition = this.buildQueryCondition(params, incomeData, voidDelivery, inputData);

      // Handle upcoming flag
      if (incomeData.upcoming) {
        order = 'ASC';
        sort = 'ASC';
        sortByField = 'deliveryStart';
      }

      // Build search condition
      if (incomeData.search) {
        searchCondition = this.buildSearchCondition(incomeData, params);
      }

      const roleId = memberDetails.RoleId;
      const memberId = memberDetails.id;

      // Handle special filters
      const needsSpecialFiltering =
        incomeData.companyFilter ||
        incomeData.gateFilter ||
        incomeData.memberFilter ||
        incomeData.assignedFilter ||
        (memberDetails.RoleId === 4 && (params.void === '0' || params.void === 0) && !incomeData.upcoming) ||
        (memberDetails.RoleId === 3 && (params.void === '0' || params.void === 0) && !incomeData.upcoming);

      if (needsSpecialFiltering) {
        const result = { count: 0, rows: [] };
        const deliveryList = await DeliveryRequest.getCalendarData(
          condition,
          roleId,
          memberId,
          searchCondition,
          order,
          sort,
          sortByField,
        );

        this.getSearchData({
          incomeData,
          deliveryList: deliveryList.rows,
          result: [],
          limit: +params.pageSize,
          count: 0,
          index: 0,
          memberDetails
        },
          async (checkResponse, checkError) => {
            if (checkError) {
              return done(null, { message: 'Something went wrong' });
            }

            this.getLimitData(
              checkResponse,
              0,
              +params.pageSize,
              [],
              incomeData,
              inputData.headers.timezoneoffset,
              async (newResponse, newError) => {
                if (newError) {
                  return done(null, { message: 'Something went wrong' });
                }

                const sortedResponse = this.sortData(newResponse, sortByField, sort);
                result.rows = sortedResponse.slice(offset, offset + +params.pageSize);
                result.count = checkResponse.length;
                done(result, false);
              }
            );
          }
        );
      } else {
        const newResult = { count: 0, rows: [] };
        const deliveryList = await DeliveryRequest.getAll({
          attr: condition,
          roleId,
          memberId,
          limit: +params.pageSize,
          offset,
          searchCondition,
          order,
          sort,
          sortColumn: sortByField,
        });

        this.getLimitData(
          deliveryList,
          0,
          +params.pageSize,
          [],
          incomeData,
          inputData.headers.timezoneoffset,
          async (newResponse, newError) => {
            if (newError) {
              return done(null, { message: 'Something went wrong' });
            }

            const sortedResponse = this.sortData(newResponse, sortByField, sort);
            newResult.rows = sortedResponse.slice(offset, offset + +params.pageSize);
            newResult.count = deliveryList.length;
            done(newResult, false);
          }
        );
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getLimitData(result, index, limit, finalResult, incomeData, timezoneoffset, done) {
    if (index < limit) {
      finalResult.push(result);
      this.getLimitData(
        result,
        index + 1,
        limit,
        finalResult,
        incomeData,
        timezoneoffset,
        (response, err) => {
          if (!err) {
            done(response, false);
          } else {
            done(null, err);
          }
        },
      );
    } else {
      done(result, false);
    }
  },
  async checkDeliveryConditions(element, incomeData) {
    const status = { companyCondition: true, gateCondition: true, memberCondition: true };

    if (incomeData.companyFilter) {
      const data = await DeliverCompany.findOne({
        where: {
          DeliveryId: element.id,
          CompanyId: incomeData.companyFilter,
          isDeleted: false,
        },
      });
      if (!data) status.companyCondition = false;
    }

    if (incomeData.gateFilter) {
      const data = await DeliverGate.findOne({
        where: {
          DeliveryId: element.id,
          GateId: incomeData.gateFilter,
          isDeleted: false,
        },
      });
      if (!data) status.gateCondition = false;
    }

    if (incomeData.memberFilter) {
      const data = await DeliveryPerson.findOne({
        where: {
          DeliveryId: element.id,
          MemberId: incomeData.memberFilter,
          isDeleted: false,
        },
      });
      if (!data) status.memberCondition = false;
    }

    return status.companyCondition && status.gateCondition && status.memberCondition;
  },

  async getSearchData(params, done) {
    const { incomeData, deliveryList, result, limit, index, count, memberDetails } = params;
    const elementValue = deliveryList[index];

    if (!elementValue) return done(result, false);

    const element = JSON.parse(JSON.stringify(elementValue));
    const isValid = await this.checkDeliveryConditions(element, incomeData);

    if (isValid) result.push(element);

    if (index < deliveryList.length - 1) {
      this.getSearchData(
        { incomeData, deliveryList, result, limit, index: index + 1, count: count + 1, memberDetails },
        (response, err) => {
          if (!err) done(response, false);
          else done(null, err);
        }
      );
    } else {
      done(result, false);
    }
  },

  async getNDRData(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const condition = {
        id: params.DeliveryRequestId,
      };
      const deliveryList = await DeliveryRequest.getNDRData(condition);
      done(deliveryList, false);
    } catch (e) {
      done(null, e);
    }
  },
  async getMemberData(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const condition = {
        UserId: inputData.user.id,
        ProjectId: params.ProjectId,
      };
      const memberData = await Member.getBy(condition);
      done(memberData, false);
    } catch (e) {
      done(null, e);
    }
  },

  // Helper function to validate delivery status update permissions
  async validateDeliveryStatusUpdatePermissions(updateData, loginUser, statusValue) {
    const memberValue = await Member.findOne({
      where: Sequelize.and({
        UserId: loginUser.id,
        ProjectId: statusValue.ProjectId,
        isDeleted: false,
      }),
    });

    if (!memberValue) {
      return { error: true, message: 'Member not found in project.' };
    }

    if (![1, 2, 3, 4].includes(memberValue.RoleId)) {
      return { error: true, message: 'Insufficient permissions to update status.' };
    }

    // Special validation for SC role
    if (memberValue.RoleId === 4) {
      const NDRData = await DeliveryRequest.getNDRData({ id: updateData.id });
      if (+loginUser.id !== +NDRData.createdUserDetails.User.id) {
        return {
          error: true,
          message: 'SC can able to deliver the NDR which was created by him only.'
        };
      }
    }

    return { error: false, memberValue };
  },

  // Helper function to get delivery location and notification preferences
  async getDeliveryLocationAndNotificationPreferences(statusValue, memberValue) {
    const locationChosen = await Locations.findOne({
      where: {
        ProjectId: statusValue.ProjectId,
        id: statusValue.LocationId,
      },
    });

    const memberLocationPreference = await LocationNotificationPreferences.findAll({
      where: {
        ProjectId: statusValue.ProjectId,
        LocationId: statusValue.LocationId,
        follow: true,
      },
      include: [
        {
          association: 'Member',
          attributes: ['id', 'RoleId'],
          where: {
            [Op.and]: [
              {
                id: { [Op.ne]: memberValue.id },
              },
            ],
          },
          include: [
            {
              association: 'User',
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      ],
    });

    return { locationChosen, memberLocationPreference };
  },

  // Helper function to prepare delivery history and notification data
  async prepareDeliveryHistoryAndNotificationData(statusValue, memberValue, loginUser, updateData, locationChosen) {
    const history = {
      DeliveryRequestId: statusValue.id,
      MemberId: memberValue.id,
      DeliveryId: statusValue.DeliveryId,
    };

    const notification = { ...history };
    notification.ProjectId = statusValue.ProjectId;

    if (statusValue?.recurrence?.recurrence) {
      notification.recurrenceType = `${statusValue.recurrence.recurrence} From ${moment(
        statusValue.recurrence.recurrenceStartDate,
      ).format('MM/DD/YYYY')} to ${moment(
        statusValue.recurrence.recurrenceEndDate,
      ).format('MM/DD/YYYY')}`;
    }

    notification.requestType = 'deliveryRequest';

    return { history, notification };
  },

  // Helper function to handle delivery approved status
  async handleDeliveryApprovedStatus(updateData, statusValue, memberValue, loginUser, history, notification, locationChosen) {
    if (updateData.statuschange && updateData.statuschange === 'Reverted') {
      history.type = 'approved';
      history.description = `${loginUser.firstName} ${loginUser.lastName} Reverted the status from delivery to approved for Delivery Booking , ${statusValue.description}`;
      history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Reverted the status from delivery to approved for Delivery Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;
    } else {
      history.type = 'approved';
      history.description = `${loginUser.firstName} ${loginUser.lastName} Approved the Delivery Booking`;
      history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Approved the Delivery Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;
    }

    notification.title = `Delivery Booking Approved by ${loginUser.firstName} ${loginUser.lastName}`;
    await DeliveryRequest.update(
      { status: updateData.status, approvedBy: memberValue.id, approved_at: new Date() },
      { where: { id: updateData.id } },
    );

    const object = {
      ProjectId: statusValue.ProjectId,
      MemberId: memberValue.id,
      DeliveryRequestId: statusValue.id,
      isDeleted: false,
      type: updateData.status.toLowerCase(),
      description: history.description,
    };
    await DeliverHistory.createInstance(object);

    return { history, notification };
  },

  // Helper function to handle delivery declined or delivered status
  async handleDeliveryDeclinedOrDeliveredStatus(updateData, statusValue, memberValue, loginUser, history, notification, locationChosen) {
    history.type = updateData.status.toLowerCase();
    history.description = `${loginUser.firstName} ${loginUser.lastName} ${updateData.status} the Delivery Booking`;
    history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} ${updateData.status} the Delivery Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;

    notification.title = `Delivery Booking ${updateData.status} by ${loginUser.firstName} ${loginUser.lastName}`;
    await DeliveryRequest.update(
      { status: updateData.status, approvedBy: memberValue.id, approved_at: new Date() },
      { where: { id: updateData.id } },
    );

    const object = {
      ProjectId: statusValue.ProjectId,
      MemberId: memberValue.id,
      DeliveryRequestId: statusValue.id,
      isDeleted: false,
      type: updateData.status.toLowerCase(),
      description: history.description,
    };
    await DeliverHistory.createInstance(object);

    return { history, notification };
  },

  async updateNDRStatus(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const updateData = inputData.body;
      const loginUser = inputData.user;

      const statusValue = await DeliveryRequest.findOne({
        where: { id: updateData.id },
        include: [
          {
            association: 'recurrence',
            required: false,
            attributes: [
              'id',
              'recurrence',
              'recurrenceStartDate',
              'recurrenceEndDate',
              'dateOfMonth',
              'monthlyRepeatType',
              'repeatEveryCount',
              'days',
              'requestType',
              'repeatEveryType',
              'chosenDateOfMonth',
              'createdBy',
              'chosenDateOfMonthValue',
            ],
          },
        ],
      });

      if (!statusValue) {
        return done(null, { message: 'Id does not exist.' });
      }

      // Validate permissions
      const permissionResult = await this.validateDeliveryStatusUpdatePermissions(updateData, loginUser, statusValue);
      if (permissionResult.error) {
        return done(null, { message: permissionResult.message });
      }
      const { memberValue } = permissionResult;

      // Get location and notification preferences
      const { locationChosen, memberLocationPreference } = await this.getDeliveryLocationAndNotificationPreferences(statusValue, memberValue);

      // Prepare history and notification data
      const { history, notification } = await this.prepareDeliveryHistoryAndNotificationData(statusValue, memberValue, loginUser, updateData, locationChosen);

      // Handle different status types
      if (updateData.status === 'Approved') {
        const result = await this.handleDeliveryApprovedStatus(updateData, statusValue, memberValue, loginUser, history, notification, locationChosen);
        await this.processDeliveryApprovedStatusNotifications(result.history, result.notification, statusValue, memberLocationPreference, memberValue, loginUser, done);
      } else if (updateData.status === 'Declined' || updateData.status === 'Delivered') {
        const result = await this.handleDeliveryDeclinedOrDeliveredStatus(updateData, statusValue, memberValue, loginUser, history, notification, locationChosen);
        const params = { history: result.history, notification: result.notification, statusValue, memberLocationPreference, memberValue, loginUser, updateData }
        await this.processDeliveryDeclinedOrDeliveredStatusNotifications(params, done);
      } else {
        return done(null, { message: 'Invalid Status' });
      }

    } catch (e) {
      return done(null, e);
    }
  },

  // Helper function to process delivery approved status notifications
  async processDeliveryApprovedStatusNotifications(history, notification, statusValue, memberLocationPreference, memberValue, loginUser, done) {
    // Add common history properties
    history.firstName = loginUser.firstName;
    history.profilePic = loginUser.profilePic;
    history.createdAt = new Date();
    history.ProjectId = statusValue.ProjectId;

    const projectDetails = await Project.findByPk(statusValue.ProjectId);
    history.projectName = projectDetails.projectName;

    const newNotification = await Notification.createInstance(notification);

    // Get member and admin data
    const { personData, adminData } = await this.getDeliveryMemberAndAdminDataForNotifications(statusValue, memberLocationPreference, memberValue, newNotification);

    // Send notifications
    if (memberLocationPreference && memberLocationPreference.length > 0) {
      await pushNotification.sendMemberLocationPreferencePushNotification(
        memberLocationPreference,
        statusValue.DeliveryRequestId,
        history.locationFollowDescription,
        statusValue.requestType,
        statusValue.ProjectId,
        statusValue.id,
        6,
      );

      await notificationHelper.createMemberDeliveryLocationInAppNotification(
        DeliveryPersonNotification,
        statusValue.ProjectId,
        newNotification.id,
        memberLocationPreference,
        6,
      );
    }

    history.memberData = personData;
    history.adminData = adminData;

    await notificationHelper.createDeliveryPersonNotification(
      adminData,
      personData,
      projectDetails,
      newNotification,
      DeliveryPersonNotification,
      memberValue,
      loginUser,
      6,
      'approved a',
      'Delivery Request',
      `Delivery Booking (${statusValue.DeliveryId} - ${statusValue.description})`,
      statusValue.id,
    );

    // Get notification preferences and send push notifications
    const checkMemberNotification = await NotificationPreference.findAll({
      where: {
        ProjectId: statusValue.ProjectId,
        isDeleted: false,
      },
      attributes: [
        'id',
        'MemberId',
        'ProjectId',
        'ParentCompanyId',
        'NotificationPreferenceItemId',
        'instant',
        'dailyDigest',
      ],
      include: [
        {
          association: 'NotificationPreferenceItem',
          where: {
            id: 6,
            isDeleted: false,
          },
          attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
        },
      ],
    });

    history.notificationPreference = checkMemberNotification;
    await pushNotification.sendDeviceToken(history, 6, statusValue.ProjectId);

    if (memberLocationPreference && memberLocationPreference.length > 0) {
      history.memberData.push(...memberLocationPreference);
    }

    // Send guest user notifications
    await this.sendDeliveryGuestUserNotifications(statusValue, loginUser, 'Approved');

    return done(history, false);
  },

  // Helper function to get delivery member and admin data for notifications
  async getDeliveryMemberAndAdminDataForNotifications(statusValue, memberLocationPreference, memberValue, newNotification) {
    const locationFollowMembers = [];
    memberLocationPreference.forEach(async (element) => {
      locationFollowMembers.push(element.Member.id);
    });

    const NDRData = await DeliveryRequest.getNDRData({ id: statusValue.id });
    const bookingMemberDetails = [];
    NDRData.memberDetails.forEach(async (element) => {
      bookingMemberDetails.push(element.Member.id);
    });

    const personData = await DeliveryPerson.findAll({
      where: { DeliveryId: statusValue.id, isDeleted: false },
      include: [
        {
          association: 'Member',
          include: [
            {
              association: 'User',
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
          where: {
            [Op.and]: {
              RoleId: {
                [Op.notIn]: [1, 2],
              },
              id: { [Op.notIn]: locationFollowMembers },
            },
          },
          attributes: ['id', 'RoleId'],
        },
      ],
      attributes: ['id'],
    });

    const adminData = await Member.findAll({
      where: {
        [Op.and]: [
          { ProjectId: statusValue.ProjectId },
          { isDeleted: false },
          { id: { [Op.in]: bookingMemberDetails } },
          { id: { [Op.ne]: newNotification.MemberId } },
          { id: { [Op.notIn]: locationFollowMembers } },
        ],
      },
      include: [
        {
          association: 'User',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
      ],
      attributes: ['id'],
    });

    return { personData, adminData, locationFollowMembers, bookingMemberDetails };
  },

  // Helper function to send delivery guest user notifications
  async sendDeliveryGuestUserNotifications(statusValue, loginUser, status) {
    const exist = await DeliveryRequest.findOne({
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'isGuestUser'],
              include: [
                {
                  association: 'User',
                  attributes: [
                    'email',
                    'phoneCode',
                    'phoneNumber',
                    'firstName',
                    'lastName',
                  ],
                },
              ],
            },
          ],
        },
      ],
      where: { id: statusValue.id },
    });

    if (exist?.memberDetails) {
      const userDataMail = exist.memberDetails;
      for (const memberDetail of userDataMail) {
        const responsibleGuestUser = memberDetail.Member.isGuestUser;
        if (responsibleGuestUser) {
          const guestMailPayload = {
            email: memberDetail.Member.User.email,
            guestName: memberDetail.Member.User.firstName,
            content: `We would like to inform you that
        ${loginUser.firstName} ${loginUser.lastName} ${status} the Delivery Booking - ${statusValue.description}.`,
          };
          await MAILER.sendMail(
            guestMailPayload,
            'notifyGuestOnEdit',
            `Delivery Booking ${status} by ${loginUser.firstName} `,
            `Delivery Booking ${status}`,
            async (info, err) => {
              console.log(info, err);
            },
          );
        }
      }
    }
  },

  // Helper function to process delivery declined or delivered status notifications
  async processDeliveryDeclinedOrDeliveredStatusNotifications(params, done) {
    const { history, notification, statusValue, memberLocationPreference, memberValue, loginUser, updateData } = params;
    // Add common history properties
    history.firstName = loginUser.firstName;
    history.profilePic = loginUser.profilePic;
    history.createdAt = new Date();
    history.ProjectId = statusValue.ProjectId;

    const projectDetails = await Project.findByPk(statusValue.ProjectId);
    history.projectName = projectDetails.projectName;

    const newNotification = await Notification.createInstance(notification);

    // Get member and admin data
    const { personData, adminData } = await this.getDeliveryMemberAndAdminDataForNotifications(statusValue, memberLocationPreference, memberValue, newNotification);

    // Send notifications similar to approved status
    if (memberLocationPreference && memberLocationPreference.length > 0) {
      await pushNotification.sendMemberLocationPreferencePushNotification(
        memberLocationPreference,
        statusValue.DeliveryRequestId,
        history.locationFollowDescription,
        statusValue.requestType,
        statusValue.ProjectId,
        statusValue.id,
        6,
      );

      await notificationHelper.createMemberDeliveryLocationInAppNotification(
        DeliveryPersonNotification,
        statusValue.ProjectId,
        newNotification.id,
        memberLocationPreference,
        6,
      );
    }

    history.memberData = personData;
    history.adminData = adminData;

    // Get notification preferences and send push notifications
    const checkMemberNotification = await NotificationPreference.findAll({
      where: {
        ProjectId: statusValue.ProjectId,
        isDeleted: false,
      },
      attributes: [
        'id',
        'MemberId',
        'ProjectId',
        'ParentCompanyId',
        'NotificationPreferenceItemId',
        'instant',
        'dailyDigest',
      ],
      include: [
        {
          association: 'NotificationPreferenceItem',
          where: {
            id: 6,
            isDeleted: false,
          },
          attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
        },
      ],
    });

    history.notificationPreference = checkMemberNotification;

    await notificationHelper.createDeliveryPersonNotification(
      adminData,
      personData,
      projectDetails,
      newNotification,
      DeliveryPersonNotification,
      memberValue,
      loginUser,
      6,
      `${updateData.status.toLowerCase()}`,
      'Delivery Request',
      `Delivery Booking (${statusValue.DeliveryId} - ${statusValue.description})`,
      statusValue.id,
    );

    await pushNotification.sendDeviceToken(history, 6, statusValue.ProjectId);

    if (memberLocationPreference && memberLocationPreference.length > 0) {
      history.memberData.push(...memberLocationPreference);
    }

    // Send guest user notifications
    await this.sendDeliveryGuestUserNotifications(statusValue, loginUser, updateData.status);

    // Handle special case for 'Delivered' status
    if (updateData.status === 'Delivered') {
      const userEmails = await this.getMemberDetailData(history, memberLocationPreference);
      if (userEmails.length > 0) {
        await this.sendDeliverySpecialEmailNotifications(userEmails, memberValue, loginUser, statusValue);
      }
    }

    return done(history, false);
  },

  // Helper function to send special email notifications for delivered status
  async sendDeliverySpecialEmailNotifications(userEmails, memberValue, loginUser, statusValue) {
    for (const element of userEmails) {
      let name = element.firstName ? `${element.firstName} ${element.lastName}` : 'user';
      const time = moment(statusValue.deliveryStart).format('MM-DD-YYYY');

      const mailPayload = {
        userName: name,
        email: element.email,
        description: statusValue.description,
        userName1: `${loginUser.firstName} ${loginUser.lastName}`,
        deliveryID: statusValue.DeliveryId,
        DeliveryId: statusValue.DeliveryId,
        status_timestamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
        timestamp: time,
      };

      const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
        where: {
          MemberId: +element.MemberId,
          ProjectId: +statusValue.ProjectId,
          LocationId: +statusValue.LocationId,
          isDeleted: false,
        },
      });

      if (isMemberFollowLocation) {
        const memberNotification = await NotificationPreference.findOne({
          where: {
            MemberId: +element.MemberId,
            ProjectId: +statusValue.ProjectId,
            isDeleted: false,
          },
          include: [
            {
              association: 'NotificationPreferenceItem',
              where: {
                id: 10,
                isDeleted: false,
              },
            },
          ],
        });

        if (memberNotification?.instant) {
          await MAILER.sendMail(
            mailPayload,
            'deliveredDR',
            'Delivery Booking status updated',
            'Delivery Booking status updated',
            async (info, err) => {
              console.log(info, err);
            },
          );
        }

        if (memberNotification?.dailyDigest) {
          await this.createDailyDigestData({
            RoleId: +memberValue.RoleId,
            MemberId: +element.MemberId,
            ProjectId: +statusValue.ProjectId,
            ParentCompanyId: +statusValue.ParentCompanyId,
            loginUser,
            dailyDigestMessage: 'delivered a',
            requestType: 'Delivery Request',
            messages: `Delivery Booking (${statusValue.DeliveryId} - ${statusValue.description})`,
            requestId: statusValue.id,
          });
        }
      }
    }
  },
  async validateUploadInput(inputData) {
    const { file } = inputData;
    const ProjectId = +inputData.params.ProjectId;

    // Check member validity
    const memberDetail = await Member.findOne({
      where: [
        Sequelize.and(
          { UserId: inputData.user.id, ProjectId, isDeleted: false },
          Sequelize.or({ RoleId: [1, 2, 3, 4] })
        ),
      ],
    });

    if (!memberDetail) {
      return { valid: false, error: { message: 'Project does not exist or you are not a valid member.' } };
    }

    // Validate file
    if (!file?.originalname) {
      return { valid: false, error: { message: 'Please select a file.' } };
    }

    const splitValue = file.originalname.split('.');
    const extension = splitValue.pop();
    const fileName = splitValue.join('.');
    const firstSplitFileName = fileName.split('_');

    if (firstSplitFileName.length !== 3) {
      return { valid: false, error: { message: 'Invalid file' } };
    }

    const [projectFileName, projectId] = firstSplitFileName;
    const projectDetails = await Project.findByPk(ProjectId);

    if (
      projectDetails.projectName.toLowerCase() !== projectFileName.toLowerCase() ||
      +ProjectId !== +projectId
    ) {
      return { valid: false, error: { message: 'Invalid file' } };
    }

    if (extension !== 'xlsx') {
      return { valid: false, error: { message: 'Please choose valid file' } };
    }

    return { valid: true, file };
  },

  async bulkdeliveryRequestUpload(inputData, done) {
    try {
      await this.getDynamicModel(inputData);

      // Step 1: Validate user, project, and file
      const validation = await this.validateUploadInput(inputData);
      if (!validation.valid) {
        return done(null, validation.error);
      }

      const { file } = validation;

      // Step 2: Process Excel and create delivery requests
      const newWorkbook = new ExcelJS.Workbook();
      await newWorkbook.xlsx.readFile(file.path);
      const worksheet = newWorkbook.getWorksheet('Delivery Booking');

      this.createDeliveryRequestFile(worksheet, inputData, (resValue, error) => {
        if (!error) {
          return done(resValue, false);
        }
        return done(null, error);
      });
    } catch (e) {
      done(null, e);
    }
  },

  async createDeliveryRequestFile(deliveryRequestWorksheet, inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const existProjectId = inputData.params.ProjectId;
      const ProjectId = +existProjectId;
      const loginUser = inputData.user;
      const projectDetails = await Project.findByPk(ProjectId);
      let fileFormat = true;
      const worksheet = deliveryRequestWorksheet;
      const ndrRecords = [];
      let headers;
      if (worksheet) {
        worksheet.eachRow(async (rowData, rowNumber) => {
          const singleRowData = rowData.values;
          singleRowData.shift();
          if (rowNumber === 2) {
            headers = rowData.values;
          } else if (singleRowData.length > 0 && rowNumber >= 2) {
            const getRow = rowData.values;
            const description = getRow[2];
            if (description) {
              ndrRecords.push(rowData.values);
            }
          }
        });
        if (ndrRecords !== undefined && ndrRecords.length === 0) {
          return done(null, {
            message: 'Please upload proper document / Please fill Mandatory columns',
          });
        }
        if (inputData.file) {
          if (+headers.length === 15) {
            fileFormat = false;
          }
        }
        if (fileFormat) {
          const worker = new Worker(bulkNdrProcess);
          const object = stringify({
            projectDetails,
            loginUser,
            ndrRecords,
            ProjectId,
            inputData,
          });
          worker.postMessage(object);
          worker.on('message', (data) => {
            if (data === 'success') {
              const socketObject = {
                message: data,
                loginUserId: loginUser.id,
              };
              global.io.emit('bulkNdrNotification', socketObject);
              worker.terminate();
            }
          });
          worker.on('exit', (data) => {
            console.log('worker thread exit ', data);
          });
          done({ message: 'success' }, false);
        } else {
          done(null, { message: 'Invalid File Format' });
        }
      } else {
        done(null, { message: 'Invalid File' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async deleteQueuedNdr(input, done) {
    try {
      await this.getDynamicModel(input);
      const reqData = input.body;
      const inputData = {
        isDeleted: true,
      };
      if (reqData.queuedDeliveryRequestSelectAll) {
        const deleteValue = await DeliveryRequest.update(inputData, {
          where: {
            ProjectId: reqData.ProjectId,
            isQueued: true,
          },
        });
        done(deleteValue, false);
      } else {
        const { id } = input.body;
        const deleteValue = await DeliveryRequest.update(inputData, {
          where: { id: { [Op.in]: id } },
        });
        done(deleteValue, false);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async editMultipleDeliveryRequest(req) {
    try {
      const payload = req.body;
      if (!payload.deliveryRequestIds || payload.deliveryRequestIds.length === 0) {
        return { message: 'Please select Delivery Booking to update.!' };
      }

      await this.getDynamicModel(req);
      const loginUser = req.user;
      const { editedFields } = payload;
      const fieldFlags = this._parseEditedFields(editedFields);
      const projectSettingDetails = await Project.getProjectAndSettings({
        isDeleted: false,
        id: +payload.ProjectId,
      });

      let notificationsCreated = false;
      for (let mainIndex = 1; mainIndex <= payload.deliveryRequestIds.length; mainIndex += 1) {
        const deliveryRequestId = payload.deliveryRequestIds[mainIndex - 1];
        const result = await this._processSingleDeliveryRequest(
          payload,
          deliveryRequestId,
          loginUser,
          fieldFlags,
          projectSettingDetails,
          mainIndex,
          notificationsCreated // Skip notifications for subsequent requests
        );

        if (result.error) {
          return result;
        }

        notificationsCreated = true; // Mark notifications as created after first request

        if (mainIndex === payload.deliveryRequestIds.length) {
          return { success: true, data: result.history };
        }
      }
    } catch (e) {
      return e;
    }
  },

  _parseEditedFields(editedFields) {
    const fieldsArray = editedFields.split(',');
    return {
      responsiblePersonsEdited: fieldsArray.includes('Responsible Person'),
      escortEdited: fieldsArray.includes('Escort')
    };
  },

  async _processSingleDeliveryRequest(payload, deliveryRequestId, loginUser, fieldFlags, projectSettingDetails, mainIndex, skipNotifications = false) {
    const getDeliveryRequestDetail = await DeliveryRequest.findOne({
      where: { id: deliveryRequestId },
    });

    const getExistsSingleDeliveryRequest = await DeliveryRequest.getSingleDeliveryRequestData({
      id: +deliveryRequestId,
    });

    const validationResult = await this._validateDeliveryRequest(payload, deliveryRequestId, getExistsSingleDeliveryRequest);
    if (validationResult.error) {
      return validationResult;
    }

    const DeliverParam = await this._prepareDeliveryParameters(payload, getDeliveryRequestDetail, projectSettingDetails);

    if (getDeliveryRequestDetail?.recurrenceId) {
      await this._handleRecurrenceSeries(payload, getDeliveryRequestDetail, getExistsSingleDeliveryRequest, loginUser);
    }

    const idDetails = await DeliveryRequest.findOne({
      where: { id: getDeliveryRequestDetail.id },
    });

    if (!idDetails) {
      return { error: true, message: 'something Went wrong!!!' };
    }

    const existsDeliveryRequest = await DeliveryRequest.getSingleDeliveryRequestData({
      id: +idDetails.id,
    });

    const memberData = await Member.getBy({
      UserId: loginUser.id,
      ProjectId: +payload.ProjectId,
    });

    const history = this._createHistoryObject(idDetails, loginUser, memberData, payload.ProjectId);
    const notification = history;

    await this._setDeliveryApprovalStatus(DeliverParam, memberData, projectSettingDetails, idDetails);
    DeliverParam.recurrenceId = null;
    await DeliveryRequest.update(DeliverParam, { where: { id: idDetails.id } });

    await this._updateRelatedEntities(payload, idDetails, fieldFlags, history, loginUser);
    await this._handleVoidRequest(payload, deliveryRequestId, memberData, loginUser, getDeliveryRequestDetail);
    await this._handleStatusUpdate(payload, deliveryRequestId, memberData);

    const persons = await this._getResponsiblePersons(fieldFlags, payload, getExistsSingleDeliveryRequest);
    await Locations.findOne({
      where: {
        ProjectId: +payload.ProjectId,
        id: existsDeliveryRequest.LocationId,
      },
    });

    await this._updateHistoryAndNotification(history, notification, loginUser, getDeliveryRequestDetail, idDetails, existsDeliveryRequest, payload.ProjectId);

    if (!skipNotifications) {
      const newNotification = await Notification.createInstance(notification);
      await this._handleNotifications({ newNotification, history, memberData, loginUser, payload, existsDeliveryRequest, idDetails, persons });
    }

    const updatedDeliveryRequest = await DeliveryRequest.getSingleDeliveryRequestData({
      id: +idDetails.id,
    });

    await this._handleApprovalHistory(updatedDeliveryRequest, idDetails, memberData, payload.ProjectId, history, loginUser, getDeliveryRequestDetail);
    await this._updateEditDeliveryRequestHistory(updatedDeliveryRequest, existsDeliveryRequest, updatedDeliveryRequest, history, loginUser);

    await pushNotification.sendDeviceToken(history, 5, payload.ProjectId);

    const editedNDR = await DeliveryRequest.getNDRData({ id: idDetails.id });
    const { fieldsChanged, deliveryDateTimeChanged } = await this._analyzeChanges(editedNDR, existsDeliveryRequest);

    await this._handleStatusTransitionsData({ payload, editedNDR, existsDeliveryRequest, memberData, projectSettingDetails, fieldsChanged, deliveryDateTimeChanged, updatedDeliveryRequest, loginUser, getDeliveryRequestDetail });

    if (+memberData.RoleId === 4 || +memberData.RoleId === 3) {
      await this._handleEmailNotifications(history, payload, loginUser, memberData, existsDeliveryRequest, idDetails, getDeliveryRequestDetail);
    }

    return { history };
  },

  async _validateDeliveryRequest(payload, deliveryRequestId, getExistsSingleDeliveryRequest) {
    if (payload.deliveryStart && payload.deliveryEnd) {
      const requestArray = [{
        ProjectId: payload.ProjectId,
        deliveryStart: payload.deliveryStart,
        deliveryEnd: payload.deliveryEnd,
        id: deliveryRequestId,
      }];

      const projectSettingDetails = await Project.getProjectAndSettings({
        isDeleted: false,
        id: +payload.ProjectId,
      });

      const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
        requestArray,
        projectSettingDetails,
        'edit',
        getExistsSingleDeliveryRequest.gateDetails[0].Gate.id,
      );

      if (isOverlapping?.error) {
        return { error: true, message: isOverlapping.message };
      }
    }

    if ((payload.deliveryStart && !payload.deliveryEnd) || (!payload.deliveryStart && payload.deliveryEnd)) {
      return { error: true, message: 'Booking start or end time is missing' };
    }

    return { error: false };
  },

  async _prepareDeliveryParameters(payload, getDeliveryRequestDetail, projectSettingDetails) {
    const DeliverParam = {};
    let craneId = 0;

    if (payload.EquipmentId && payload.EquipmentId.length > 0) {
      if (payload.isAssociatedWithCraneRequest && !getDeliveryRequestDetail.isAssociatedWithCraneRequest) {
        craneId = await this._generateNewCraneRequestId(payload.ProjectId);
        DeliverParam.CraneRequestId = craneId;
        DeliverParam.requestType = 'deliveryRequestWithCrane';
        DeliverParam.cranePickUpLocation = payload.cranePickUpLocation;
        DeliverParam.craneDropOffLocation = payload.craneDropOffLocation;
        DeliverParam.isAssociatedWithCraneRequest = payload.isAssociatedWithCraneRequest;
      } else if (payload.isAssociatedWithCraneRequest && getDeliveryRequestDetail.isAssociatedWithCraneRequest) {
        DeliverParam.requestType = 'deliveryRequestWithCrane';
        DeliverParam.cranePickUpLocation = payload.cranePickUpLocation;
        DeliverParam.craneDropOffLocation = payload.craneDropOffLocation;
        DeliverParam.isAssociatedWithCraneRequest = payload.isAssociatedWithCraneRequest;
      } else if (!payload.isAssociatedWithCraneRequest) {
        DeliverParam.requestType = 'deliveryRequest';
        DeliverParam.cranePickUpLocation = null;
        DeliverParam.craneDropOffLocation = null;
        DeliverParam.isAssociatedWithCraneRequest = payload.isAssociatedWithCraneRequest;
      }
    }

    return DeliverParam;
  },

  async _generateNewCraneRequestId(projectId) {
    const lastData = await CraneRequest.findOne({
      where: { ProjectId: +projectId, isDeleted: false },
      order: [['CraneRequestId', 'DESC']],
    });

    const deliveryRequestList = await DeliveryRequest.findOne({
      where: {
        ProjectId: +projectId,
        isDeleted: false,
        isAssociatedWithCraneRequest: true,
      },
      order: [['CraneRequestId', 'DESC']],
    });

    let craneId = 1;
    if (deliveryRequestList) {
      if (lastData) {
        craneId = Math.max(deliveryRequestList.CraneRequestId, lastData.CraneRequestId) + 1;
      } else {
        craneId = deliveryRequestList.CraneRequestId + 1;
      }
    } else if (lastData) {
      craneId = lastData.CraneRequestId + 1;
    }

    return craneId;
  },

  async _handleRecurrenceSeries(payload, getDeliveryRequestDetail, getExistsSingleDeliveryRequest, loginUser) {
    const { recurrenceId } = getDeliveryRequestDetail;
    const recurrenceSeriesEndDate = getExistsSingleDeliveryRequest.recurrence.recurrenceEndDate;

    const previousSeriesRecurrenceEndDate = moment(getDeliveryRequestDetail.deliveryStart)
      .add(-1, 'days')
      .format('YYYY-MM-DD');
    const chosenTimezonePreviousSeriesRecurrenceEndDate = moment.tz(
      `${previousSeriesRecurrenceEndDate}  '00:00'`,
      'YYYY-MM-DD HH:mm',
      payload.timezone,
    );
    const utcPreviousSeriesRecurrenceEndDate = chosenTimezonePreviousSeriesRecurrenceEndDate
      .clone()
      .tz('UTC')
      .format('YYYY-MM-DD HH:mm:ssZ');

    const nextSeriesRecurrenceStartDate = moment(getDeliveryRequestDetail.deliveryStart)
      .add(1, 'days')
      .format('YYYY-MM-DD');
    const chosenTimezoneNextSeriesRecurrenceStartDate = moment.tz(
      `${nextSeriesRecurrenceStartDate}  '00:00'`,
      'YYYY-MM-DD HH:mm',
      payload.timezone,
    );
    const utcNextSeriesRecurrenceStartDate = chosenTimezoneNextSeriesRecurrenceStartDate
      .clone()
      .tz('UTC')
      .format('YYYY-MM-DD HH:mm:ssZ');

    const previousRecordInThisEventSeries = await DeliveryRequest.findAll({
      where: {
        recurrenceId,
        id: { [Op.lt]: getDeliveryRequestDetail.id },
        isDeleted: false,
      },
      order: [['id', 'DESC']],
    });

    const NextSeriesLastRecord = await DeliveryRequest.findAll({
      where: {
        recurrenceId,
        id: { [Op.gt]: getDeliveryRequestDetail.id },
        isDeleted: false,
      },
      order: [['id', 'DESC']],
    });

    if ((NextSeriesLastRecord && NextSeriesLastRecord.length > 0) || (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 0)) {
      await this._updateRecurrenceSeriesRecords({ previousRecordInThisEventSeries, NextSeriesLastRecord, utcPreviousSeriesRecurrenceEndDate, utcNextSeriesRecurrenceStartDate, recurrenceSeriesEndDate, payload, loginUser, getExistsSingleDeliveryRequest });
    }
  },

  async _updateRecurrenceSeriesRecords(params) {
    const {
      previousRecordInThisEventSeries,
      NextSeriesLastRecord,
      utcPreviousSeriesRecurrenceEndDate,
      utcNextSeriesRecurrenceStartDate,
      recurrenceSeriesEndDate,
      payload,
      loginUser,
      getExistsSingleDeliveryRequest,
    } = params;

    await this._handlePreviousSeriesUpdate(previousRecordInThisEventSeries, utcPreviousSeriesRecurrenceEndDate);
    await this._handleNextSeriesUpdate(
      previousRecordInThisEventSeries,
      NextSeriesLastRecord,
      utcNextSeriesRecurrenceStartDate,
      recurrenceSeriesEndDate,
      payload,
      loginUser,
      getExistsSingleDeliveryRequest
    );
  },

  async _handlePreviousSeriesUpdate(previousRecordInThisEventSeries, utcPreviousSeriesRecurrenceEndDate) {
    if (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length === 1) {
      await DeliveryRequest.update(
        { recurrenceId: null },
        { where: { id: previousRecordInThisEventSeries[0].id } },
      );
    }

    if (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 1) {
      await RequestRecurrenceSeries.update(
        { recurrenceEndDate: utcPreviousSeriesRecurrenceEndDate },
        { where: { id: previousRecordInThisEventSeries[0].recurrenceId } },
      );
    }
  },

  async _handleNextSeriesUpdate(
    previousRecordInThisEventSeries,
    NextSeriesLastRecord,
    utcNextSeriesRecurrenceStartDate,
    recurrenceSeriesEndDate,
    payload,
    loginUser,
    getExistsSingleDeliveryRequest
  ) {
    if (
      (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length === 1) ||
      (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length === 0)
    ) {
      if (NextSeriesLastRecord && NextSeriesLastRecord.length > 1) {
        await RequestRecurrenceSeries.update(
          { recurrenceStartDate: utcNextSeriesRecurrenceStartDate },
          { where: { id: NextSeriesLastRecord[0].recurrenceId } },
        );
      }
    } else if (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 1) {
      if (NextSeriesLastRecord && NextSeriesLastRecord.length > 1) {
        const recurrenceObject = {
          ProjectId: payload.ProjectId,
          ParentCompanyId: payload.ParentCompanyId,
          recurrence: getExistsSingleDeliveryRequest.recurrence.recurrence,
          repeatEveryCount: getExistsSingleDeliveryRequest.recurrence.repeatEveryCount,
          repeatEveryType: getExistsSingleDeliveryRequest.recurrence.repeatEveryType,
          days: getExistsSingleDeliveryRequest.recurrence.days,
          dateOfMonth: getExistsSingleDeliveryRequest.recurrence.dateOfMonth,
          chosenDateOfMonth: getExistsSingleDeliveryRequest.recurrence.chosenDateOfMonth,
          monthlyRepeatType: getExistsSingleDeliveryRequest.recurrence.monthlyRepeatType,
          requestType: getExistsSingleDeliveryRequest.requestType,
          createdBy: loginUser.id,
          recurrenceStartDate: utcNextSeriesRecurrenceStartDate,
          recurrenceEndDate: recurrenceSeriesEndDate,
        };
        const recurrenceSeries = await RequestRecurrenceSeries.createInstance(recurrenceObject);
        const newRecurrenceId = recurrenceSeries.id;

        for (const element of NextSeriesLastRecord) {
          await DeliveryRequest.update(
            { recurrenceId: newRecurrenceId },
            { where: { id: element.id } },
          );
        }
      }
    }
  },

  _createHistoryObject(idDetails, loginUser, memberData, projectId) {
    return {
      DeliveryRequestId: idDetails.id,
      DeliveryId: idDetails.DeliveryId,
      MemberId: memberData.id,
      type: 'edit',
      description: `${loginUser.firstName} ${loginUser.lastName} Edited this Delivery Booking.`,
      ProjectId: projectId,
    };
  },

  async _setDeliveryApprovalStatus(DeliverParam, memberData, projectSettingDetails, idDetails) {
    if (
      ((memberData.RoleId === 2 || memberData.RoleId === 1) && idDetails.status === 'Approved') ||
      memberData.isAutoApproveEnabled ||
      projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
    ) {
      DeliverParam.status = 'Approved';
      DeliverParam.approvedBy = memberData.id;
      DeliverParam.approved_at = new Date();
    }
  },

  async _updateRelatedEntities(payload, idDetails, fieldFlags, history, loginUser) {
    const condition = Sequelize.and({
      ProjectId: +payload.ProjectId,
      DeliveryId: idDetails.id,
    });

    const updateParam = {
      DeliveryId: idDetails.id,
      DeliveryCode: idDetails.DeliveryId,
      ProjectId: idDetails.ProjectId,
      isDeleted: false,
    };

    await this._updateEntityDetails(payload, fieldFlags, condition, updateParam, history, loginUser);
    await this._updateDeliveryDates(payload, idDetails);
  },

  async _updateEntityDetails(payload, fieldFlags, condition, updateParam, history, loginUser) {
    if (payload.companies?.length > 0) {
      await this._updateCompanies(payload.companies, condition, updateParam, history, loginUser);
    }

    if (fieldFlags.responsiblePersonsEdited && payload.persons?.length > 0) {
      await this._updatePersons(payload.persons, condition, updateParam, history, loginUser);
    }

    if (payload.define?.length > 0) {
      await this._updateDefine(payload.define, condition, updateParam, history, loginUser);
    }

    if (fieldFlags.escortEdited && payload.escort) {
      await DeliveryRequest.update(
        { escort: payload.escort },
        { where: { id: updateParam.DeliveryId } }
      );
    }

    if (payload.GateId) {
      await this._updateGates([payload.GateId], condition, updateParam, history, loginUser);
    }

    if (payload.EquipmentId?.length > 0) {
      await DeliveryRequest.update(
        { escort: payload.escort },
        { where: { id: updateParam.DeliveryId } }
      );
      await this._updateEquipment(payload.EquipmentId, condition, updateParam, history, loginUser);
    }
  },

  async _updateDeliveryDates(payload, idDetails) {
    if (payload.deliveryStart && payload.deliveryEnd) {
      const startDate = new Date(payload.deliveryStart).getTime();
      const currentDate = new Date().getTime();
      const endDate = new Date(payload.deliveryEnd).getTime();

      if (startDate > currentDate && endDate > currentDate) {
        const DeliverParam = {
          deliveryStart: payload.deliveryStart,
          deliveryEnd: payload.deliveryEnd,
        };
        await DeliveryRequest.update(DeliverParam, { where: { id: idDetails.id } });
      }
    }
  },


  async _updateCompanies(companies, condition, updateParam, history, loginUser) {
    const existCompanies = await DeliverCompany.findAll({ where: condition });
    const deletedCompany = existCompanies.filter((e) => {
      return companies.indexOf(e.CompanyId) === -1 && e.isDeleted === false;
    });
    await DeliverCompany.update({ isDeleted: true }, { where: condition });

    const addedCompany = [];
    for (const element of companies) {
      const index = existCompanies.findIndex((item) => item.CompanyId === element);
      const companyParam = { ...updateParam, CompanyId: element };

      if (index !== -1) {
        await DeliverCompany.update(companyParam, { where: { id: existCompanies[index].id } });
        if (existCompanies[index].isDeleted !== false) {
          addedCompany.push(existCompanies[index]);
        }
      } else {
        const newCompanyData = await DeliverCompany.createInstance(companyParam);
        addedCompany.push(newCompanyData);
      }
    }


    this.updateCompanyHistory(addedCompany, deletedCompany, history, loginUser);
  },

  async _updatePersons(persons, condition, updateParam, history, loginUser) {
    const existPerson = await DeliveryPerson.findAll({ where: condition });
    const deletedPerson = existPerson.filter((e) => {
      return persons.indexOf(e.MemberId) === -1 && e.isDeleted === false;
    });
    await DeliveryPerson.update({ isDeleted: true }, { where: condition });

    const addedPerson = [];
    for (const element of persons) {
      const index = existPerson.findIndex((item) => item.MemberId === element);
      const memberParam = { ...updateParam, MemberId: element };

      if (index !== -1) {
        await DeliveryPerson.update(memberParam, { where: { id: existPerson[index].id } });
        if (existPerson[index].isDeleted !== false) {
          addedPerson.push(existPerson[index]);
        }
      } else {
        const newPersonData = await DeliveryPerson.createInstance(memberParam);
        addedPerson.push(newPersonData);
      }
    }

    this.updatePersonHistory(addedPerson, deletedPerson, history, loginUser);
  },

  async _updateDefine(define, condition, updateParam, history, loginUser) {
    const existDefine = await DeliverDefine.findAll({ where: condition });
    const deletedDefine = existDefine.filter((e) => {
      return define.indexOf(e.DeliverDefineWorkId) === -1 && e.isDeleted === false;
    });
    await DeliverDefine.update({ isDeleted: true }, { where: condition });

    const addedDefineData = [];
    for (const element of define) {
      const index = existDefine.findIndex((item) => item.DeliverDefineWorkId === element);
      const defineParam = { ...updateParam, DeliverDefineWorkId: element };

      if (index !== -1) {
        await DeliverDefine.update(defineParam, { where: { id: existDefine[index].id } });
        if (existDefine[index].isDeleted !== false) {
          addedDefineData.push(existDefine[index]);
        }
      } else {
        const newDefineData = await DeliverDefine.createInstance(defineParam);
        addedDefineData.push(newDefineData);
      }
    }

    this.updateDefineHistory(addedDefineData, deletedDefine, history, loginUser);
  },

  async _updateGates(gates, condition, updateParam, history, loginUser) {
    const existGate = await DeliverGate.findAll({ where: condition });
    const deletedGate = existGate.filter((e) => {
      return gates.indexOf(e.GateId) === -1 && e.isDeleted === false;
    });
    await DeliverGate.update({ isDeleted: true }, { where: condition });

    const addedGate = [];
    for (const element of gates) {
      const index = existGate.findIndex((item) => item.GateId === element);
      const gateParam = { ...updateParam, GateId: element };

      if (index !== -1) {
        await DeliverGate.update(gateParam, { where: { id: existGate[index].id } });
        if (existGate[index].isDeleted !== false) {
          addedGate.push(existGate[index]);
        }
      } else {
        const newGateData = await DeliverGate.createInstance(gateParam);
        addedGate.push(newGateData);
      }
    }


    this.updateGateHistory(addedGate, deletedGate, history, loginUser);
  },

  async _updateEquipment(equipments, condition, updateParam, history, loginUser) {
    const existEquipment = await DeliverEquipment.findAll({ where: condition });
    const deletedEquipment = existEquipment.filter((e) => {
      return equipments.indexOf(e.EquipmentId) === -1 && e.isDeleted === false;
    });
    await DeliverEquipment.update({ isDeleted: true }, { where: condition });

    const addedEquipment = [];
    for (const element of equipments) {
      const index = existEquipment.findIndex((item) => item.EquipmentId === element);
      const equipmentParam = { ...updateParam, EquipmentId: element };

      if (index !== -1) {
        await DeliverEquipment.update(equipmentParam, { where: { id: existEquipment[index].id } });
        if (existEquipment[index].isDeleted !== false) {
          addedEquipment.push(existEquipment[index]);
        }
      } else {
        const newEquipmentData = await DeliverEquipment.createInstance(equipmentParam);
        addedEquipment.push(newEquipmentData);
      }
    }

    this.updateEquipmentHistory(addedEquipment, deletedEquipment, history, loginUser);
  },

  async _handleVoidRequest(payload, deliveryRequestId, memberData, loginUser, getDeliveryRequestDetail) {
    if (payload.void === true) {
      const existVoid = await VoidList.findOne({
        where: Sequelize.and({ DeliveryRequestId: deliveryRequestId }),
      });

      if (!existVoid) {
        const voidcreate = {
          DeliveryRequestId: deliveryRequestId,
          ProjectId: payload.ProjectId,
          ParentCompanyId: payload.ParentCompanyId,
        };
        const newVoidData = await VoidList.createInstance(voidcreate);

        if (newVoidData) {
          const object = {
            ProjectId: payload.ProjectId,
            MemberId: memberData.id,
            DeliveryRequestId: deliveryRequestId,
            isDeleted: false,
            type: 'void',
            description: `${loginUser.firstName} ${loginUser.lastName} Voided the Delivery Booking, ${getDeliveryRequestDetail.description}`,
          };
          await DeliverHistory.createInstance(object);
        }
      } else {
        return { error: true, message: 'Delivery Booking already is in void list.' };
      }
    }
  },

  async _handleStatusUpdate(payload, deliveryRequestId, memberData) {
    if (payload.status) {
      if (memberData.RoleId === 2 || memberData.RoleId === 1) {
        const DeliverParam2 = {
          status: payload.status,
          approvedBy: memberData.id,
          approved_at: new Date(),
        };
        await DeliveryRequest.update(DeliverParam2, { where: { id: deliveryRequestId } });
      }
    }
  },

  async _getResponsiblePersons(fieldFlags, payload, getExistsSingleDeliveryRequest) {
    if (fieldFlags.responsiblePersonsEdited) {
      return payload.persons;
    } else {
      const responsiblePersonsList = [];
      getExistsSingleDeliveryRequest.memberDetails.forEach((element) => {
        responsiblePersonsList.push(element.Member.id);
      });
      return responsiblePersonsList;
    }
  },

  async _updateHistoryAndNotification(history, notification, loginUser, getDeliveryRequestDetail, idDetails, existsDeliveryRequest, projectId) {
    const locationChosen = await Locations.findOne({
      where: {
        ProjectId: +projectId,
        id: existsDeliveryRequest.LocationId,
      },
    });

    history.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Delivery Booking, ${getDeliveryRequestDetail.description}`;
    history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Updated the Delivery Booking, ${getDeliveryRequestDetail.description}. Location: ${locationChosen.locationPath}.`;
    history.firstName = loginUser.firstName;
    history.profilePic = loginUser.profilePic;
    history.createdAt = new Date();

    const projectDetails = await Project.findByPk(+projectId);
    history.projectName = projectDetails.projectName;

    notification.ProjectId = idDetails.ProjectId;
    notification.title = `Delivery Booking Updated by ${loginUser.firstName} ${loginUser.lastName}`;

    if (existsDeliveryRequest?.recurrence?.recurrence) {
      notification.recurrenceType = `${existsDeliveryRequest.recurrence.recurrence} From ${moment(existsDeliveryRequest.recurrence.recurrenceStartDate).format('MM/DD/YYYY')} to ${moment(existsDeliveryRequest.recurrence.recurrenceEndDate).format('MM/DD/YYYY')}`;
    }
    notification.requestType = 'deliveryRequest';
  },

  async _handleNotifications(params) {
    const { newNotification, history, memberData, loginUser, payload, existsDeliveryRequest, idDetails, persons } = params;
    const memberLocationPreference = await LocationNotificationPreferences.findAll({
      where: {
        ProjectId: payload.ProjectId,
        LocationId: +existsDeliveryRequest.LocationId,
        follow: true,
      },
      include: [{
        association: 'Member',
        attributes: ['id', 'RoleId'],
        where: { [Op.and]: [{ id: { [Op.ne]: memberData.id } }] },
        include: [{ association: 'User', attributes: ['id', 'firstName', 'lastName', 'email'] }],
      }],
    });

    const locationFollowMembers = memberLocationPreference.map(element => element.Member.id);

    const adminData = await Member.findAll({
      where: {
        [Op.and]: [
          { ProjectId: payload.ProjectId },
          { isDeleted: false },
          { id: { [Op.in]: persons } },
          { id: { [Op.ne]: newNotification.MemberId } },
          { id: { [Op.notIn]: locationFollowMembers } },
        ],
      },
      include: [{ association: 'User', attributes: ['id', 'firstName', 'lastName', 'email'] }],
      attributes: ['id', 'RoleId'],
    });

    if (memberLocationPreference && memberLocationPreference.length > 0) {
      await pushNotification.sendMemberLocationPreferencePushNotification(
        memberLocationPreference,
        idDetails.DeliveryRequestId,
        history.locationFollowDescription,
        'deliveryRequest',
        payload.ProjectId,
        idDetails.id,
        5,
      );
      await notificationHelper.createMemberDeliveryLocationInAppNotification(
        DeliveryPersonNotification,
        payload.ProjectId,
        newNotification.id,
        memberLocationPreference,
        5,
      );
    }

    history.adminData = adminData;
    if (memberLocationPreference && memberLocationPreference.length > 0) {
      history.memberData = [...memberLocationPreference];
    }

    await notificationHelper.createDeliveryPersonNotification(
      adminData,
      [],
      await Project.findByPk(+payload.ProjectId),
      newNotification,
      DeliveryPersonNotification,
      memberData,
      loginUser,
      5,
      'updated a',
      'Delivery Request',
      `delivery Booking (${idDetails.DeliveryId} - ${idDetails.description})`,
      idDetails.id,
    );

    const checkMemberNotification = await NotificationPreference.findAll({
      where: { ProjectId: payload.ProjectId, isDeleted: false },
      attributes: ['id', 'MemberId', 'ProjectId', 'ParentCompanyId', 'NotificationPreferenceItemId', 'instant', 'dailyDigest'],
      include: [{
        association: 'NotificationPreferenceItem',
        where: { id: 5, isDeleted: false },
        attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
      }],
    });

    history.notificationPreference = checkMemberNotification;
  },

  async _handleApprovalHistory(updatedDeliveryRequest, idDetails, memberData, projectId, history, loginUser, getDeliveryRequestDetail) {
    if (updatedDeliveryRequest.status === 'Approved' && idDetails.status !== 'Approved' && idDetails.isQueued === false) {
      const object = {
        ProjectId: projectId,
        MemberId: memberData.id,
        DeliveryRequestId: updatedDeliveryRequest.id,
        isDeleted: false,
        type: 'approved',
        description: history.description,
      };
      await DeliverHistory.createInstance(object);
    }
  },

  async _analyzeChanges(editedNDR, existsDeliveryRequest) {
    let tagsUpdated = false;
    let fieldsChanged = false;

    // Check for changes in various entities
    const entityChecks = [
      { edited: editedNDR.defineWorkDetails, existing: existsDeliveryRequest.defineWorkDetails, idField: 'id' },
      { edited: editedNDR.gateDetails, existing: existsDeliveryRequest.gateDetails, idField: 'Gate.id' },
      { edited: editedNDR.equipmentDetails, existing: existsDeliveryRequest.equipmentDetails, idField: 'Equipment.id' },
      { edited: editedNDR.companyDetails, existing: existsDeliveryRequest.companyDetails, idField: 'Company.id' },
      { edited: editedNDR.memberDetails, existing: existsDeliveryRequest.memberDetails, idField: 'Member.id' },
    ];

    for (const check of entityChecks) {
      if (check.edited.length > 0 && check.existing.length > 0) {
        const added = check.edited.filter(el => !check.existing.find(element => this._getNestedValue(element, check.idField) === this._getNestedValue(el, check.idField)));
        const deleted = check.existing.filter(el => !check.edited.find(element => this._getNestedValue(element, check.idField) === this._getNestedValue(el, check.idField)));

        if (added.length > 0 || deleted.length > 0) {
          tagsUpdated = true;
        }
      }
    }

    // Check for field changes
    const fieldComparisons = [
      'description', 'CraneRequestId', 'LocationId', 'requestType', 'vehicleDetails', 'notes',
      'isAssociatedWithCraneRequest', 'escort', 'craneDropOffLocation', 'cranePickUpLocation',
      'recurrence', 'chosenDateOfMonth', 'dateOfMonth', 'monthlyRepeatType', 'days',
      'repeatEveryType', 'repeatEveryCount'
    ];

    for (const field of fieldComparisons) {
      if (existsDeliveryRequest[field] !== editedNDR[field]) {
        fieldsChanged = true;
        break;
      }
    }

    if (tagsUpdated) {
      fieldsChanged = true;
    }

    const deliveryDateTimeChanged = +new Date(existsDeliveryRequest.deliveryStart) !== +new Date(editedNDR.deliveryStart) ||
      +new Date(existsDeliveryRequest.deliveryEnd) !== +new Date(editedNDR.deliveryEnd);

    return { tagsUpdated, fieldsChanged, deliveryDateTimeChanged };
  },

  _getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  },

  async _handleStatusTransitionsData(params) {
    const { payload, editedNDR, existsDeliveryRequest, memberData, projectSettingDetails, fieldsChanged, deliveryDateTimeChanged, updatedDeliveryRequest, loginUser, getDeliveryRequestDetail } = params;
    if (!payload.status) {
      const statusHandlers = {
        'Delivered': () => this._handleDeliveredStatus(editedNDR, memberData, projectSettingDetails, fieldsChanged, deliveryDateTimeChanged),
        'Approved': () => this._handleApprovedStatus(editedNDR, memberData, projectSettingDetails, fieldsChanged, deliveryDateTimeChanged),
        'Expired': () => this._handleExpiredOrDeclinedStatus({ editedNDR, memberData, projectSettingDetails, fieldsChanged, deliveryDateTimeChanged, updatedDeliveryRequest, loginUser, getDeliveryRequestDetail, ProjectId: payload.ProjectId }),
        'Declined': () => this._handleExpiredOrDeclinedStatus({ editedNDR, memberData, projectSettingDetails, fieldsChanged, deliveryDateTimeChanged, updatedDeliveryRequest, loginUser, getDeliveryRequestDetail, ProjectId: payload.ProjectId }),
        'Pending': () => this._handlePendingStatus({ editedNDR, memberData, projectSettingDetails, fieldsChanged, deliveryDateTimeChanged, updatedDeliveryRequest, loginUser, getDeliveryRequestDetail, ProjectId: payload.ProjectId }),
      };

      const handler = statusHandlers[existsDeliveryRequest.status];
      if (handler) {
        await handler();
      }
    } else if (memberData.RoleId === 2 || memberData.RoleId === 1) {
      const DeliverParam2 = {
        status: payload.status,
        approvedBy: memberData.id,
        approved_at: new Date(),
      };
      await DeliveryRequest.update(DeliverParam2, { where: { id: editedNDR.id } });
    }
  },

  async _handleDeliveredStatus(editedNDR, memberData, projectSettingDetails, fieldsChanged, deliveryDateTimeChanged) {
    if ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId === 2 || memberData.isAutoApproveEnabled || projectSettingDetails.ProjectSettings.isAutoApprovalEnabled) {
      await DeliveryRequest.update(
        { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
        { where: { id: editedNDR.id } },
      );
    } else if ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId !== 2 && !memberData.isAutoApproveEnabled && !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled) {
      await DeliveryRequest.update({ status: 'Pending' }, { where: { id: editedNDR.id } });
    }
  },

  async _handleApprovedStatus(editedNDR, memberData, projectSettingDetails, fieldsChanged, deliveryDateTimeChanged) {
    if ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId !== 2 && !memberData.isAutoApproveEnabled && !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled) {
      await DeliveryRequest.update({ status: 'Pending' }, { where: { id: editedNDR.id } });
    } else if ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId === 2 || memberData.isAutoApproveEnabled || projectSettingDetails.ProjectSettings.isAutoApprovalEnabled) {
      await DeliveryRequest.update(
        { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
        { where: { id: editedNDR.id } },
      );
    }
  },

  async _handleExpiredOrDeclinedStatus(params) {
    const { editedNDR, memberData, projectSettingDetails, fieldsChanged, deliveryDateTimeChanged, updatedDeliveryRequest, loginUser, getDeliveryRequestDetail, projectId } = params
    if ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId !== 2 && !memberData.isAutoApproveEnabled && !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled) {
      await DeliveryRequest.update({ status: 'Pending' }, { where: { id: editedNDR.id } });
    } else if ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId === 2 || memberData.isAutoApproveEnabled || projectSettingDetails.ProjectSettings.isAutoApprovalEnabled) {
      const isStatusDataUpdated = await DeliveryRequest.update(
        { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
        { where: { id: editedNDR.id } },
      );
      if (isStatusDataUpdated) {
        const object = {
          ProjectId: projectId,
          MemberId: memberData.id,
          DeliveryRequestId: updatedDeliveryRequest.id,
          isDeleted: false,
          type: 'approved',
          description: `${loginUser.firstName} ${loginUser.lastName} Approved the Delivery Booking, ${getDeliveryRequestDetail.description}`,
        };
        await DeliverHistory.createInstance(object);
      }
    }
  },

  async _handlePendingStatus(params) {
    const { editedNDR, memberData, projectSettingDetails, fieldsChanged, deliveryDateTimeChanged, updatedDeliveryRequest, loginUser, getDeliveryRequestDetail, projectId } = params;
    if ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId !== 2 && !memberData.isAutoApproveEnabled && !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled) {
      await DeliveryRequest.update({ status: 'Pending' }, { where: { id: editedNDR.id } });
    } else if ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId === 2 || memberData.isAutoApproveEnabled || projectSettingDetails.ProjectSettings.isAutoApprovalEnabled) {
      const isStatusDataUpdated = await DeliveryRequest.update(
        { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
        { where: { id: editedNDR.id } },
      );
      if (isStatusDataUpdated) {
        const object = {
          ProjectId: projectId,
          MemberId: memberData.id,
          DeliveryRequestId: updatedDeliveryRequest.id,
          isDeleted: false,
          type: 'approved',
          description: `${loginUser.firstName} ${loginUser.lastName} Approved the Delivery Request, ${getDeliveryRequestDetail.description}`,
        };
        await DeliverHistory.createInstance(object);
      }
    }
  },

  async _handleEmailNotifications(history, payload, loginUser, memberData, existsDeliveryRequest, idDetails, getDeliveryRequestDetail) {
    const userEmails = await this.getMemberDetailData(history, []);
    if (userEmails.length > 0) {
      for (const element of userEmails) {
        if (element.RoleId === 2) {
          await this._sendEmailToAdmin(element, loginUser, memberData, payload, existsDeliveryRequest, idDetails, getDeliveryRequestDetail);
        }
      }

      const exist2 = await DeliveryRequest.findOne({
        include: [{
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{
            association: 'Member',
            attributes: ['id', 'isGuestUser'],
            include: [{
              association: 'User',
              attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            }],
          }],
        }],
        where: { isDeleted: false, id: +getDeliveryRequestDetail.id },
      });

      if (exist2?.memberDetails) {
        for (const memberDetail of exist2.memberDetails) {
          const responsibleGuestUser = memberDetail.Member.isGuestUser;
          if (responsibleGuestUser) {
            const guestMailPayload = {
              email: responsibleGuestUser.User.email,
              guestName: responsibleGuestUser.User.firstName,
              content: `We would like to inform you that ${loginUser.firstName} ${loginUser.lastName} has updated a delivery booking ${exist2.description}.`,
            };
            await MAILER.sendMail(
              guestMailPayload,
              'notifyGuestOnEdit',
              `Delivery Booking updated by ${loginUser.firstName} ${loginUser.lastName}`,
              `Delivery Booking updated by ${loginUser.firstName} ${loginUser.lastName}`,
              async (info, err) => { console.log(info, err); },
            );
          }
        }
      }
    }
  },

  async updateEditDeliveryRequestHistory(
    userEditedDeliveryRequestData,
    existsDeliveryRequestData,
    updatedDeliveryRequest,
    history,
    loginUser,
  ) {
    const historyObject = history;

    await this.updateBasicFieldsHistory(
      userEditedDeliveryRequestData,
      existsDeliveryRequestData,
      updatedDeliveryRequest,
      historyObject,
      loginUser
    );

    await this.updateCraneAndVehicleHistory(
      userEditedDeliveryRequestData,
      existsDeliveryRequestData,
      historyObject,
      loginUser
    );

    await this.updateListBasedHistory(
      updatedDeliveryRequest,
      existsDeliveryRequestData,
      historyObject,
      loginUser
    );
  },

  async updateBasicFieldsHistory(
    userEditedData,
    existingData,
    updatedData,
    historyObject,
    loginUser
  ) {
    if (userEditedData.description?.toLowerCase() !== existingData.description?.toLowerCase()) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Description ${userEditedData.description}`;
      DeliverHistory.createInstance(historyObject);
    }

    if (userEditedData.LocationId !== existingData.LocationId && updatedData?.location?.locationPath) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Location, ${updatedData.location.locationPath}`;
      DeliverHistory.createInstance(historyObject);
    }

    if (new Date(userEditedData.deliveryStart).getTime() !== new Date(existingData.deliveryStart).getTime()) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Delivery Start Date ${userEditedData.deliveryStart}`;
      DeliverHistory.createInstance(historyObject);
    }

    if (new Date(userEditedData.deliveryEnd).getTime() !== new Date(existingData.deliveryEnd).getTime()) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Delivery End Date ${userEditedData.deliveryEnd}`;
      DeliverHistory.createInstance(historyObject);
    }

    this.updateNotesHistory(userEditedData, existingData, historyObject, loginUser);
  },

  updateNotesHistory(userEditedData, existingData, historyObject, loginUser) {
    if (userEditedData.notes) {
      if (!existingData.notes || existingData.notes.toLowerCase() !== userEditedData.notes.toLowerCase()) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Notes ${existingData.notes}`;
        DeliverHistory.createInstance(historyObject);
      }
    } else if (existingData.notes) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Removed the Notes ${existingData.notes}`;
      DeliverHistory.createInstance(historyObject);
    }
  },

  async updateCraneAndVehicleHistory(userEditedData, existingData, historyObject, loginUser) {
    this.updateFieldHistory("cranePickUpLocation", "Picking From", userEditedData, existingData, historyObject, loginUser);
    this.updateFieldHistory("craneDropOffLocation", "Picking To", userEditedData, existingData, historyObject, loginUser);
    this.updateFieldHistory("vehicleDetails", "Vechicle Details", userEditedData, existingData, historyObject, loginUser);

    if (userEditedData.escort !== existingData.escort) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} ${userEditedData.escort ? "enabled" : "disabled"} the Escort`;
      DeliverHistory.createInstance(historyObject);
    }
  },

  updateFieldHistory(fieldName, label, userEditedData, existingData, historyObject, loginUser) {
    if (userEditedData[fieldName] !== existingData[fieldName]) {
      if (userEditedData[fieldName]) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the ${label} ${userEditedData[fieldName]}`;
      } else {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Removed the ${label} ${existingData[fieldName]}`;
      }
      DeliverHistory.createInstance(historyObject);
    }
  },

  async updateListBasedHistory(updatedData, existingData, historyObject, loginUser) {
    await this.updateList("memberDetails", "member", (element) =>
      `${element.Member.User.firstName} ${element.Member.User.lastName}`, updatedData, existingData, historyObject, loginUser);

    await this.updateList("companyDetails", "company", (element) =>
      element.Company.companyName, updatedData, existingData, historyObject, loginUser);

    await this.updateList("defineWorkDetails", "Definable feature of work", (element) =>
      element.DeliverDefineWork.DFOW, updatedData, existingData, historyObject, loginUser);

    await this.updateList("equipmentDetails", "Equipment", (element) =>
      element.Equipment.equipmentName, updatedData, existingData, historyObject, loginUser);

    await this.updateList("gateDetails", "Gate", (element) =>
      element.Gate.gateName, updatedData, existingData, historyObject, loginUser);
  },

  async updateList(field, label, getName, updatedData, existingData, historyObject, loginUser) {
    if (updatedData[field].length > 0 && existingData[field].length > 0) {
      const addedItems = updatedData[field].filter(el => !existingData[field].some(e => e.id === el.id));
      const deletedItems = existingData[field].filter(el => !updatedData[field].some(e => e.id === el.id));

      for (const element of addedItems) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the ${label} ${getName(element)}`;
        await DeliverHistory.createInstance(historyObject);
      }

      for (const element of deletedItems) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the ${label} ${getName(element)}`;
        await DeliverHistory.createInstance(historyObject);
      }
    }
  },

  // Helper function to add unique email to array
  addUniqueEmailToArray(emailArray, existAdminData, userInfo) {
    const index = existAdminData.findIndex((adminNew) => adminNew.email === userInfo.email);
    if (index === -1) {
      existAdminData.push({ email: userInfo.email });
      emailArray.push(userInfo);
    }
  },

  // Refactored getMemberDetailData function with reduced duplication
  async getMemberDetailData(data, memberLocationPreference) {
    const emailArray = [];
    const existAdminData = [];

    // Process member data
    if (data.memberData !== undefined) {
      data.memberData.forEach((element) => {
        const userInfo = {
          email: element.Member.User.email,
          firstName: element.Member.User.firstName,
          lastName: element.Member.User.lastName,
          UserId: element.Member.User.id,
          MemberId: element.Member.id,
          RoleId: element.Member.RoleId,
        };
        this.addUniqueEmailToArray(emailArray, existAdminData, userInfo);
      });
    }

    // Process admin data
    if (data.adminData !== undefined) {
      data.adminData.forEach((element) => {
        const userInfo = {
          email: element.User.email,
          firstName: element.User.firstName,
          lastName: element.User.lastName,
          UserId: element.User.id,
          MemberId: element.id,
          RoleId: element.RoleId,
        };
        this.addUniqueEmailToArray(emailArray, existAdminData, userInfo);
      });
    }

    // Process member location preference data
    if (memberLocationPreference !== undefined && memberLocationPreference.length > 0) {
      memberLocationPreference.forEach((element) => {
        const userInfo = {
          email: element.Member.User.email,
          firstName: element.Member.User.firstName,
          lastName: element.Member.User.lastName,
          UserId: element.Member.User.id,
          MemberId: element.Member.id,
          RoleId: element.Member.RoleId,
        };
        this.addUniqueEmailToArray(emailArray, existAdminData, userInfo);
      });
    }

    return emailArray;
  },
  // prettier-ignore
  async createDailyDigestData(
    params
  ) {
    const {
      MemberId,
      ProjectId,
      ParentCompanyId,
      loginUser,
      dailyDigestMessage,
      requestType,
      messages,
      requestId, } = params;
    const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
    const encryptedRequestId = cryptr.encrypt(requestId);
    const encryptedMemberId = cryptr.encrypt(MemberId);
    let imageUrl;
    let link;
    let height;
    if (requestType === 'Delivery Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/87758081-8ccd-4e4d-a4eb-6257466876da.png';
      link = 'delivery-request';
      height = 'height:18px;';
    }
    if (requestType === 'Crane Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/ab400721-520c-4f6f-941c-ac07acc28809.png';
      link = 'crane-request';
      height = 'height:32px;';
    }
    if (requestType === 'Concrete Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/c43e4c58-4c4d-44ff-b78c-fe9948ea53eb.png';
      link = 'concrete-request';
      height = 'height:18px;';
    }
    const object = {
      description: `<div>
  <ul style="list-style-type:none;padding:0px;border-bottom:1px dashed #E3E3E3;">
    <li style="display:flex;">
      <img src="${imageUrl}" alt="message-icon" style="${height}">
        <p style="margin:0px;font-size:12px;padding-left:10px;">
          <a href="#" ta
        rget="" style="text-decoration: none;color:#4470FF;">
      ${loginUser.firstName}  ${loginUser.lastName}
          </a>
          ${dailyDigestMessage}
      <a href = "${process.env.BASE_URL}/${link}?requestId=${encryptedRequestId}&memberId=${encryptedMemberId} " style="text - decoration: none; color:#4470FF; " >${messages}</a>
  <span style="color:#707070;">on ${moment().utc().format('MMMM DD')} at ${moment()
          .utc()
          .format('hh:mm A zz')}</span>
        </p>
    </li>
  </ul>
</div> `,
      MemberId,
      ProjectId,
      isSent: false,
      isDeleted: false,
      ParentCompanyId,
    };
    await DigestNotification.create(object);
  },
  async sendEmailNotificationToUser(
    history,
    memberDetails,
    loginUser,
    newDeliverData,
    deliveryData,
    memberLocationPreference,
  ) {
    const userEmails = await this.getMemberDetailData(history, memberLocationPreference);
    if (userEmails.length > 0) {
      userEmails.forEach(async (element) => {
        let name;
        if (!element.firstName) {
          name = 'user';
        } else {
          name = `${element.firstName} ${element.lastName} `;
        }
        if (+element.MemberId !== +memberDetails.id) {
          const memberRole = await Role.findOne({
            where: {
              id: memberDetails.RoleId,
              isDeleted: false,
            },
          });
          const time = moment(newDeliverData.deliveryStart).format('MM-DD-YYYY');
          const mailPayload = {
            userName: name,
            email: element.email,
            deliveryId: newDeliverData.DeliveryId,
            description: newDeliverData.description,
            timestamp: time,
            createdTimestamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
            content: ` ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has created the delivery booking ${newDeliverData.DeliveryId}.Please see below for more details`,
          };
          const memberNotification = await NotificationPreference.findOne({
            where: {
              MemberId: +element.MemberId,
              ProjectId: +deliveryData.ProjectId,
              isDeleted: false,
            },
            include: [
              {
                association: 'NotificationPreferenceItem',
                where: {
                  id: 12,
                  isDeleted: false,
                },
              },
            ],
          });
          if (memberNotification?.instant) {
            await MAILER.sendMail(
              mailPayload,
              'deliveryRequestCreated',
              `Delivery Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} `,
              `Delivery Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} `,
              async (info, err) => {
                console.log(info, err);
              },
            );
          }
          if (memberNotification?.dailyDigest) {
            await this.createDailyDigestData({
              RoleId: +memberDetails.RoleId,
              MemberId: +element.MemberId,
              ProjectId: +deliveryData.ProjectId,
              ParentCompanyId: +deliveryData.ParentCompanyId,
              loginUser,
              dailyDigestMessage: 'created a',
              requestType: 'Delivery Request',
              messages: `delivery Booking(${newDeliverData.DeliveryId} - ${newDeliverData.description})`,
              requestId: newDeliverData.id,
            });
          }
        }
      });
    }
    return true;
  },
  async createDailyDigestDataApproval(
    params
  ) {
    const {
      MemberId,
      ProjectId,
      ParentCompanyId,
      loginUser,
      dailyDigestMessage,
      requestType,
      messages,
      messages2,
      requestId } = params
    const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
    const encryptedRequestId = cryptr.encrypt(requestId);
    const encryptedMemberId = cryptr.encrypt(MemberId);
    let imageUrl;
    let link;
    let height;
    if (requestType === 'Delivery Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/87758081-8ccd-4e4d-a4eb-6257466876da.png';
      link = 'delivery-request';
      height = 'height:18px;';
    }
    if (requestType === 'Crane Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/ab400721-520c-4f6f-941c-ac07acc28809.png';
      link = 'crane-request';
      height = 'height:32px;';
    }
    if (requestType === 'Concrete Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/c43e4c58-4c4d-44ff-b78c-fe9948ea53eb.png';
      link = 'concrete-request';
      height = 'height:18px;';
    }
    const object = {
      description: `<div>
  <ul style="list-style-type:none;padding:0px;border-bottom:1px dashed #E3E3E3;">
    <li style="display:flex;">
      <img src="${imageUrl}" alt="message-icon" style="${height}">
        <p style="margin:0px;font-size:12px;padding-left:10px;">
          <a href="
        #" target="" style="text-decoration: none;color:#4470FF;">
          ${loginUser.firstName}  ${loginUser.lastName}
          </a>
          ${dailyDigestMessage}
      <a href = "${process.env.BASE_URL
        }/ ${link}?requestId = ${encryptedRequestId}& memberId=${encryptedMemberId} " style="text - decoration: none; color:#4470FF; " >
          ${messages}
        </a>
  ${messages2}
<span style="color:#707070;">on ${moment().utc().format('MMMM DD')} at ${moment()
          .utc()
          .format('hh:mm A zz')}</span>
        </p>
    </li>
  </ul>
</div> `,
      MemberId,
      ProjectId,
      isSent: false,
      isDeleted: false,
      ParentCompanyId,
    };
    await DigestNotification.create(object);
  },
  async convertTimezoneToUtc(date, timezone, time) {
    const chosenTimezoneDeliveryStart = moment.tz(`${date} ${time}`, 'MM/DD/YYYY HH:mm', timezone);
    const utcDate = chosenTimezoneDeliveryStart.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
    return utcDate;
  },
  async createCopyofDeliveryRequest(dataInSeries, payload, dates, loginUser, newRecurrenceId) {
    const { memberDetails, projectDetails } = await this._validateAndGetDeliveryRequestData(payload, loginUser, dates);
    const eventsArray = await this._generateRecurringDeliveryEvents(dataInSeries, payload, dates, memberDetails, projectDetails, newRecurrenceId);
    await this._saveDeliveryRequestsAndRelatedData(eventsArray, payload, memberDetails, loginUser);
  },

  async _validateAndGetDeliveryRequestData(payload, loginUser, dates) {
    const memberDetails = await Member.getBy({
      UserId: loginUser.id,
      ProjectId: payload.ProjectId,
      isActive: true,
      isDeleted: false,
    });
    const projectDetails = await Project.getProjectAndSettings({
      isDeleted: false,
      id: +payload.ProjectId,
    });

    // Validate delivery window constraints
    if (payload.recurrence && dates && dates.length > 0) {
      const startDate = await this.compareDeliveryDateWithDeliveryWindowDate(
        dates[0],
        payload.deliveryStartTime,
        payload.timezone,
        projectDetails.ProjectSettings.deliveryWindowTime,
        projectDetails.ProjectSettings.deliveryWindowTimeUnit,
      );
      const endDate = await this.compareDeliveryDateWithDeliveryWindowDate(
        dates[0],
        payload.deliveryEndTime,
        payload.timezone,
        projectDetails.ProjectSettings.deliveryWindowTime,
        projectDetails.ProjectSettings.deliveryWindowTimeUnit,
      );

      if (startDate || endDate) {
        throw new Error(
          `Bookings can not be submitted within ${projectDetails.ProjectSettings.deliveryWindowTime} ${projectDetails.ProjectSettings.deliveryWindowTimeUnit} prior to the event`,
        );
      }
    }

    return { memberDetails, projectDetails };
  },

  async _getNextAvailableIds(projectId) {
    // Get next delivery ID
    const lastIdValue = await DeliveryRequest.findOne({
      where: { ProjectId: projectId, isDeleted: false },
      order: [['DeliveryId', 'DESC']],
    });
    let id = 0;
    const newValue = JSON.parse(JSON.stringify(lastIdValue));
    if (newValue && newValue.DeliveryId !== null && newValue.DeliveryId !== undefined) {
      id = newValue.DeliveryId;
    }

    // Get next crane ID
    let lastData = {};
    lastData = await CraneRequest.findOne({
      where: { ProjectId: +projectId, isDeleted: false },
      order: [['CraneRequestId', 'DESC']],
    });
    const deliveryRequestList = await DeliveryRequest.findOne({
      where: {
        ProjectId: +projectId,
        isDeleted: false,
        isAssociatedWithCraneRequest: true,
      },
      order: [['CraneRequestId', 'DESC']],
    });
    if (deliveryRequestList) {
      if (lastData) {
        if (deliveryRequestList.CraneRequestId > lastData.CraneRequestId) {
          lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
        }
      } else {
        lastData = {};
        lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
      }
    }
    if (lastData) {
      const data = lastData.CraneRequestId;
      lastData.CraneRequestId = 0;
      lastData.CraneRequestId = data + 1;
    } else {
      lastData = {};
      lastData.CraneRequestId = 1;
    }
    let craneId = 0;
    const newId = JSON.parse(JSON.stringify(lastData));
    if (newId && newId.CraneRequestId !== null && newId.CraneRequestId !== undefined) {
      craneId = newId.CraneRequestId;
    }

    return { id, craneId };
  },

  async _createDeliveryParam(params) {
    const { payload, date, id, craneId, memberDetails, projectDetails, roleDetails, accountRoleDetails, newRecurrenceId } = params;
    const deliverParam = {
      description: payload.description,
      escort: payload.escort,
      vehicleDetails: payload.vehicleDetails,
      notes: payload.notes,
      DeliveryId: id,
      deliveryStart: await this.convertTimezoneToUtc(
        date,
        payload.timezone,
        payload.deliveryStartTime,
      ),
      deliveryEnd: await this.convertTimezoneToUtc(
        date,
        payload.timezone,
        payload.deliveryEndTime,
      ),
      ProjectId: payload.ProjectId,
      createdBy: memberDetails.id,
      isAssociatedWithCraneRequest: payload.isAssociatedWithCraneRequest,
      requestType: payload.requestType,
      cranePickUpLocation: payload.cranePickUpLocation,
      craneDropOffLocation: payload.craneDropOffLocation,
      recurrenceId: newRecurrenceId,
    };

    if (payload.requestType === 'deliveryRequestWithCrane') {
      deliverParam.CraneRequestId = craneId;
    }

    if (
      memberDetails.RoleId === roleDetails.id ||
      memberDetails.RoleId === accountRoleDetails.id ||
      memberDetails.isAutoApproveEnabled ||
      projectDetails.ProjectSettings.isAutoApprovalEnabled
    ) {
      deliverParam.status = 'Approved';
      deliverParam.approvedBy = memberDetails.id;
      deliverParam.approved_at = new Date();
    }

    return deliverParam;
  },

  async _generateRecurringDeliveryEvents(
    dataInSeries,
    payload,
    dates,
    memberDetails,
    projectDetails,
    newRecurrenceId
  ) {
    const recurrenceType = dataInSeries?.recurrence?.recurrence;
    if (!recurrenceType) return [];

    // Run independent async lookups in parallel.
    const [
      { id: nextDeliveryId, craneId: nextCraneId },
      roleDetails,
      accountRoleDetails,
    ] = await Promise.all([
      this._getNextAvailableIds(memberDetails.ProjectId),
      Role.getBy('Project Admin'),
      Role.getBy('Account Admin'),
    ]);

    const baseArgs = {
      dataInSeries,
      dates,
      payload,
      memberDetails,
      projectDetails,
      roleDetails,
      accountRoleDetails,
      newRecurrenceId,
      startingDeliveryId: nextDeliveryId,
      startingCraneId: nextCraneId,
    };

    switch (recurrenceType) {
      case 'Daily':
        return await this._generateDailyEvents(baseArgs);

      case 'Weekly':
        return await this._generateWeeklyEvents(baseArgs);

      case 'Monthly':
      case 'Yearly':
        return await this._generateMonthlyOrYearlyEvents({
          ...baseArgs,
          recurrenceType,
        });

      default:
        return [];
    }
  },


  async _generateDailyEvents(params) {
    let { eventsArray, dataInSeries, dates, payload, memberDetails, projectDetails, roleDetails, accountRoleDetails, newRecurrenceId, id, craneId } = params;
    let dailyIndex = 0;

    while (dailyIndex < dates.length) {
      const data = dates[dailyIndex];
      if (
        moment(data).isBetween(moment(dates[0]), moment(dates[dates.length - 1]), null, '[]') ||
        moment(data).isSame(dates[0]) ||
        moment(data).isSame(dates[dates.length - 1])
      ) {
        id += 1;
        craneId += 1;
        const deliverParam = await this._createDeliveryParam({
          payload,
          data,
          id,
          craneId,
          memberDetails,
          projectDetails,
          roleDetails,
          accountRoleDetails,
          newRecurrenceId
        });
        eventsArray.push(deliverParam);
        dailyIndex += +dataInSeries.recurrence.repeatEveryCount;
      }
    }
  },

  async _generateWeeklyEvents(params) {
    let { eventsArray, dataInSeries, dates, payload, memberDetails, projectDetails, roleDetails, accountRoleDetails, newRecurrenceId, id, craneId } = params;
    const startDayWeek = moment(dates[0]).startOf('week');
    const endDayWeek = moment(dates[dates.length - 1]).endOf('week');
    const range1 = momentRange.range(moment(startDayWeek), moment(endDayWeek));
    const totalDaysOfRecurrence = Array.from(range1.by('day'));

    let count;
    let weekIncrement;
    if (+dataInSeries.recurrence.repeatEveryCount > 1) {
      count = +dataInSeries.recurrence.repeatEveryCount - 1;
      weekIncrement = 7;
    } else {
      count = 1;
      weekIncrement = 0;
    }

    for (let indexba = 0; indexba < totalDaysOfRecurrence.length; indexba += weekIncrement * count) {
      const totalLength = indexba + 6;
      for (let indexb = indexba; indexb <= totalLength; indexb += 1) {
        const data = totalDaysOfRecurrence[indexb];
        if (
          data &&
          !moment(data).isBefore(dates[0]) &&
          !moment(data).isAfter(dates[dates.length - 1])
        ) {
          const day = moment(data).format('dddd');
          const indexVal = dataInSeries.recurrence.days.includes(day);
          if (indexVal) {
            id += 1;
            craneId += 1;
            const date = moment(data).format('MM/DD/YYYY');
            const deliverParam = await this._createDeliveryParam({
              payload,
              date,
              id,
              craneId,
              memberDetails,
              projectDetails,
              roleDetails,
              accountRoleDetails,
              newRecurrenceId
            });
            eventsArray.push(deliverParam);
          }
        }
      }
    }
  },


  async _generateMonthlyOrYearlyEvents(paramsData) {
    let { eventsArray, dataInSeries, dates, payload, memberDetails, projectDetails, roleDetails, accountRoleDetails, newRecurrenceId, id, craneId, recurrenceType } = paramsData;
    const monthIncrement = recurrenceType === 'Yearly' ? 12 : 1;

    let startDate1 = moment(dates[0]);
    const endDate1 = moment(dates[dates.length - 1]).endOf('month');
    const allMonthsInPeriod = [];
    while (startDate1.isBefore(endDate1)) {
      allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
      startDate1 = startDate1.add(monthIncrement, 'month');
    }

    for (let k = 0; k < allMonthsInPeriod.length + 1; k += +dataInSeries.recurrence.repeatEveryCount) {
      const currentMonthDates = Array.from(
        { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
        (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days')
      );

      await this._processMonthEvents({
        eventsArray, currentMonthDates, dataInSeries, dates, payload,
        memberDetails, projectDetails, roleDetails, accountRoleDetails,
        newRecurrenceId, nextId: () => { id += 1; return id; }, nextCraneId: () => { craneId += 1; return craneId; }
      });
    }
  },

  async _processMonthEvents(paramsData) {
    const {
      eventsArray, currentMonthDates, dataInSeries, dates, payload,
      memberDetails, projectDetails, roleDetails, accountRoleDetails,
      newRecurrenceId, nextId, nextCraneId
    } = paramsData;

    const isValidDate = (date) =>
      moment(date).isBetween(moment(dates[0]), moment(dates[dates.length - 1]), null, '[]') ||
      moment(date).isSame(dates[0]) ||
      moment(date).isSame(dates[dates.length - 1]);

    const pushEvent = async (date) => {
      const deliverParam = await this._createDeliveryParam({
        payload,
        date: moment(date).format('MM/DD/YYYY'),
        id: nextId(),
        craneId: nextCraneId(),
        memberDetails,
        projectDetails,
        roleDetails,
        accountRoleDetails,
        newRecurrenceId
      });
      eventsArray.push(deliverParam);
    };

    if (dataInSeries.recurrence.chosenDateOfMonth) {
      const getDate = currentMonthDates.filter(
        (value) => moment(value).format('DD') === dataInSeries.recurrence.dateOfMonth
      );

      if (getDate.length === 1 && isValidDate(getDate[0])) {
        await pushEvent(getDate[0].toDate());
      }
      return;
    }

    // Handle monthlyRepeatType logic (first/second/etc.)
    const [week, day] = dataInSeries.recurrence.monthlyRepeatType.split(' ').map((s) => s.toLowerCase());
    const chosenDay = moment(currentMonthDates[0]).startOf('month').day(day);
    const getAllDays = [];

    if (chosenDay.date() > 7) chosenDay.add(7, 'd');
    const month = chosenDay.month();
    while (month === chosenDay.month()) {
      getAllDays.push(chosenDay.format('YYYY-MM-DD'));
      chosenDay.add(7, 'd');
    }

    const weekMap = { first: 0, second: 1, third: 2, fourth: 3, last: getAllDays.length - 1 };
    const finalDay = getAllDays[weekMap[week] || 0];

    if (isValidDate(finalDay)) {
      await pushEvent(finalDay);
    }
  },


  async _saveDeliveryRequestsAndRelatedData(eventsArray, payload, memberDetails, loginUser) {
    if (eventsArray.length > 0) {
      for (const event of eventsArray) {
        const newDeliverData = await DeliveryRequest.createInstance(event);
        await this._saveRelatedDeliveryData(newDeliverData, payload);
        await this._saveDeliveryHistory(newDeliverData, payload, memberDetails, loginUser);
      }
    }
  },

  async _saveRelatedDeliveryData(newDeliverData, payload) {
    const { companies, persons, define } = payload;
    const gates = [payload.GateId];
    const equipments = payload.EquipmentId;
    const updateParam = {
      DeliveryId: newDeliverData.id,
      DeliveryCode: newDeliverData.DeliveryId,
      ProjectId: payload.ProjectId,
    };

    for (const element of companies) {
      await DeliverCompany.createInstance({ ...updateParam, CompanyId: element });
    }
    for (const element of gates) {
      await DeliverGate.createInstance({ ...updateParam, GateId: element });
    }
    for (const element of equipments) {
      await DeliverEquipment.createInstance({ ...updateParam, EquipmentId: element });
    }
    for (const element of persons) {
      await DeliveryPerson.createInstance({ ...updateParam, MemberId: element });
    }
    for (const element of define) {
      await DeliverDefine.createInstance({ ...updateParam, DeliverDefineWorkId: element });
    }
  },

  async _saveDeliveryHistory(newDeliverData, payload, memberDetails, loginUser) {
    const history = {
      DeliveryRequestId: newDeliverData.id,
      DeliveryId: newDeliverData.DeliveryId,
      MemberId: memberDetails.id,
      isDeleted: false,
      ProjectId: payload.ProjectId,
      type: 'create',
      description: `${loginUser.firstName} ${loginUser.lastName} Created Delivery Booking, ${payload.description}.`,
    };
    await DeliverHistory.createInstance(history);

    if (newDeliverData.status === 'Approved') {
      const approvedHistory = {
        ProjectId: payload.ProjectId,
        MemberId: memberDetails.id,
        DeliveryRequestId: newDeliverData.id,
        isDeleted: false,
        type: 'approved',
        description: `${loginUser.firstName} ${loginUser.lastName} Approved Delivery Booking, ${payload.description}.`,
      };
      await DeliverHistory.createInstance(approvedHistory);
    }
  },

  async checkDeliveryConflictsWithAlreadyScheduled(requestsArray, type, gateId) {
    if (requestsArray && requestsArray.length > 0) {
      const deliveryStartDateArr = [];
      const deliveryEndDateArr = [];
      const requestIds = [];
      const recurrenceIds = [];
      requestsArray.forEach((data) => {
        deliveryStartDateArr.push(new Date(data.deliveryStart));
        deliveryEndDateArr.push(new Date(data.deliveryEnd));
        if (type === 'edit') {
          if (data.id) {
            requestIds.push(data.id);
          }
          if (data.recurrenceId) {
            recurrenceIds.push(data.recurrenceId);
          }
        }
      });
      let condition = {
        ProjectId: requestsArray[0].ProjectId,
        status: {
          [Op.notIn]: ['Delivered', 'Expired'],
        },
        isDeleted: false,
      };
      if (type === 'edit') {
        if (recurrenceIds && recurrenceIds.length > 0) {
          condition = {
            ...condition,
            recurrenceId: {
              [Op.notIn]: recurrenceIds,
            },
          };
        } else {
          condition = {
            ...condition,
            id: {
              [Op.notIn]: requestIds,
            },
          };
        }
      }
      const isDeliveryBookingOverlapping = await DeliveryRequest.findAll({
        where: {
          ...condition,
          [Op.or]: [
            {
              [Op.or]: deliveryStartDateArr.map((date) => ({
                deliveryStart: { [Op.lt]: date },
                deliveryEnd: { [Op.gt]: date },
              })),
            },
            {
              [Op.or]: deliveryEndDateArr.map((date) => ({
                deliveryStart: { [Op.lt]: date },
                deliveryEnd: { [Op.gt]: date },
              })),
            },
          ],
        },
        include: [
          {
            association: 'gateDetails',
            where: {
              isDeleted: false,
              isActive: true,
              GateId: { [Op.eq]: +gateId },
            },
          },
        ],
      });
      const overlappingIds = []
      isDeliveryBookingOverlapping.forEach(data => {
        overlappingIds.push(data.id)
      })
      const voidData = await VoidList.findAll({
        where: {
          DeliveryRequestId: {
            [Op.in]: overlappingIds
          }
        }
      })
      const checkOverlappingInVoid = isDeliveryBookingOverlapping.map(data => data.id);

      const allPresentInVoidList = checkOverlappingInVoid.every(id =>
        voidData.some(voidEntry => voidEntry.DeliveryRequestId === id)
      );

      return !allPresentInVoidList;
    }
  },
  async checkDoubleBookingAllowedOrNot(eventsArray, projectDetails, type, gateId) {
    if (!projectDetails.ProjectSettings.deliveryAllowOverlappingBooking) {
      const checkBookingOverlapping = await this.checkDeliveryConflictsWithAlreadyScheduled(
        eventsArray,
        type,
        gateId,
      );
      if (checkBookingOverlapping) {
        return {
          error: true,
          message:
            'This booking clashes with another booking. Overlapping is disabled by the administrator.',
        };
      }
    }
    if (
      projectDetails.ProjectSettings &&
      !projectDetails.ProjectSettings.deliveryAllowOverlappingCalenderEvents
    ) {
      const checkCalenderEventsOverlapping =
        await concreteRequestService.checkCalenderEventsOverlappingWithBooking(
          eventsArray,
          'delivery',
          type,
        );
      if (checkCalenderEventsOverlapping) {
        return {
          error: true,
          message:
            'This booking clashes with a scheduled calendar event. Overlapping is disabled by the administrator',
        };
      }
    }
    return {
      error: false,
      message: '',
    };
  },

  async createNewRecurrenceEvents(
    deliveryData,
    memberDetails,
    totalDays1,
    roleDetails,
    accountRoleDetails,
    projectDetails,
    done
  ) {
    try {
      const occurrenceDates = this.calculateRecurrenceDates(deliveryData, totalDays1);

      if (!occurrenceDates.length) {
        return done([], false);
      }

      const eventsArray = this.mapDatesToEvents(
        occurrenceDates,
        deliveryData,
        memberDetails,
        roleDetails,
        accountRoleDetails,
        projectDetails
      );

      if (!eventsArray.length) {
        return done([], false);
      }

      // Keep identical overlap check semantics
      const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
        eventsArray,
        projectDetails,
        'edit',
        deliveryData.GateId
      );

      if (isOverlapping?.error) {
        return done(null, { message: isOverlapping.message });
      }

      return done(eventsArray, false);
    } catch (err) {
      return done(null, { message: err?.message || 'Failed to create recurrence events' });
    }
  },

  calculateDailyRecurrence(deliveryData, totalDays, start, end) {
    const results = [];
    const step = +deliveryData.repeatEveryCount || 1;
    let idx = 0;
    while (idx < totalDays.length) {
      const d = moment(totalDays[idx]);
      if (d.isBetween(start, end.clone().add(1, 'day'), null, '[]')) {
        results.push(d);
        idx += step;
      } else {
        idx += 1;
      }
    }
    return results;
  },

  calculateWeeklyRecurrence(deliveryData, start, end) {
    const results = [];
    const repeatWeeks = +deliveryData.repeatEveryCount || 1;
    const wantedDays = (deliveryData.days || []).map(d => d.toLowerCase());
    if (!wantedDays.length) return results;

    let cursor = start.clone().startOf('week');
    const endWeek = end.clone().endOf('week');

    while (cursor.isSameOrBefore(endWeek)) {
      for (let i = 0; i < 7; i++) {
        const dayMoment = cursor.clone().add(i, 'days');
        if (
          dayMoment.isBetween(start, end.clone().add(1, 'day'), null, '[]') &&
          wantedDays.includes(dayMoment.format('dddd').toLowerCase())
        ) {
          results.push(dayMoment);
        }
      }
      cursor.add(repeatWeeks, 'weeks');
    }
    return results;
  },

  getChosenDateTargets(cursor, start, end, dateOfMonth) {
    const results = [];
    const target = cursor.clone().date(Number(dateOfMonth));
    if (target.isValid() && target.isBetween(start, end.clone().add(1, 'day'), null, '[]')) {
      results.push(target);
    }
    return results;
  },

  getNthPatternTargets(cursor, start, end, nthPattern) {
    const results = [];
    const [weekWord, dayWord] = nthPattern.toLowerCase().split(' ');
    if (!weekWord || !dayWord) return results;

    let firstDay = cursor.clone().startOf('month').day(dayWord);
    if (firstDay.date() > 7) firstDay.add(7, 'days');

    const daysInPattern = [];
    const targetMonth = firstDay.month();
    while (firstDay.month() === targetMonth) {
      daysInPattern.push(firstDay.clone());
      firstDay.add(7, 'days');
    }

    let index = 0;
    if (weekWord === 'second') index = 1;
    else if (weekWord === 'third') index = 2;
    else if (weekWord === 'fourth') index = 3;
    else if (weekWord === 'last') index = daysInPattern.length - 1;

    const target = daysInPattern[index];
    if (target?.isBetween(start, end.clone().add(1, 'day'), null, '[]')) {
      results.push(target);
    }
    return results;
  },

  calculateMonthlyOrYearlyRecurrence(deliveryData, start, end, monthsStep) {
    const results = [];
    const chosenDateMode = !!deliveryData.chosenDateOfMonth;
    const dateOfMonth = deliveryData.dateOfMonth;
    const nthPattern = deliveryData.monthlyRepeatType;
    const repeatMonths = monthsStep || +deliveryData.repeatEveryCount || 1;

    let cursor = start.clone().startOf('month');
    while (cursor.isSameOrBefore(end)) {
      if (chosenDateMode) {
        results.push(...this.getChosenDateTargets(cursor, start, end, dateOfMonth));
      } else if (nthPattern) {
        results.push(...this.getNthPatternTargets(cursor, start, end, nthPattern));
      }
      cursor.add(repeatMonths, 'months');
    }
    return results;
  },

  calculateRecurrenceDates(deliveryData, totalDays1) {
    const start = moment(deliveryData.deliveryStart);
    const end = moment(deliveryData.recurrenceEndDate);
    const totalDays = Array.isArray(totalDays1) ? totalDays1 : [];

    switch (deliveryData.recurrence) {
      case 'Daily':
        return this.calculateDailyRecurrence(deliveryData, totalDays, start, end);
      case 'Weekly':
        return this.calculateWeeklyRecurrence(deliveryData, start, end);
      case 'Monthly':
        return this.calculateMonthlyOrYearlyRecurrence(deliveryData, start, end, +deliveryData.repeatEveryCount || 1);
      case 'Yearly':
        return calculateMonthlyOrYearlyRecurrence(deliveryData, start, end, 12);
      default:
        return [];
    }
  },

  mapDatesToEvents(
    dateMoments,
    deliveryData,
    memberDetails,
    roleDetails,
    accountRoleDetails,
    projectDetails
  ) {
    const {
      deliveryStartTime,
      deliveryEndTime,
      timezone,
      recurrenceId,
    } = deliveryData;

    return dateMoments.map(d => {
      const dateStr = d.format('MM/DD/YYYY');

      const startLocal = moment.tz(
        `${dateStr} ${deliveryStartTime}`,
        'MM/DD/YYYY HH:mm',
        timezone
      );
      const endLocal = moment.tz(
        `${dateStr} ${deliveryEndTime}`,
        'MM/DD/YYYY HH:mm',
        timezone
      );

      const deliveryStart = startLocal.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
      const deliveryEnd = endLocal.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');

      const DeliverParam = {
        description: deliveryData.description,
        escort: deliveryData.escort,
        vehicleDetails: deliveryData.vehicleDetails,
        notes: deliveryData.notes,
        DeliveryId: null,
        deliveryStart,
        deliveryEnd,
        ProjectId: deliveryData.ProjectId,
        createdBy: memberDetails.id,
        isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
        requestType: deliveryData.requestType,
        cranePickUpLocation: deliveryData.cranePickUpLocation,
        craneDropOffLocation: deliveryData.craneDropOffLocation,
        CraneRequestId: null,
        recurrenceId,
        LocationId: deliveryData.LocationId,
        OriginationAddress: deliveryData.originationAddress,
        vehicleType: deliveryData.vehicleType,
      };

      if (
        memberDetails.RoleId === roleDetails.id ||
        memberDetails.RoleId === accountRoleDetails.id ||
        memberDetails.isAutoApproveEnabled ||
        projectDetails.ProjectSettings.isAutoApprovalEnabled
      ) {
        DeliverParam.status = 'Approved';
        DeliverParam.approvedBy = memberDetails.id;
        DeliverParam.approved_at = new Date();
      }

      return DeliverParam;
    });
  },

  async createEquipmentMapping(payload) {
    try {
      if (payload) {
        payload.EquipmentId = JSON.stringify(payload.EquipmentId)
        const createMapping = await EquipmentMapping.create(payload);
        if (createMapping) {
          return true;
        }
      }
    } catch (err) {
      console.log(err, "error in create Mapping")
      return err
    }
  },

  async deleteEquipmentMapping(payload) {
    try {
      if (payload) {
        const condition = {
          where: {
            [Op.and]: {
              GateId: payload.GateId,
              LocationId: payload.LocationId,
              DeliveryId: payload.DeliveryId,
            },
          },
        };
        const deleteMapping = await EquipmentMapping.destroy(condition);
        return !!deleteMapping;
      }
    } catch (err) {
      console.log(err, "error in create Mapping")
      return err
    }
  },

  async findEquipmentMapping(payload) {
    console.log(payload, "payload====")
    const eventTimeZone = await TimeZone.findOne({
      where: {
        isDeleted: false,
        id: +payload.TimeZoneId,
      },
      attributes: [
        'id',
        'location',
        'isDayLightSavingEnabled',
        'timeZoneOffsetInMinutes',
        'dayLightSavingTimeInMinutes',
        'timezone',
      ],
    });
    if (!eventTimeZone) {
      return done(null, { message: 'Provide a valid timezone' });
    }
    const chosenTimezoneDeliveryStart = moment.tz(
      `${payload.deliveryStart} ${payload.startPicker}`,
      'YYYY MM DD 00:00:00 HH:mm',
      eventTimeZone.timezone,
    );
    const chosenTimezoneDeliveryEnd = moment.tz(
      `${payload.deliveryEnd} ${payload.endPicker}`,
      'YYYY MM DD 00:00:00 HH:mm',
      eventTimeZone.timezone,
    );
    const deliveryStart = chosenTimezoneDeliveryStart
      .clone()
      .tz('UTC')
      .format('YYYY-MM-DD HH:mm:ssZ');
    const deliveryEnd = chosenTimezoneDeliveryEnd
      .clone()
      .tz('UTC')
      .format('YYYY-MM-DD HH:mm:ssZ');
    const results = await EquipmentMapping.findAll({
      where: {
        [Op.and]: [
          {
            [Op.or]: [
              {
                startTime: {
                  [Op.between]: [deliveryStart, deliveryEnd],
                },
              },
              {
                endTime: {
                  [Op.between]: [deliveryStart, deliveryEnd],
                },
              },
            ],
          },
          Sequelize.literal(
            `string_to_array(trim(both '[]' from "EquipmentId"), ',')::int[] && ARRAY[${payload.EquipmentId.join(',')}]::int[]`
          ),
        ],
      },
    });

    return results.length === 0;
  },

  async _updateRelatedCompanies(idDetails, deliveryData) {
    const companies = deliveryData.companies;
    const updateParam = {
      DeliveryId: idDetails.id,
      DeliveryCode: idDetails.DeliveryId,
      ProjectId: deliveryData.ProjectId,
      isDeleted: false,
      isActive: true,
    };
    const condition = Sequelize.and({
      ProjectId: deliveryData.ProjectId,
      DeliveryId: idDetails.id,
    });
    const existCompanies = await DeliverCompany.findAll({ where: condition });
    const addedCompany = [];
    for (const element of companies) {
      const index = existCompanies.findIndex((item) => item.CompanyId === element);
      const companyParam = { ...updateParam, CompanyId: element };
      if (index !== -1) {
        await DeliverCompany.update(companyParam, {
          where: { id: existCompanies[index].id },
        });
        if (existCompanies[index].isDeleted !== false) {
          addedCompany.push(existCompanies[index]);
        }
      } else {
        const newCompanyData = await DeliverCompany.createInstance(companyParam);
        addedCompany.push(newCompanyData);
      }
    }
    return addedCompany;
    // Optionally: handle deleted companies if needed
  },

  async _updateRelatedGates(idDetails, deliveryData) {
    const gates = [deliveryData.GateId];
    const updateParam = {
      DeliveryId: idDetails.id,
      DeliveryCode: idDetails.DeliveryId,
      ProjectId: deliveryData.ProjectId,
      isDeleted: false,
      isActive: true,
    };
    const condition = Sequelize.and({
      ProjectId: deliveryData.ProjectId,
      DeliveryId: idDetails.id,
    });
    const existGate = await DeliverGate.findAll({ where: condition });
    const addedGate = [];
    for (const element of gates) {
      const index = existGate.findIndex((item) => item.GateId === element);
      const gateParam = { ...updateParam, GateId: element };
      if (index !== -1) {
        await DeliverGate.update(gateParam, {
          where: { id: existGate[index].id },
        });
        if (existGate[index].isDeleted !== false) {
          addedGate.push(existGate[index]);
        }
      } else {
        const newGateData = await DeliverGate.createInstance(gateParam);
        addedGate.push(newGateData);
      }
    }
    return addedGate;
    // Optionally: handle deleted gates if needed
  },

  async _updateRelatedEquipment(idDetails, deliveryData) {
    const equipments = deliveryData.EquipmentId;
    const updateParam = {
      DeliveryId: idDetails.id,
      DeliveryCode: idDetails.DeliveryId,
      ProjectId: deliveryData.ProjectId,
      isDeleted: false,
      isActive: true,
    };
    const condition = Sequelize.and({
      ProjectId: deliveryData.ProjectId,
      DeliveryId: idDetails.id,
    });
    const existEquipment = await DeliverEquipment.findAll({ where: condition });
    const addedEquipment = [];
    for (const element of equipments) {
      const index = existEquipment.findIndex((item) => item.EquipmentId === element);
      const equipmentParam = { ...updateParam, EquipmentId: element };
      if (index !== -1) {
        await DeliverEquipment.update(equipmentParam, {
          where: { id: existEquipment[index].id },
        });
        if (existEquipment[index].isDeleted !== false) {
          addedEquipment.push(existEquipment[index]);
        }
      } else {
        const newEquipmentData = await DeliverEquipment.createInstance(equipmentParam);
        addedEquipment.push(newEquipmentData);
      }
    }
    return addedEquipment;
    // Optionally: handle deleted equipment if needed
  },

  async _updateRelatedPersons(idDetails, deliveryData) {
    const persons = deliveryData.persons;
    const updateParam = {
      DeliveryId: idDetails.id,
      DeliveryCode: idDetails.DeliveryId,
      ProjectId: deliveryData.ProjectId,
      isDeleted: false,
      isActive: true,
    };
    const condition = Sequelize.and({
      ProjectId: deliveryData.ProjectId,
      DeliveryId: idDetails.id,
    });
    const existPerson = await DeliveryPerson.findAll({ where: condition });
    const addedPerson = [];
    for (const element of persons) {
      const index = existPerson.findIndex((item) => item.MemberId === element);
      const memberParam = { ...updateParam, MemberId: element };
      if (index !== -1) {
        await DeliveryPerson.update(memberParam, {
          where: { id: existPerson[index].id },
        });
        if (existPerson[index].isDeleted !== false) {
          addedPerson.push(existPerson[index]);
        }
      } else {
        const newPersonData = await DeliveryPerson.createInstance(memberParam);
        addedPerson.push(newPersonData);
      }
    }
    return addedPerson;
    // Optionally: handle deleted persons if needed
  },

  async _updateRelatedDefine(idDetails, deliveryData) {
    const define = deliveryData.define;
    const updateParam = {
      DeliveryId: idDetails.id,
      DeliveryCode: idDetails.DeliveryId,
      ProjectId: deliveryData.ProjectId,
      isDeleted: false,
      isActive: true,
    };
    const condition = Sequelize.and({
      ProjectId: deliveryData.ProjectId,
      DeliveryId: idDetails.id,
    });
    const existDefine = await DeliverDefine.findAll({ where: condition });
    const addedDefineData = [];
    for (const element of define) {
      const index = existDefine.findIndex((item) => item.DeliverDefineWorkId === element);
      const defineParam = { ...updateParam, DeliverDefineWorkId: element };
      if (index !== -1) {
        await DeliverDefine.update(defineParam, {
          where: { id: existDefine[index].id },
        });
        if (existDefine[index].isDeleted !== false) {
          addedDefineData.push(existDefine[index]);
        }
      } else {
        const newDefineData = await DeliverDefine.createInstance(defineParam);
        addedDefineData.push(newDefineData);
      }
    }
    return addedDefineData;
    // Optionally: handle deleted define if needed
  },


  async _sendEmailToAdmin(element, loginUser, memberData, payload, existsDeliveryRequest, idDetails, getDeliveryRequestDetail) {
    const name = element.firstName ? `${element.firstName} ${element.lastName}` : 'user';
    const memberRole = await Role.findOne({
      where: { id: memberData.RoleId, isDeleted: false },
    });

    const mailPayload = {
      name,
      email: element.email,
      content: `We would like to inform you that ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has updated a delivery booking ${idDetails.DeliveryId} and waiting for your approval.Kindly review the booking and update the status.`,
    };

    const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
      where: {
        MemberId: +element.MemberId,
        ProjectId: +payload.ProjectId,
        LocationId: +existsDeliveryRequest.LocationId,
        isDeleted: false,
      },
    });

    if (isMemberFollowLocation) {
      const memberNotification = await NotificationPreference.findOne({
        where: { MemberId: +element.MemberId, ProjectId: +payload.ProjectId, isDeleted: false },
        include: [{
          association: 'NotificationPreferenceItem',
          where: { id: 9, isDeleted: false },
        }],
      });

      if (memberNotification?.instant) {
        await MAILER.sendMail(
          mailPayload,
          'notifyPAForReApproval',
          `Delivery Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
          `Delivery Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
          async (info, err) => { console.log(info, err); },
        );
      }

      if (memberNotification?.dailyDigest) {
        await this.createDailyDigestDataApproval({
          RoleId: +memberData.RoleId,
          MemberId: +element.MemberId,
          ProjectId: +payload.ProjectId,
          ParentCompanyId: +payload.ParentCompanyId,
          loginUser,
          dailyDigestMessage: 'updated a',
          requestType: 'Delivery Request',
          messages: `delivery Booking (${idDetails.DeliveryId} - ${idDetails.description})`,
          messages2: 'and waiting for your approval',
          requestId: idDetails.id,
        });
      }
    }
  },
};
module.exports = deliveryService;
