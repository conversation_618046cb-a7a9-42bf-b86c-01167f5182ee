module.exports = (sequelize, DataTypes) => {
    const ConcreteEquipment = sequelize.define(
        'ConcreteEquipment',
        {
            ConcreteRequestId: DataTypes.INTEGER,
            isDeleted: DataTypes.BOOLEAN,
            ConcreteRequestCode: DataTypes.INTEGER,
            EquipmentId: DataTypes.INTEGER,
            publicSchemaId: {
                type: DataTypes.INTEGER,
            },
            ProjectId: DataTypes.INTEGER,
            isActive: DataTypes.BOOLEAN,
        },
        {},
    );
    ConcreteEquipment.associate = (models) => {
        // associations can be defined here
        ConcreteEquipment.belongsTo(models.ConcreteRequest, {
            as: 'concreterequest',
            foreignKey: 'ConcreteRequestId',
        });
        ConcreteEquipment.belongsTo(models.Equipments, {
            as: 'Equipments',
            foreignKey: 'EquipmentId',
        });
        ConcreteEquipment.belongsTo(models.Equipments);
    };
    ConcreteEquipment.createInstance = async (paramData) => {
        const newConcreteEquipment = await ConcreteEquipment.create(paramData);
        return newConcreteEquipment;
    };
    return ConcreteEquipment;
};
