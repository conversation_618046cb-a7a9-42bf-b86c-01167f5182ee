module.exports = (sequelize, DataTypes) => {
  const DeliverCompany = sequelize.define(
    'DeliverCompany',
    {
      DeliveryId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      DeliveryCode: DataTypes.INTEGER,
      CompanyId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ProjectId: {
        type: DataTypes.INTEGER,
      },
    },
    {},
  );
  DeliverCompany.associate = (models) => {
    // associations can be defined here
    DeliverCompany.belongsTo(models.DeliveryRequest, {
      as: 'deliveryrequest',
      foreignKey: 'DeliveryId',
    });
    DeliverCompany.belongsTo(models.Company);
  };
  DeliverCompany.getAll = async (attr) => {
    const newDeliverCompany = await DeliverCompany.findAll({
      where: { ...attr },
      order: [['id', 'DESC']],
    });
    return newDeliverCompany;
  };
  DeliverCompany.createInstance = async (paramData) => {
    const newDeliverCompany = await DeliverCompany.create(paramData);
    return newDeliverCompany;
  };
  return DeliverCompany;
};
