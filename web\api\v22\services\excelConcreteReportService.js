const moment = require('moment');

const excelConcreteReportService = {
  async concreteReport(workbook, responseData, selectedHeaders, timezoneoffset) {
    const worksheet = workbook.addWorksheet('Concrete Report');
    /* Note */
    /* Column headers */
    const rowValues = [];
    const columns = [];
    let isIdSelected = false;
    let isDescriptionSelected = false;
    let isDateSelected = false;
    let isStatusSelected = false;
    let isApprovedBySelected = false;
    let isCompanySelected = false;
    let isOrderNumberSelected = false;
    let isSlumpSelected = false;
    let isTruckSpacingSelected = false;
    let isPrimerOrderedSelected = false;
    let isPersonSelected = false;
    let isQuantityOrderedSelected = false;
    let isMixDesignSelected = false;
    let isLocationSelected = false;
    selectedHeaders.map((object) => {
      if (object.isActive === true) {
        rowValues.push(object.title);
        if (object.key === 'id') {
          columns.push({ key: object.key, width: 5 });
        } else {
          columns.push({ key: object.key, width: 32 });
        }
        if (object.key === 'id') isIdSelected = true;
        if (object.key === 'description') isDescriptionSelected = true;
        if (object.key === 'date') isDateSelected = true;
        if (object.key === 'status') isStatusSelected = true;
        if (object.key === 'approvedby') isApprovedBySelected = true;
        if (object.key === 'company') isCompanySelected = true;
        if (object.key === 'orderNumber') isOrderNumberSelected = true;
        if (object.key === 'slump') isSlumpSelected = true;
        if (object.key === 'truckSpacing') isTruckSpacingSelected = true;
        if (object.key === 'primer') isPrimerOrderedSelected = true;
        if (object.key === 'name') isPersonSelected = true;
        if (object.key === 'quantity') isQuantityOrderedSelected = true;
        if (object.key === 'mixDesign') isMixDesignSelected = true;
        if (object.key === 'location') isLocationSelected = true;
      }
      return object;
    });

    worksheet.getRow(1).values = rowValues;
    worksheet.columns = columns;
    const cellRange = {
      0: 'A',
      1: 'B',
      2: 'C',
      3: 'D',
      4: 'E',
      5: 'F',
      6: 'G',
      7: 'H',
      8: 'I',
      9: 'J',
      10: 'K',
      11: 'L',
      12: 'M',
      13: 'N',
    };
    // Insert data into worksheet
    for (let index = 1; index <= responseData.length; index += 1) {
      worksheet.addRow();
      if (isIdSelected) {
        const cellValue = cellRange[rowValues.indexOf('Id')];
        worksheet.getCell(`${cellValue}${index + 1}`).value =
          responseData[index - 1].ConcreteRequestId;
      }
      if (isDescriptionSelected) {
        const cellValue = cellRange[rowValues.indexOf('Description')];
        worksheet.getCell(`${cellValue}${index + 1}`).value = responseData[index - 1].description;
      }
      if (isDateSelected) {
        const cellValue = cellRange[rowValues.indexOf('Date & Time')];

        const start = moment(responseData[index - 1].concretePlacementStart).add(Number(timezoneoffset), 'minutes');
        const end = moment(responseData[index - 1].concretePlacementEnd).add(Number(timezoneoffset), 'minutes');

        worksheet.getCell(`${cellValue}${index + 1}`).value =
          `${start.format('MM/DD/YYYY hh:mm a')} - ${end.format('hh:mm a')}`;
      }

      if (isStatusSelected) {
        const cellValue = cellRange[rowValues.indexOf('Status')];
        worksheet.getCell(`${cellValue}${index + 1}`).value = responseData[index - 1].status;
      }
      if (isApprovedBySelected) {
        const cellValue = cellRange[rowValues.indexOf('Approved By')];
        worksheet.getCell(`${cellValue}${index + 1}`).value =
          responseData[index - 1].approverDetails &&
            responseData[index - 1].approverDetails.User.firstName
            ? `${responseData[index - 1].approverDetails.User.firstName} ${responseData[index - 1].approverDetails.User.lastName
            }`
            : '-';
      }
      if (isCompanySelected) {
        const cellValue = cellRange[rowValues.indexOf('Concrete Supplier')];
        if (
          responseData[index - 1].concreteSupplierDetails &&
          responseData[index - 1].concreteSupplierDetails.length > 0
        ) {
          const companyValues = [];
          for (let m = 0; m < responseData[index - 1].concreteSupplierDetails.length; m += 1) {
            if (
              responseData[index - 1].concreteSupplierDetails &&
              responseData[index - 1].concreteSupplierDetails[m]
            ) {
              companyValues.push(
                responseData[index - 1].concreteSupplierDetails[m].Company.companyName,
              );
            }
          }
          const company = companyValues.join(', ');
          worksheet.getCell(`${cellValue}${index + 1}`).value = company;
        } else {
          worksheet.getCell(`${cellValue}${index + 1}`).value = '-';
        }
      }
      if (isOrderNumberSelected) {
        const cellValue = cellRange[rowValues.indexOf('Order Number')];
        worksheet.getCell(`${cellValue}${index + 1}`).value = responseData[index - 1]
          .concreteOrderNumber
          ? responseData[index - 1].concreteOrderNumber
          : '-';
      }
      if (isSlumpSelected) {
        const cellValue = cellRange[rowValues.indexOf('Slump')];
        worksheet.getCell(`${cellValue}${index + 1}`).value = responseData[index - 1].slump
          ? responseData[index - 1].slump
          : '-';
      }
      if (isTruckSpacingSelected) {
        const cellValue = cellRange[rowValues.indexOf('Truck Spacing')];
        worksheet.getCell(`${cellValue}${index + 1}`).value = responseData[index - 1]
          .truckSpacingHours
          ? responseData[index - 1].truckSpacingHours
          : '-';
      }
      if (isPrimerOrderedSelected) {
        const cellValue = cellRange[rowValues.indexOf('Primer Ordered')];
        worksheet.getCell(`${cellValue}${index + 1}`).value = responseData[index - 1].primerForPump
          ? responseData[index - 1].primerForPump
          : '-';
      }
      if (isPersonSelected) {
        const cellValue = cellRange[rowValues.indexOf('Responsible Person')];
        if (
          responseData[index - 1].memberDetails &&
          responseData[index - 1].memberDetails.length > 0
        ) {
          const memberValues = [];
          for (let m = 0; m < responseData[index - 1].memberDetails.length; m += 1) {
            if (
              responseData[index - 1].memberDetails &&
              responseData[index - 1].memberDetails[m] &&
              responseData[index - 1].memberDetails[m].Member &&
              responseData[index - 1].memberDetails[m].Member.User
            ) {
              memberValues.push(
                `${responseData[index - 1].memberDetails[m].Member.User.firstName} ${responseData[index - 1].memberDetails[m].Member.User.lastName
                }`,
              );
            }
          }
          const member = memberValues.join(', ');
          worksheet.getCell(`${cellValue}${index + 1}`).value = member;
        } else {
          worksheet.getCell(`${cellValue}${index + 1}`).value = '-';
        }
      }
      if (isQuantityOrderedSelected) {
        const cellValue = cellRange[rowValues.indexOf('Quantity Ordered')];
        worksheet.getCell(`${cellValue}${index + 1}`).value = responseData[index - 1]
          .concreteQuantityOrdered
          ? responseData[index - 1].concreteQuantityOrdered
          : '-';
      }
      if (isMixDesignSelected) {
        const cellValue = cellRange[rowValues.indexOf('Mix Design')];
        if (
          responseData[index - 1].mixDesignDetails &&
          responseData[index - 1].mixDesignDetails.length > 0
        ) {
          const mixDesignValues = [];
          for (let m = 0; m < responseData[index - 1].mixDesignDetails.length; m += 1) {
            if (
              responseData[index - 1].mixDesignDetails &&
              responseData[index - 1].mixDesignDetails[m]
            ) {
              mixDesignValues.push(
                responseData[index - 1].mixDesignDetails[m].ConcreteMixDesign.mixDesign,
              );
            }
          }
          const mixDesign = mixDesignValues.join(', ');
          worksheet.getCell(`${cellValue}${index + 1}`).value = mixDesign;
        } else {
          worksheet.getCell(`${cellValue}${index + 1}`).value = '-';
        }
      }
      if (isLocationSelected) {
        const cellValue = cellRange[rowValues.indexOf('Location')];
        worksheet.getCell(`${cellValue}${index + 1}`).value =
          responseData[index - 1].location && responseData[index - 1].location.locationPath
            ? `${responseData[index - 1].location.locationPath}`
            : '-';
      }
    }
    return workbook;
  },
};
module.exports = excelConcreteReportService;
