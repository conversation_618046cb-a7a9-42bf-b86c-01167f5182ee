module.exports = (sequelize, DataTypes) => {
  const Equipments = sequelize.define(
    'Equipments',
    {
      equipmentName: DataTypes.STRING,
      PresetEquipmentTypeId: DataTypes.INTEGER,
      createdBy: {
        type: DataTypes.INTEGER,
        references: {
          model: 'Members', // name of Target model
          key: 'id', // key in Target model that we're referencing
        },
      },
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ProjectId: {
        type: DataTypes.INTEGER,
      },
      controlledBy: {
        type: DataTypes.INTEGER,
        references: {
          model: 'Members', // name of Target model
          key: 'id', // key in Target model that we're referencing
        },
      },
      equipmentAutoId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      isActive: DataTypes.BOOLEAN,
    },
    {},
  );
  Equipments.associate = (models) => {
    Equipments.belongsTo(models.Member, {
      as: 'userDetails',
      foreignKey: 'createdBy',
    });

    Equipments.belongsTo(models.Project);
    Equipments.belongsTo(models.PresetEquipmentType);
    Equipments.hasMany(models.DeliverEquipment);
    Equipments.belongsToMany(models.Locations, { through: 'LocationEquipments', foreignKey: 'EquipmentId', as: 'equipmentDetails' });
    Equipments.belongsTo(models.Member, {
      as: 'controllUserDetails',
      foreignKey: 'controlledBy',
    });

    return Equipments;
  };
  Equipments.getEquipment = async (attr) => {
    const equipment = await Equipments.findOne({
      include: [
        { association: 'userDetails', attributes: { exclude: ['password', 'resetPasswordToken'] } },
        {
          association: 'controllUserDetails',
          attributes: { exclude: ['password', 'resetPasswordToken'] },
        },
        'Project',
      ],

      where: { ...attr },
    });
    return equipment;
  };
  Equipments.getAll = async (attr, limit, offset, searchCondition, sort, sortColumn) => {
    let equipment;
    const sortByFieldName = sortColumn || 'id';
    const sortByColumnType = sort || 'DESC';
    let orderQuery;
    if (sortByFieldName === 'companyName') {
      orderQuery = [['controllUserDetails', 'Company', 'companyName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'contactPerson') {
      orderQuery = [['controllUserDetails', 'User', 'firstName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'equipmentName' || sortByFieldName === 'id') {
      orderQuery = [[`${sortByFieldName}`, `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'equipmentType') {
      orderQuery = [['PresetEquipmentType', `${sortByFieldName}`, `${sortByColumnType}`]];
    }
    if (limit === 0) {
      equipment = await Equipments.findAll({
        where: { ...attr },
        include: [
          {
            required: false,
            where: { isDeleted: false, isActive: true },
            association: 'PresetEquipmentType',
            attributes: ['id', 'equipmentType', 'isCraneType'],
          },
        ],
        order: orderQuery,
      });
    } else {
      equipment = await Equipments.findAndCountAll({
        include: [
          {
            association: 'userDetails',
            attributes: { exclude: ['password', 'resetPasswordToken'] },
            include: [
              { association: 'User', attributes: ['email', 'id', 'firstName', 'lastName'] },
            ],
          },
          {
            required: true,
            where: { isDeleted: false, isActive: true },
            association: 'PresetEquipmentType',
            attributes: ['id', 'equipmentType', 'isCraneType'],
          },
          {
            association: 'controllUserDetails',
            attributes: { exclude: ['password', 'resetPasswordToken'] },
            include: [
              { association: 'User', attributes: ['email', 'id', 'firstName', 'lastName'] },
              { association: 'Company', attributes: ['id', 'companyName'] },
            ],
          },
          'Project',
        ],
        where: { ...attr, ...searchCondition },
        limit,
        offset,
        order: orderQuery,
      });
    }

    return equipment;
  };
  Equipments.updateInstance = async (id, args) => {
    const equipment = await Equipments.update(args, { where: { id } });

    return equipment;
  };

  Equipments.createEquipment = async (equipmentData) => {
    const newData = await Equipments.create(equipmentData);
    return newData;
  };
  return Equipments;
};
