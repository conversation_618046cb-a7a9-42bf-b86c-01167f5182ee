const status = require('http-status');
const AttachementController = require('../AttachementController');

// Mock the services
jest.mock('../../services', () => ({
  attachementService: {
    createAttachement: jest.fn(),
    createInspectionAttachement: jest.fn(),
    getAttachement: jest.fn(),
    getInspectionAttachement: jest.fn(),
    deleteAttachement: jest.fn(),
    deleteInspectionAttachement: jest.fn(),
  },
}));

const { attachementService } = require('../../services');

describe('AttachementController', () => {
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();

    // Setup mock request, response, and next function
    mockReq = {
      body: {},
      params: {},
      files: {},
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    mockNext = jest.fn();
  });

  describe('createAttachement', () => {
    it('should create attachment successfully', async () => {
      // Arrange
      const mockResponse = { id: 1, filename: 'test.pdf', url: 'https://example.com/test.pdf' };
      attachementService.createAttachement.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await AttachementController.createAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(attachementService.createAttachement).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.CREATED);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Uploaded Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Upload failed');
      attachementService.createAttachement.mockImplementation((req, callback) =>
        callback(null, error),
      );

      // Act
      await AttachementController.createAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(attachementService.createAttachement).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Service error');
      attachementService.createAttachement.mockImplementation(() => {
        throw error;
      });

      // Act
      await AttachementController.createAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('createInspectionAttachement', () => {
    it('should create inspection attachment successfully', async () => {
      // Arrange
      const mockResponse = {
        id: 1,
        filename: 'inspection.pdf',
        url: 'https://example.com/inspection.pdf',
      };
      attachementService.createInspectionAttachement.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await AttachementController.createInspectionAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(attachementService.createInspectionAttachement).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.CREATED);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Uploaded Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Upload failed');
      attachementService.createInspectionAttachement.mockImplementation((req, callback) =>
        callback(null, error),
      );

      // Act
      await AttachementController.createInspectionAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(attachementService.createInspectionAttachement).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Service error');
      attachementService.createInspectionAttachement.mockImplementation(() => {
        throw error;
      });

      // Act
      await AttachementController.createInspectionAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('getAttachement', () => {
    it('should get attachment successfully', async () => {
      // Arrange
      const mockResponse = { id: 1, filename: 'test.pdf', url: 'https://example.com/test.pdf' };
      attachementService.getAttachement.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await AttachementController.getAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(attachementService.getAttachement).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Attachment Viewed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('File not found');
      attachementService.getAttachement.mockImplementation((req, callback) =>
        callback(null, error),
      );

      // Act
      await AttachementController.getAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(attachementService.getAttachement).toHaveBeenCalledWith(mockReq, expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Service error');
      attachementService.getAttachement.mockImplementation(() => {
        throw error;
      });

      // Act
      await AttachementController.getAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('getInspectionAttachement', () => {
    it('should get inspection attachment successfully', async () => {
      // Arrange
      const mockResponse = {
        id: 1,
        filename: 'inspection.pdf',
        url: 'https://example.com/inspection.pdf',
      };
      attachementService.getInspectionAttachement.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await AttachementController.getInspectionAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(attachementService.getInspectionAttachement).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Attachment Viewed Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('File not found');
      attachementService.getInspectionAttachement.mockImplementation((req, callback) =>
        callback(null, error),
      );

      // Act
      await AttachementController.getInspectionAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(attachementService.getInspectionAttachement).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Service error');
      attachementService.getInspectionAttachement.mockImplementation(() => {
        throw error;
      });

      // Act
      await AttachementController.getInspectionAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('deleteAttachement', () => {
    it('should delete attachment successfully', async () => {
      // Arrange
      const mockResponse = { id: 1, deleted: true };
      attachementService.deleteAttachement.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await AttachementController.deleteAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(attachementService.deleteAttachement).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Attachment Deleted Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Delete failed');
      attachementService.deleteAttachement.mockImplementation((req, callback) =>
        callback(null, error),
      );

      // Act
      await AttachementController.deleteAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(attachementService.deleteAttachement).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Service error');
      attachementService.deleteAttachement.mockImplementation(() => {
        throw error;
      });

      // Act
      await AttachementController.deleteAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('deleteInspectionAttachement', () => {
    it('should delete inspection attachment successfully', async () => {
      // Arrange
      const mockResponse = { id: 1, deleted: true };
      attachementService.deleteInspectionAttachement.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await AttachementController.deleteInspectionAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(attachementService.deleteInspectionAttachement).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockRes.status).toHaveBeenCalledWith(status.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Attachment Deleted Successfully.',
        data: mockResponse,
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle error from service', async () => {
      // Arrange
      const error = new Error('Delete failed');
      attachementService.deleteInspectionAttachement.mockImplementation((req, callback) =>
        callback(null, error),
      );

      // Act
      await AttachementController.deleteInspectionAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(attachementService.deleteInspectionAttachement).toHaveBeenCalledWith(
        mockReq,
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should handle thrown error', async () => {
      // Arrange
      const error = new Error('Service error');
      attachementService.deleteInspectionAttachement.mockImplementation(() => {
        throw error;
      });

      // Act
      await AttachementController.deleteInspectionAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('Edge Cases', () => {
    it('should handle null response from service', async () => {
      // Arrange
      attachementService.getAttachement.mockImplementation((req, callback) => callback(null, null));

      // Act
      await AttachementController.getAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Attachment Viewed Successfully.',
        data: null,
      });
    });

    it('should handle empty response from service', async () => {
      // Arrange
      attachementService.getAttachement.mockImplementation((req, callback) => callback({}, null));

      // Act
      await AttachementController.getAttachement(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Attachment Viewed Successfully.',
        data: {},
      });
    });

    it('should handle request with files', async () => {
      // Arrange
      const mockReqWithFiles = {
        ...mockReq,
        files: {
          attachment: {
            name: 'test.pdf',
            size: 1024,
            mimetype: 'application/pdf',
          },
        },
      };
      const mockResponse = { id: 1, filename: 'test.pdf' };
      attachementService.createAttachement.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await AttachementController.createAttachement(mockReqWithFiles, mockRes, mockNext);

      // Assert
      expect(attachementService.createAttachement).toHaveBeenCalledWith(
        mockReqWithFiles,
        expect.any(Function),
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Uploaded Successfully.',
        data: mockResponse,
      });
    });

    it('should handle request with params', async () => {
      // Arrange
      const mockReqWithParams = {
        ...mockReq,
        params: { id: '123' },
      };
      const mockResponse = { id: 123, filename: 'test.pdf' };
      attachementService.getAttachement.mockImplementation((req, callback) =>
        callback(mockResponse, null),
      );

      // Act
      await AttachementController.getAttachement(mockReqWithParams, mockRes, mockNext);

      // Assert
      expect(attachementService.getAttachement).toHaveBeenCalledWith(
        mockReqWithParams,
        expect.any(Function),
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        message: 'Attachment Viewed Successfully.',
        data: mockResponse,
      });
    });
  });
});
