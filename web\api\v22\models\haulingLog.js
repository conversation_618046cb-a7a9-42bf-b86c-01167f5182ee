module.exports = (sequelize, DataTypes) => {
    const HaulingLog = sequelize.define(
        'HaulingLog',
        {
            companyName: DataTypes.STRING,
            importOrExportName: DataTypes.STRING,
            materialType: DataTypes.STRING,
            quantity: DataTypes.STRING,
            noOfTrips: DataTypes.STRING,
            haulingOriginLocation: DataTypes.STRING,
            haulingDestinationLocation: DataTypes.STRING,
            projectId: DataTypes.INTEGER,
            unitType: DataTypes.STRING,
            unitValue: DataTypes.STRING,
            isDeleted: DataTypes.BOOLEAN,
        },
        {},
    );
    HaulingLog.createHaulingLog = async (equipmentData) => {
        const newData = await HaulingLog.create(equipmentData);
        return newData;
    };
    HaulingLog.getAll = async (attr, limit, offset, searchCondition, sort, sortColumn) => {
        let HaulingLogs;
        const sortByFieldName = sortColumn || 'id';
        const sortByColumnType = sort || 'DESC';

        HaulingLogs = await HaulingLog.findAndCountAll({
            where: { ...attr, ...searchCondition },
            limit,
            offset,
            order: [[`${sortByFieldName}`, `${sortByColumnType}`]]
        });

        return HaulingLogs;
    };
    return HaulingLog;
};