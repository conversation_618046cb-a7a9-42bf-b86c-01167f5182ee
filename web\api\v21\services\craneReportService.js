const { Sequelize, Enterprise } = require('../models');
const moment = require('moment');
let { DeliveryRequest, Member, DeliverCompany, User } = require('../models');
const {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>oidList,
  CraneRequestResponsible<PERSON>erson,
  CraneRequest,
  CraneRequestCompany,
} = require('../models');
const helper = require('../helpers/domainHelper');
const exportService = require('./exportService');
const pdfCraneReportService = require('./pdfCraneReportService');
const csvCraneReportService = require('./csvCraneReportService');
const excelCraneReportService = require('./excelCraneReportService');
const awsConfig = require('../middlewares/awsConfig');
const deliveryReportService = require('./deliveryreportService');

let publicUser;
let publicMember;
const { Op } = Sequelize;
const craneReportService = {
  async getDomainFromEnterprise(domainName) {
    if (!domainName) return '';

    const domainEnterpriseValue = await Enterprise.findOne({
      where: { name: domainName.toLowerCase() },
    });

    return domainEnterpriseValue ? domainName.toLowerCase() : '';
  },

  async resolveEnterpriseByParentOrUser(inputData, domainName) {
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;

    if (domainName || ParentCompanyId === undefined || ParentCompanyId === 'undefined') {
      return { domainName, enterpriseValue: null };
    }

    const { email } = inputData.user;
    if (!email) {
      return { domainName, enterpriseValue: null };
    }

    const enterpriseValue = await this._findEnterpriseValue(email, ParentCompanyId);

    if (enterpriseValue) {
      domainName = enterpriseValue.name.toLowerCase();
    }

    return { domainName, enterpriseValue };
  },

  async _findEnterpriseValue(email, ParentCompanyId) {
    const userData = await publicUser.findOne({ where: { email } });
    if (!userData) {
      return null;
    }

    const memberData = await publicMember.findOne({
      where: {
        UserId: userData.id,
        RoleId: { [Op.ne]: 4 },
        isDeleted: false,
      },
    });

    if (!memberData) {
      return await Enterprise.findOne({
        where: { ParentCompanyId, status: 'completed' },
      });
    }

    const whereCondition = memberData.isAccount
      ? { id: memberData.EnterpriseId, status: 'completed' }
      : { ParentCompanyId, status: 'completed' };

    return await Enterprise.findOne({ where: whereCondition });
  },

  async resolveDomainNameAndEnterprise(inputData) {
    let { domainName } = inputData.user;

    domainName = await this.getDomainFromEnterprise(domainName);

    const { domainName: finalDomainName, enterpriseValue } =
      await this.resolveEnterpriseByParentOrUser(inputData, domainName);

    return { domainName: finalDomainName, enterpriseValue };
  },


  async getDynamicModel(inputData) {
    await this.returnProjectModel();

    const { domainName, enterpriseValue } = await this.resolveDomainNameAndEnterprise(inputData);
    const modelObj = await helper.getDynamicModel(domainName);

    DeliveryRequest = modelObj.DeliveryRequest;
    Member = modelObj.Member;
    DeliverCompany = modelObj.DeliverCompany;
    User = modelObj.User;

    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      inputData.user = newUser;
    }

    return null; // originally `ProjectId` was declared but never assigned
  },

  async listCraneRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;
      const { sort, sortByField } = incomeData;

      if (params.void >= 1 && params.void <= 0) {
        return done(null, { message: 'Please enter void as 1 or 0' });
      }

      const memberDetails = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: params.ProjectId,
          isDeleted: false,
          isActive: true,
        }),
      });

      if (!memberDetails) {
        return done(null, { message: 'Project Id/Member does not exist' });
      }

      const { voidCraneDelivery, voidDelivery } = await this.getVoidLists(params.ProjectId);

      const { craneCondition, condition } = this.buildConditions(params, incomeData, voidCraneDelivery, voidDelivery);

      const { craneRequestList, deliveryRequest } = await this.getFilteredCraneAndDeliveryRequests({
        inputData,
        incomeData,
        memberDetails,
        craneCondition,
        condition,
        sort,
        sortByField,
        voidFlag: params.void
      });

      this.handleFinalSortingAndPaging({
        inputData,
        incomeData,
        craneRequestList,
        deliveryRequest,
        memberDetails,
        sort,
        sortByField,
        pageSize: +params.pageSize,
        offset: (+params.pageNo - 1) * +params.pageSize
      },
        done
      );
    } catch (e) {
      done(null, e);
    }
  },

  // ======================= HELPER FUNCTIONS ======================= //

  async getVoidLists(projectId) {
    const voidDeliveryList = await VoidList.findAll({
      where: { ProjectId: projectId, isDeliveryRequest: true, DeliveryRequestId: { [Op.ne]: null } }
    });
    const voidCraneRequestList = await VoidList.findAll({
      where: { ProjectId: projectId, isDeliveryRequest: false, CraneRequestId: { [Op.ne]: null } }
    });

    const voidDelivery = voidDeliveryList.map(el => el.DeliveryRequestId);
    const voidCraneDelivery = voidCraneRequestList.map(el => el.CraneRequestId);

    return { voidDelivery, voidCraneDelivery };
  },

  buildConditions(params, incomeData, voidCraneDelivery, voidDelivery) {
    const condition = { ProjectId: +params.ProjectId, isDeleted: false };
    const craneCondition = { ProjectId: +params.ProjectId, isDeleted: false };

    if (params.void === '0' || params.void === 0) {
      condition['$DeliveryRequest.id$'] = { [Op.and]: [{ [Op.notIn]: voidDelivery }] };
      craneCondition['$CraneRequest.id$'] = { [Op.and]: [{ [Op.notIn]: voidCraneDelivery }] };
    } else {
      condition['$DeliveryRequest.id$'] = { [Op.and]: [{ [Op.in]: voidDelivery }] };
      craneCondition['$CraneRequest.id$'] = { [Op.and]: [{ [Op.in]: voidCraneDelivery }] };
    }

    if (incomeData.defineFilter?.length) {
      condition['$defineWorkDetails.DeliverDefineWork.id$'] = { [Op.in]: incomeData.defineFilter };
      craneCondition['$defineWorkDetails.DeliverDefineWork.id$'] = { [Op.in]: incomeData.defineFilter };
    }

    if (incomeData.companyFilter?.length) {
      condition['$companyDetails.Company.id$'] = { [Op.in]: incomeData.companyFilter };
      craneCondition['$companyDetails.Company.id$'] = { [Op.in]: incomeData.companyFilter };
    }

    if (incomeData.locationFilter?.length) {
      condition['$location.id$'] = { [Op.in]: incomeData.locationFilter };
      craneCondition['$location.id$'] = { [Op.in]: incomeData.locationFilter };
    }

    return { condition, craneCondition };
  },

  async getFilteredCraneAndDeliveryRequests(params) {
    const { inputData, incomeData, memberDetails, craneCondition, condition, sort, sortByField, voidFlag } = params;
    const roleId = memberDetails.RoleId;
    const memberId = memberDetails.id;
    let craneRequestList = [], deliveryRequest = [];

    if (!(incomeData.gateFilter > 0 || incomeData.statusFilter === 'Delivered')) {
      craneRequestList = await this.getAllCraneRequest({
        inputData,
        roleId,
        memberId,
        craneCondition,
        descriptionFilter: incomeData.descriptionFilter,
        startdate: incomeData.startdate,
        enddate: incomeData.enddate,
        companyFilter: incomeData.companyFilter,
        memberFilter: incomeData.memberFilter,
        equipmentFilter: incomeData.equipmentFilter,
        statusFilter: incomeData.statusFilter,
        idFilter: incomeData.idFilter,
        pickFrom: incomeData.pickFrom,
        pickTo: incomeData.pickTo,
        search: incomeData.search,
        order: sort,
        sort,
        sortByField,
        dateFilter: incomeData.dateFilter
      });
    }

    if (incomeData.statusFilter !== 'Completed') {
      deliveryRequest = await DeliveryRequest.getCraneAssociatedRequest(
        inputData,
        roleId,
        memberId,
        condition,
        incomeData.descriptionFilter,
        incomeData.startdate,
        incomeData.enddate,
        incomeData.companyFilter,
        incomeData.memberFilter,
        incomeData.equipmentFilter,
        incomeData.statusFilter,
        incomeData.idFilter,
        incomeData.pickFrom,
        incomeData.pickTo,
        incomeData.search,
        incomeData.gateFilter,
        null,
        sort,
        sortByField,
        voidFlag,
        incomeData.dateFilter
      );
    }

    return { craneRequestList, deliveryRequest };
  },

  handleFinalSortingAndPaging(params, done) {
    const {
      inputData, incomeData, craneRequestList, deliveryRequest,
      memberDetails, sort, sortByField, pageSize, offset
    } = params;

    this.getSearchCraneData(
      { inputData, incomeData, craneRequestList, result: [], pageSize, index: 0, count: 0, memberDetails },
      (craneResp, craneErr) => {
        if (craneErr) return done(null, { message: 'Something went wrong' });
        this._handleDeliveryData({
          craneResp,
          inputData,
          incomeData,
          deliveryRequest,
          pageSize,
          memberDetails,
          sort,
          sortByField,
          offset
        },
          done
        );
      }
    );
  },

  _handleDeliveryData(params, done) {
    const {
      craneResp, inputData, incomeData, deliveryRequest,
      pageSize, memberDetails, sort, sortByField, offset
    } = params;
    this.getSearchDeliveryData(
      { inputData, incomeData, deliveryRequest, result: [], pageSize, index: 0, count: 0, memberDetails },
      (deliveryResp, deliveryErr) => {
        if (deliveryErr) return done(null, { message: 'Something went wrong' });

        const allRequests = [...craneResp, ...deliveryResp];

        this._handleFinalData({
          allRequests,
          pageSize,
          inputData,
          incomeData,
          sort,
          sortByField,
          offset
        },
          done
        );
      }
    );
  },

  _handleFinalData(params, done) {
    const {
      allRequests, pageSize, inputData, incomeData,
      sort, sortByField, offset
    } = params;
    this.getLimitData(
      allRequests,
      0,
      pageSize,
      [],
      incomeData,
      inputData.headers.timezoneoffset,
      (finalResp, finalErr) => {
        if (finalErr) return done(null, { message: 'Something went wrong' });

        let rows = inputData.body.exportType
          ? finalResp
          : finalResp.slice(offset, offset + pageSize);

        if (sort && sortByField && rows) {
          rows.sort((a, b) => {
            const isAscending = sort === 'ASC';
            if (isAscending) {
              return a[sortByField] > b[sortByField] ? 1 : -1;
            } else {
              return b[sortByField] > a[sortByField] ? 1 : -1;
            }
          });
        }


        done({ count: allRequests.length, rows }, false);
      }
    );
  },

  async checkCompanyCondition(req, incomeData, element) {
    if (Number(req.params.void) === 1) {
      if (incomeData.companyFilter > 0) {
        const data = element.companyDetails.find((ele) => {
          return ele.Company.companyName === incomeData.companyFilter;
        });
        return !!data;
      }
      return true;
    }

    if (Number(req.params.void) === 0) {
      if (incomeData.companyFilter > 0) {
        const data = await CraneRequestCompany.findOne({
          where: {
            CraneRequestId: element.id,
            CompanyId: incomeData.companyFilter,
            isDeleted: false,
          },
        });
        return !!data;
      }
      return true;
    }

    return true;
  },

  async getSearchCraneData(params, done) {
    const {
      req,
      incomeData,
      deliveryList,
      result,
      limit,
      index,
      count,
      memberDetails,
    } = params;

    const elementValue = deliveryList[index];

    if (!elementValue) {
      return done(result, false);
    }

    const element = JSON.parse(JSON.stringify(elementValue));

    const status = {
      companyCondition: await this.checkCompanyCondition(req, incomeData, element),
      memberCondition: true, // Extend this later if member checking logic is added
    };

    if (status.companyCondition && status.memberCondition) {
      result.push(element);
    }

    if (index < deliveryList.length - 1) {
      this.getSearchCraneData(
        {
          req,
          incomeData,
          deliveryList,
          result,
          limit,
          index: index + 1,
          count: count + 1,
          memberDetails,
        },
        (response, err) => {
          if (!err) {
            done(response, false);
          } else {
            done(null, err);
          }
        },
      );
    } else {
      done(result, false);
    }
  },

  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async checkDeliveryConditions(element, incomeData, req) {
    const status = { companyCondition: true, memberCondition: true };

    if (Number(req.params.void) === 1) {
      if (incomeData.companyFilter) {
        const data = element.companyDetails.find(
          (ele) => ele.Company.companyName === incomeData.companyFilter
        );
        if (!data) status.companyCondition = false;
      }
    } else if (req.params.void === 0) {
      if (incomeData.companyFilter > 0) {
        const data = await DeliverCompany.findOne({
          where: {
            DeliveryId: element.id,
            CompanyId: +incomeData.companyFilter,
            isDeleted: false,
          },
        });
        if (!data) status.companyCondition = false;
      }
    }

    return status;
  },

  async getSearchDeliveryData(params, done) {
    const {
      req,
      incomeData,
      deliveryList,
      result,
      limit,
      index,
      count,
      memberDetails,
    } = params;

    const elementValue = deliveryList[index];
    if (!elementValue) {
      return done(result, false);
    }

    const element = JSON.parse(JSON.stringify(elementValue));
    const status = await this.checkDeliveryConditions(element, incomeData, req);

    if (status.companyCondition && status.memberCondition) {
      result.push(element);
    }

    if (index < deliveryList.length - 1) {
      this.getSearchDeliveryData(
        {
          req,
          incomeData,
          deliveryList,
          result,
          limit,
          index: index + 1,
          count: count + 1,
          memberDetails,
        },
        (response, err) => {
          if (!err) done(response, false);
          else done(null, err);
        }
      );
    } else {
      done(result, false);
    }
  },

  async getLimitData(result, index, limit, finalResult, incomeData, timezoneoffset, done) {
    if (index < limit) {
      finalResult.push(result);
      this.getLimitData(
        result,
        index + 1,
        limit,
        finalResult,
        incomeData,
        timezoneoffset,
        (response, err) => {
          if (!err) {
            done(result, false);
          } else {
            done(null, err);
          }
        },
      );
    } else {
      done(result, false);
    }
  },

  async exportReportForScheduler(req) {
    return new Promise((res, rej) => {
      this.listCraneRequest(req, async (response, error) => {
        if (error) {
          return rej(error instanceof Error ? error : new Error(String(error)));
        }

        if (response.count === 0) {
          return res('No Data Found');
        }

        try {
          const result = await this.handleExportByType(req, response.rows);
          res(result);
        } catch (exportErr) {
          rej(exportErr instanceof Error ? exportErr : new Error(String(exportErr)));
        }
      });
    });
  },


  async handleExportByType(req, rows) {
    const { exportType, selectedHeaders, reportName } = req.body;
    const timezoneOffset = req.headers.timezoneoffset;

    if (exportType === 'PDF') {
      const loginUser = req.user;
      return new Promise((resolve, reject) => {
        pdfCraneReportService.pdfFormatOfCraneRequest(
          req.params,
          loginUser,
          rows,
          req,
          (pdfFile, err) => {
            if (err) {
              reject(err instanceof Error ? err : new Error(String(err)));
            } else {
              resolve(pdfFile);
            }
          }
        );
      });
    }

    if (exportType === 'EXCEL') {
      const workbook = await exportService.createWorkbook();
      let reportWorkbook = await excelCraneReportService.craneReport(
        workbook,
        rows,
        selectedHeaders,
        timezoneOffset
      );

      if (!reportWorkbook) return 'No data found';

      const buffer = await reportWorkbook.xlsx.writeBuffer();
      if (!Buffer.isBuffer(buffer)) return 'No data found';

      return new Promise((resolve, reject) => {
        awsConfig.reportUpload(
          buffer,
          reportName,
          'xlsx',
          (result, error) => {
            if (error) {
              reject(error instanceof Error ? error : new Error(String(error)));
            } else {
              resolve(result);
            }
          }
        );
      });
    }


    if (exportType === 'CSV') {
      return new Promise((resolve, reject) => {
        csvCraneReportService.exportCraneReportInCsvFormat(
          rows,
          selectedHeaders,
          timezoneOffset,
          reportName,
          exportType,
          (csvFile, err) => {
            if (err) {
              reject(err instanceof Error ? err : new Error(String(err)));
            } else {
              resolve(csvFile);
            }
          }
        );
      });
    }

    throw new Error(`Unsupported export type: ${exportType}`);
  },

  async exportReport(req, done) {
    await this.listCraneRequest(req, async (response, error) => {
      if (error) return;

      const exportType = req.body.exportType;
      switch (exportType) {
        case 'PDF':
          await this.handlePdfExport(req, response.rows, done);
          break;
        case 'EXCEL':
          await this.handleExcelExport(req, response.rows, done);
          break;
        case 'CSV':
          await this.handleCsvExport(req, response.rows, done);
          break;
        default:
          done(null, { message: 'Unsupported export type' });
      }
    });
  },

  async handlePdfExport(req, rows, done) {
    const loginUser = req.user;
    await pdfCraneReportService.pdfFormatOfCraneRequest(
      req.params,
      loginUser,
      rows,
      req,
      async (pdfFile, err) => {
        if (err) return done(null, { message: 'Failed to export PDF' });

        if (req.body.saved) {
          req.body.reportType = 'Crane';
          const savedData = await deliveryReportService.createSavedReports(req, pdfFile);
          return savedData ? done(pdfFile, false) : done(null, { message: 'Cannot create reports' });
        }

        return done(pdfFile, false);
      }
    );
  },

  async handleExcelExport(req, rows, done) {
    const workbook = await exportService.createWorkbook();
    let reportWorkbook = await excelCraneReportService.craneReport(
      workbook,
      rows,
      req.body.selectedHeaders,
      req.headers.timezoneoffset
    );

    if (!reportWorkbook) return done(null, { message: 'Cannot export document' });

    if (req.body.saved) {
      const buffer = await reportWorkbook.xlsx.writeBuffer();
      const excelFile = await deliveryReportService.saveExcelReport(
        buffer,
        req.body.reportName,
        req.body.exportType
      );

      if (excelFile) {
        req.body.reportType = 'Crane';
        const savedData = await deliveryReportService.createSavedReports(req, excelFile);
        return savedData ? done(excelFile, false) : done(null, { message: 'Cannot create reports' });
      }

      return done(null, { message: 'Cannot save Excel file' });
    }

    return done(reportWorkbook, false);
  },

  async handleCsvExport(req, rows, done) {
    await csvCraneReportService.exportCraneReportInCsvFormat(
      rows,
      req.body.selectedHeaders,
      req.headers.timezoneoffset,
      req.body.reportName,
      req.body.exportType,
      async (csvFile, err) => {
        if (err) return done(null, { message: 'Cannot export document' });

        if (req.body.saved) {
          req.body.reportType = 'Crane';
          const savedData = await deliveryReportService.createSavedReports(req, csvFile);
          return savedData ? done(csvFile, false) : done(null, { message: 'Cannot create reports' });
        }

        return done(csvFile, false);
      }
    );
  },


  async getAllCraneRequest(params) {
    const {
      req, roleId, attr, descriptionFilter, startdate, enddate,
      companyFilter, memberFilter, equipmentFilter, statusFilter,
      idFilter, pickFrom, pickTo, search, order, sort, sortColumn,
      dateFilter,
    } = params;

    const timezoneOffset = Number(req.headers.timezoneoffset);
    const locationFilter = req.body.locationFilter || [];

    let commonSearch = {
      ...attr,
      isDeleted: false,
    };

    const orderQuery = this.extractSortOrder(sortColumn, order, sort);
    commonSearch = this.buildCommonSearchFilters({
      commonSearch, descriptionFilter, dateFilter, pickFrom, pickTo,
      memberFilter, equipmentFilter, locationFilter, statusFilter,
      idFilter, companyFilter, search, timezoneOffset, startdate, enddate, req
    });

    const includeModels = this.getIncludeModels(roleId);

    const newDeliveryRequest = await CraneRequest.findAll({
      subQuery: false,
      distinct: true,
      include: includeModels,
      where: commonSearch,
      attributes: [
        'id', 'description', 'craneDeliveryStart', 'craneDeliveryEnd', 'status',
        'additionalNotes', 'CraneRequestId', 'approved_at', 'isEscortNeeded',
        'pickUpLocation', 'dropOffLocation', 'isAssociatedWithDeliveryRequest',
        'requestType', 'LocationId', 'isCreatedByGuestUser'
      ],
      order: orderQuery,
    });

    return newDeliveryRequest;
  },

  extractSortOrder(sortColumn, order, sort) {
    const sortByFieldName = sortColumn || 'id';
    const sortByColumnType = order || sort || 'DESC';

    const specialSorts = {
      equipment: [['equipmentDetails', 'Equipment', 'equipmentName', sortByColumnType]],
      approvedUser: [['approverDetails', 'User', 'firstName', sortByColumnType]],
      company: [['companyDetails', 'Company', 'companyName', sortByColumnType]],
      dfow: [['defineWorkDetails', 'DeliverDefineWork', 'DFOW', sortByColumnType]],
      member: [['memberDetails', 'Member', 'User', 'firstName', sortByColumnType]],
      datetime: [['craneDeliveryStart', sortByColumnType]],
    };

    if (specialSorts[sortByFieldName]) return specialSorts[sortByFieldName];
    return [[sortByFieldName, sortByColumnType]];
  },

  buildCommonSearchFilters({
    commonSearch, descriptionFilter, dateFilter, pickFrom, pickTo,
    memberFilter, equipmentFilter, locationFilter, statusFilter,
    idFilter, companyFilter, search, timezoneOffset, startdate, enddate, req
  }) {

    const andWrap = (base, condition) => ({
      [Op.and]: [
        { ...base, [Op.or]: [condition] }
      ]
    });

    if (descriptionFilter) {
      commonSearch = andWrap(commonSearch, {
        description: { [Op.iLike]: `%${descriptionFilter}%` }
      });
    }

    if (dateFilter) {
      const start = moment(dateFilter).startOf('day').utcOffset(timezoneOffset, true);
      const end = moment(dateFilter).endOf('day').utcOffset(timezoneOffset, true);
      commonSearch = andWrap(commonSearch, {
        craneDeliveryStart: { [Op.between]: [start, end] }
      });
    }

    if (pickFrom) {
      commonSearch = andWrap(commonSearch, {
        pickUpLocation: { [Op.iLike]: `%${pickFrom}%` }
      });
    }

    if (pickTo) {
      commonSearch = andWrap(commonSearch, {
        dropOffLocation: { [Op.iLike]: `%${pickTo}%` }
      });
    }

    if (memberFilter > 0) {
      commonSearch = andWrap(commonSearch, {
        '$memberDetails.Member.id$': +memberFilter
      });
    }

    if (equipmentFilter?.length) {
      commonSearch = andWrap(commonSearch, {
        '$equipmentDetails.Equipment.equipmentName$': {
          [Op.iLike]: `${equipmentFilter}`
        }
      });
    }

    if (locationFilter?.length) {
      commonSearch = andWrap(commonSearch, {
        '$location.id$': { [Op.in]: locationFilter }
      });
    }

    if (statusFilter?.length) {
      commonSearch = andWrap(commonSearch, { status: statusFilter });
    }

    if (idFilter) {
      commonSearch = andWrap(commonSearch, { CraneRequestId: idFilter });
    }

    if (typeof companyFilter === 'string' && companyFilter !== '') {
      commonSearch = andWrap(commonSearch, {
        '$companyDetails.Company.companyName$': {
          [Op.iLike]: `${companyFilter}`
        }
      });
    }

    if (startdate) {
      const start = moment(startdate).startOf('day').utcOffset(timezoneOffset, true);
      const end = moment(enddate).endOf('day').utcOffset(timezoneOffset, true);
      commonSearch = andWrap(commonSearch, {
        craneDeliveryStart: { [Op.between]: [start, end] }
      });
    } else {
      const start = moment().startOf('day').utcOffset(timezoneOffset, true);
      commonSearch = andWrap(commonSearch, {
        craneDeliveryStart: { [Op.gte]: start }
      });
    }

    if (search) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              { description: { [Op.iLike]: `%${search}%` } },
              { pickUpLocation: { [Op.iLike]: `%${search}%` } },
              { dropOffLocation: { [Op.iLike]: `%${search}%` } },
              { '$equipmentDetails.Equipment.equipmentName$': { [Op.iLike]: `%${search}%` } },
              { '$location.locationPath$': { [Op.iLike]: `%${search}%` } }
            ]
          }
        ]
      };
    }

    return commonSearch;
  },

  getIncludeModels(roleId) {
    const requiredCondition = roleId !== 2;

    return [
      {
        required: requiredCondition,
        association: 'memberDetails',
        where: { isDeleted: false, isActive: true },
        attributes: ['id'],
        include: [{
          association: 'Member',
          attributes: ['id', 'UserId'],
          include: [{
            association: 'User',
            attributes: ['id', 'email', 'firstName', 'lastName']
          }]
        }]
      },
      {
        association: 'companyDetails',
        where: { isDeleted: false },
        required: false,
        attributes: ['id'],
        include: [{ association: 'Company', attributes: ['companyName', 'id'] }]
      },
      {
        association: 'Project',
        attributes: ['projectName']
      },
      {
        association: 'createdUserDetails',
        required: false,
        attributes: ['id', 'RoleId'],
        include: [{
          association: 'User',
          attributes: ['email', 'id', 'firstName', 'lastName']
        }]
      },
      {
        association: 'approverDetails',
        attributes: ['id'],
        include: [{
          association: 'User',
          attributes: ['email', 'firstName', 'lastName']
        }]
      },
      {
        required: false,
        association: 'defineWorkDetails',
        where: { isDeleted: false },
        attributes: ['id'],
        include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }]
      },
      {
        required: false,
        association: 'equipmentDetails',
        where: { isDeleted: false, isActive: true },
        attributes: ['id'],
        include: [{
          include: [{
            required: true,
            where: { isDeleted: false, isActive: true, isCraneType: true },
            association: 'PresetEquipmentType',
            attributes: ['id', 'equipmentType', 'isCraneType']
          }],
          association: 'Equipment',
          attributes: ['equipmentName', 'id']
        }]
      },
      {
        association: 'gateDetails',
        where: { isDeleted: false, isActive: true },
        required: false,
        attributes: ['id'],
        include: [{ association: 'Gate', attributes: ['gateName', 'id'] }]
      },
      {
        association: 'voidList',
        attributes: ['id', 'MemberId', 'ProjectId', 'CraneRequestId']
      },
      {
        association: 'recurrence',
        required: false,
        attributes: [
          'id', 'recurrence', 'recurrenceStartDate', 'recurrenceEndDate', 'dateOfMonth',
          'monthlyRepeatType', 'repeatEveryCount', 'days', 'requestType', 'repeatEveryType',
          'chosenDateOfMonth', 'createdBy', 'chosenDateOfMonthValue'
        ]
      },
      {
        association: 'location',
        where: { isDeleted: false },
        required: false,
        attributes: ['id', 'locationPath']
      }
    ];
  }

};
module.exports = craneReportService;
