module.exports = (sequelize, DataTypes) => {
  const ConcreteRequestCompany = sequelize.define(
    'ConcreteRequestCompany',
    {
      ConcreteRequestId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      ConcreteRequestCode: DataTypes.INTEGER,
      CompanyId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ProjectId: {
        type: DataTypes.INTEGER,
      },
    },
    {},
  );
  ConcreteRequestCompany.associate = (models) => {
    ConcreteRequestCompany.belongsTo(models.ConcreteRequest, {
      as: 'concreteRequest',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequestCompany.belongsTo(models.Company);
  };
  ConcreteRequestCompany.getAll = async (attr) => {
    const getConcreteCompanies = await ConcreteRequestCompany.findAll({
      where: { ...attr },
      order: [['id', 'DESC']],
    });
    return getConcreteCompanies;
  };
  ConcreteRequestCompany.createInstance = async (paramData) => {
    const newConcreteCompany = await ConcreteRequestCompany.create(paramData);
    return newConcreteCompany;
  };
  return ConcreteRequestCompany;
};
