module.exports = (sequelize, DataTypes) => {
  const InspectionAttachement = sequelize.define(
    'InspectionAttachement',
    {
      ProjectId: DataTypes.INTEGER,
      MemberId: DataTypes.INTEGER,
      InspectionRequestId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      attachement: DataTypes.STRING,
      filename: DataTypes.STRING,
      extension: DataTypes.STRING,
    },
    {},
  );

  InspectionAttachement.associate = (models) => {
    // associations can be defined here
    InspectionAttachement.belongsTo(models.InspectionRequest);
  };

  InspectionAttachement.getAll = async (attr) => {
    const newInspectionAttachement = await InspectionAttachement.findAll({
      where: { ...attr },
      order: [['id', 'DESC']],
    });
    return newInspectionAttachement;
  };
  InspectionAttachement.createMultipleInstance = async (paramData) => {
    const newInspectionAttachement = await InspectionAttachement.bulkCreate(paramData);
    return newInspectionAttachement;
  };
  InspectionAttachement.createInstance = async (paramData) => {
    const newInspectionAttachement = await InspectionAttachement.create(paramData);
    return newInspectionAttachement;
  };
  return InspectionAttachement;
};
