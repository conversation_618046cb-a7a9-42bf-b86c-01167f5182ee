const status = require('http-status');
const wasteLogService = require('../services/wasteLogService')

const WasteLogController = {
    async addWasteLog(req, res, next) {
        wasteLogService.addWasteLog(req, async (wasteLog, error) => {
            if (error) {
                console.log(error, "error======")
                next(error);
            } else {
                res.status(status.CREATED).json({
                    message: 'WasteLog added successfully.',
                    data: wasteLog,
                });
            }
        });
    },
    async listWasteLog(req, res, next) {
        wasteLogService.listWasteLog(req, async (wasteLogDetail, error) => {
            if (error) {
                next(error);
            } else {
                res.status(status.OK).json({
                    message: 'Waste Log Listed successfully.',
                    data: wasteLogDetail,
                });
            }
        });
    }
};
module.exports = WasteLogController;
