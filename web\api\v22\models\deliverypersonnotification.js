const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class DeliveryPersonNotification extends Model {
    static associate(models) {
      // define association here
      DeliveryPersonNotification.belongsTo(models.Notification);
      DeliveryPersonNotification.belongsTo(models.Member);
      DeliveryPersonNotification.belongsTo(models.Project);
      DeliveryPersonNotification.belongsTo(models.User);
    }
  }
  DeliveryPersonNotification.init(
    {
      MemberId: DataTypes.INTEGER,
      NotificationId: DataTypes.INTEGER,
      ProjectId: DataTypes.INTEGER,
      UserId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      seen: DataTypes.BOOLEAN,
      isLocationFollowNotification: DataTypes.BOOLEAN,
    },
    {
      sequelize,
      modelName: 'DeliveryPersonNotification',
    },
  );
  DeliveryPersonNotification.getUnSeenCount = async (attr, inputData) => {
    const notification = await DeliveryPersonNotification.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          association: 'Member',
          attributes: ['id'],
          where: { UserId: inputData.id, isDeleted: false, isActive: true },
          required: true,
        },
        {
          association: 'Notification',
          attributes: ['MemberId'],
        },
      ],
      where: { ...attr },
    });
    return notification;
  };
  return DeliveryPersonNotification;
};
