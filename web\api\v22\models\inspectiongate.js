module.exports = (sequelize, DataTypes) => {
  const InspectionGate = sequelize.define(
    'InspectionGate',
    {
      InspectionId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      InspectionCode: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      GateId: DataTypes.INTEGER,
      ProjectId: DataTypes.INTEGER,
      isActive: DataTypes.BOOLEAN,
    },
    {},
  );
  InspectionGate.associate = (models) => {
    // associations can be defined here
    InspectionGate.belongsTo(models.InspectionRequest, {
      as: 'inspectionrequest',
      foreignKey: 'InspectionId',
    });
    InspectionGate.belongsTo(models.Gates, {
      as: 'Gates',
      foreignKey: 'GateId',
    });
    InspectionGate.belongsTo(models.Gates);
  };
  InspectionGate.createInstance = async (paramData) => {
    const newInspectionGate = await InspectionGate.create(paramData);
    return newInspectionGate;
  };
  return InspectionGate;
};
